#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
保守精准的红球杀号算法
基于失败案例分析，采用更保守的策略
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict

class ConservativeRedKiller:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        
        # 危险号码：经常被错误杀掉的号码
        self.dangerous_numbers = [18, 26, 1, 2, 14, 27, 10, 3, 33, 19]
        
    def calculate_conservative_red_kills(self, recent_periods: List[List[int]], target_count: int = 8) -> List[int]:
        """
        保守的红球杀号策略
        
        核心原则：
        1. 减少杀号数量：从13个减少到8个
        2. 只杀真正确定的冷号
        3. 避免杀掉危险号码
        4. 宁可少杀，不可误杀
        """
        if len(recent_periods) < 2:
            return list(range(1, min(target_count + 1, 9)))
        
        print(f"🛡️  保守杀号策略 (目标{target_count}个):")
        
        # 策略1: 获取绝对冷号 (长期未出现)
        absolute_cold = self._get_absolute_cold_numbers()
        print(f"  绝对冷号: {absolute_cold}")
        
        # 策略2: 获取近期冷号 (最近10期未出现)
        recent_cold = self._get_recent_cold_numbers()
        print(f"  近期冷号: {recent_cold}")
        
        # 策略3: 避开危险号码和近期活跃号码
        recent_active = self._get_recent_active_numbers(recent_periods)
        print(f"  近期活跃: {sorted(recent_active)}")
        print(f"  危险号码: {self.dangerous_numbers}")
        
        # 生成候选杀号
        kill_candidates = []
        
        # 优先级1: 绝对冷号且不在危险列表中
        for num in absolute_cold:
            if num not in self.dangerous_numbers and num not in recent_active:
                kill_candidates.append(num)
        
        # 优先级2: 近期冷号且不在危险列表中
        for num in recent_cold:
            if (num not in self.dangerous_numbers and 
                num not in recent_active and 
                num not in kill_candidates):
                kill_candidates.append(num)
        
        # 优先级3: 其他相对安全的号码
        if len(kill_candidates) < target_count:
            safe_numbers = self._get_safe_numbers(recent_active)
            for num in safe_numbers:
                if num not in kill_candidates:
                    kill_candidates.append(num)
                    if len(kill_candidates) >= target_count:
                        break
        
        final_kills = kill_candidates[:target_count]
        
        print(f"  最终杀号: {sorted(final_kills)} (共{len(final_kills)}个)")
        
        return final_kills
    
    def _get_absolute_cold_numbers(self) -> List[int]:
        """获取绝对冷号 (最近20期未出现)"""
        try:
            appeared_numbers = set()
            for i, row in self.data.head(20).iterrows():
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(row)
                appeared_numbers.update(red_balls)
            
            absolute_cold = [num for num in range(1, 36) if num not in appeared_numbers]
            return absolute_cold
        except:
            return []
    
    def _get_recent_cold_numbers(self) -> List[int]:
        """获取近期冷号 (最近10期未出现)"""
        try:
            appeared_numbers = set()
            for i, row in self.data.head(10).iterrows():
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(row)
                appeared_numbers.update(red_balls)
            
            recent_cold = [num for num in range(1, 36) if num not in appeared_numbers]
            return recent_cold
        except:
            return []
    
    def _get_recent_active_numbers(self, recent_periods: List[List[int]]) -> Set[int]:
        """获取近期活跃号码 (前2期出现的号码)"""
        active_numbers = set()
        for period in recent_periods[:2]:  # 只看前2期
            active_numbers.update(period)
        return active_numbers
    
    def _get_safe_numbers(self, recent_active: Set[int]) -> List[int]:
        """获取相对安全的号码"""
        # 选择一些历史频率较低且不在活跃列表中的号码
        safe_candidates = []
        
        # 一些经验上相对冷的号码区间
        cold_ranges = [
            list(range(30, 36)),  # 30-35
            list(range(23, 30)),  # 23-29
            [16, 17, 4, 5]        # 一些中等号码
        ]
        
        for range_nums in cold_ranges:
            for num in range_nums:
                if (num not in recent_active and 
                    num not in self.dangerous_numbers and
                    num not in safe_candidates):
                    safe_candidates.append(num)
        
        return safe_candidates
    
    def test_against_historical_failures(self):
        """测试对历史失败案例的改进效果"""
        print("\n🧪 测试对历史失败案例的改进效果:")
        
        test_cases = [
            {
                'name': '25068→25069',
                'recent_periods': [[6, 10, 12, 21, 22], [15, 18, 27, 28, 34]],
                'actual_result': [1, 4, 17, 20, 22],
                'old_kills': [1, 26, 14, 13, 4, 33, 19, 24, 35, 2, 3, 17, 5],
                'old_wrong': [1, 4, 17]
            },
            {
                'name': '25067→25068', 
                'recent_periods': [[15, 18, 27, 28, 34], [7, 25, 32, 33, 35]],
                'actual_result': [6, 10, 12, 21, 22],
                'old_kills': [14, 26, 13, 19, 1, 4, 24, 33, 35, 2, 17, 6, 8],
                'old_wrong': [6]
            }
        ]
        
        for case in test_cases:
            print(f"\n  {case['name']}:")
            print(f"    实际开奖: {case['actual_result']}")
            print(f"    原算法杀号: {case['old_kills']} (误杀{len(case['old_wrong'])}个)")
            
            # 测试保守算法
            new_kills = self.calculate_conservative_red_kills(case['recent_periods'], 8)
            new_wrong = [num for num in new_kills if num in case['actual_result']]
            
            print(f"    保守算法杀号: {new_kills} (误杀{len(new_wrong)}个)")
            
            if len(new_wrong) < len(case['old_wrong']):
                print(f"    ✅ 改进效果: 误杀从{len(case['old_wrong'])}个减少到{len(new_wrong)}个")
            elif len(new_wrong) == 0:
                print(f"    🎯 完美: 完全避免了误杀")
            else:
                print(f"    ⚠️  仍需改进: 误杀{len(new_wrong)}个")

def test_conservative_killer():
    """测试保守杀号算法"""
    print("🛡️  测试保守精准的红球杀号算法")
    print("=" * 60)
    
    # 加载数据
    try:
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 初始化保守算法
        conservative_killer = ConservativeRedKiller(data)
        
        # 测试对历史失败案例的改进
        conservative_killer.test_against_historical_failures()
        
        # 获取最新数据进行测试
        recent_periods = []
        for i in range(4):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(data.iloc[i])
                recent_periods.append(red_balls)
        
        print(f"\n📊 使用最新数据测试:")
        print(f"最近期数: {[sorted(p) for p in recent_periods[:2]]}")
        
        conservative_kills = conservative_killer.calculate_conservative_red_kills(recent_periods, 8)
        
        print(f"\n🎯 保守算法总结:")
        print(f"  杀号数量: {len(conservative_kills)}个 (从13个减少到8个)")
        print(f"  杀号列表: {sorted(conservative_kills)}")
        
        if conservative_kills:
            odd_count = sum(1 for num in conservative_kills if num % 2 == 1)
            small_count = sum(1 for num in conservative_kills if num <= 17)
            print(f"  特征分析: 奇数{odd_count}个, 偶数{len(conservative_kills)-odd_count}个")
            print(f"  大小分布: 小数{small_count}个, 大数{len(conservative_kills)-small_count}个")
            print(f"  号码范围: {min(conservative_kills)}-{max(conservative_kills)}")
        
        print(f"\n✅ 保守算法测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_conservative_killer()
