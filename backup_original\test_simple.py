"""
简化测试程序，用于调试
"""

import pandas as pd
from utils import load_data, parse_numbers

def test_basic_functionality():
    """测试基本功能"""
    print("开始测试基本功能...")
    
    try:
        # 测试数据加载
        print("1. 测试数据加载...")
        data = load_data()
        print(f"数据加载成功，共{len(data)}行")
        print(f"列名: {list(data.columns)}")
        print(f"前3行数据:")
        print(data.head(3))
        
        # 测试数据解析
        print("\n2. 测试数据解析...")
        for i in range(3):
            row = data.iloc[i]
            red_balls, blue_balls = parse_numbers(row)
            print(f"第{i+1}行: 红球{red_balls}, 蓝球{blue_balls}")
        
        print("\n基本功能测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_functionality()
