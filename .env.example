# 大乐透预测系统环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# 基础配置
DEBUG=false
LOG_LEVEL=INFO

# 数据配置
DATA_FILE=data/raw/dlt_data.csv
PROCESSED_DATA_DIR=data/processed
EXTERNAL_DATA_DIR=data/external

# 模型配置
MARKOV_ORDER=2
NEURAL_EPOCHS=100
NEURAL_LEARNING_RATE=0.001

# 回测配置
BACKTEST_PERIODS=50
DISPLAY_PERIODS=10

# 日志配置
LOG_FILE=logs/lottery_predictor.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# 缓存配置（可选）
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# 数据库配置（可选）
DATABASE_URL=sqlite:///lottery_predictor.db

# 外部API配置（可选）
EXTERNAL_API_KEY=your_api_key_here
EXTERNAL_API_URL=https://api.example.com

# 性能配置
MAX_WORKERS=4
MEMORY_LIMIT=2048  # MB

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1

# 开发配置
DEVELOPMENT_MODE=false
PROFILING_ENABLED=false
