"""
基础类和接口定义
定义系统中所有组件的基础接口和抽象类
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Tuple, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import pandas as pd

from config.logging_config import LoggerMixin


class BallType(Enum):
    """球类型枚举"""
    RED = "red"
    BLUE = "blue"


class FeatureType(Enum):
    """特征类型枚举"""
    ODD_EVEN = "odd_even"
    SIZE = "size"
    SUM_RANGE = "sum_range"
    SPAN = "span"
    CONSECUTIVE = "consecutive"
    AC_VALUE = "ac_value"
    ZONE_DISTRIBUTION = "zone_distribution"


@dataclass
class PredictionResult:
    """预测结果数据类"""
    feature_type: FeatureType
    ball_type: BallType
    predicted_state: str
    confidence: float
    probability_distribution: Dict[str, float]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class GenerationResult:
    """号码生成结果数据类"""
    red_balls: List[int]
    blue_balls: List[int]
    generation_method: str
    confidence: float
    kill_numbers: Dict[str, List[List[int]]]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def formatted_numbers(self) -> Tuple[str, str]:
        """格式化的号码字符串"""
        red_str = ','.join([f"{num:02d}" for num in sorted(self.red_balls)])
        blue_str = ','.join([f"{num:02d}" for num in sorted(self.blue_balls)])
        return red_str, blue_str


@dataclass
class AnalysisResult:
    """分析结果数据类"""
    feature_sequences: Dict[str, List[str]]
    state_frequencies: Dict[str, Dict[str, float]]
    trend_analysis: Dict[str, Any]
    statistical_summary: Dict[str, Any]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseAnalyzer(ABC, LoggerMixin):
    """数据分析器基类"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化分析器
        
        Args:
            data: 历史开奖数据
        """
        self.data = data
        self.logger.info(f"初始化分析器，数据量: {len(data)} 期")
    
    @abstractmethod
    def extract_features(self) -> AnalysisResult:
        """
        提取特征
        
        Returns:
            AnalysisResult: 分析结果
        """
        pass
    
    @abstractmethod
    def get_feature_sequence(self, feature_type: str) -> List[str]:
        """
        获取特征序列
        
        Args:
            feature_type: 特征类型
            
        Returns:
            List[str]: 特征序列
        """
        pass
    
    @abstractmethod
    def calculate_state_frequencies(self, feature_type: str) -> Dict[str, float]:
        """
        计算状态频率
        
        Args:
            feature_type: 特征类型
            
        Returns:
            Dict[str, float]: 状态频率字典
        """
        pass


class BasePredictor(ABC, LoggerMixin):
    """预测器基类"""
    
    def __init__(self, name: str):
        """
        初始化预测器
        
        Args:
            name: 预测器名称
        """
        self.name = name
        self.is_trained = False
        self.logger.info(f"初始化预测器: {name}")
    
    @abstractmethod
    def train(self, analyzer: BaseAnalyzer) -> None:
        """
        训练预测器
        
        Args:
            analyzer: 数据分析器
        """
        pass
    
    @abstractmethod
    def predict(
        self, 
        feature_type: FeatureType,
        ball_type: BallType,
        current_state: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> PredictionResult:
        """
        进行预测
        
        Args:
            feature_type: 特征类型
            ball_type: 球类型
            current_state: 当前状态
            context: 上下文信息
            
        Returns:
            PredictionResult: 预测结果
        """
        pass
    
    def validate_training(self) -> bool:
        """验证训练状态"""
        if not self.is_trained:
            self.logger.warning(f"预测器 {self.name} 尚未训练")
            return False
        return True


class BaseGenerator(ABC, LoggerMixin):
    """号码生成器基类"""
    
    def __init__(self, name: str):
        """
        初始化生成器
        
        Args:
            name: 生成器名称
        """
        self.name = name
        self.logger.info(f"初始化生成器: {name}")
    
    @abstractmethod
    def generate(
        self,
        predictions: Dict[FeatureType, PredictionResult],
        kill_numbers: Dict[str, List[List[int]]],
        context: Optional[Dict[str, Any]] = None
    ) -> GenerationResult:
        """
        生成号码
        
        Args:
            predictions: 预测结果字典
            kill_numbers: 杀号字典
            context: 上下文信息
            
        Returns:
            GenerationResult: 生成结果
        """
        pass
    
    def validate_numbers(self, red_balls: List[int], blue_balls: List[int]) -> bool:
        """
        验证号码有效性
        
        Args:
            red_balls: 红球号码
            blue_balls: 蓝球号码
            
        Returns:
            bool: 是否有效
        """
        # 基础验证 - 大乐透格式：5个红球 + 2个蓝球
        if len(red_balls) != 5 or len(blue_balls) != 2:
            return False

        if not all(1 <= num <= 35 for num in red_balls):
            return False

        if not all(1 <= num <= 12 for num in blue_balls):
            return False

        if len(set(red_balls)) != 5 or len(set(blue_balls)) != 2:
            return False
        
        return True


class BaseSystem(ABC, LoggerMixin):
    """预测系统基类"""
    
    def __init__(self, name: str, version: str = "1.0"):
        """
        初始化系统
        
        Args:
            name: 系统名称
            version: 版本号
        """
        self.name = name
        self.version = version
        self.is_initialized = False
        self.logger.info(f"初始化系统: {name} v{version}")
    
    @abstractmethod
    def initialize(self) -> None:
        """初始化系统组件"""
        pass
    
    @abstractmethod
    def predict_next_period(self, current_period_index: int) -> Dict[str, Any]:
        """
        预测下一期
        
        Args:
            current_period_index: 当前期次索引
            
        Returns:
            Dict[str, Any]: 预测结果
        """
        pass
    
    @abstractmethod
    def run_backtest(self, num_periods: int = 50, display_periods: int = 10) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            num_periods: 回测期数
            display_periods: 显示期数
            
        Returns:
            Dict[str, Any]: 回测结果
        """
        pass
