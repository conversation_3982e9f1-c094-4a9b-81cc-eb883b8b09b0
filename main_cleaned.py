"""
大乐透预测系统主程序 - 清理版本
移除冗余代码，优化导入，确保调用优化后的杀号算法
"""

import pandas as pd
import sys
import os
import traceback
from typing import Dict, List, Tuple
from pathlib import Path
from collections import Counter, defaultdict

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 核心导入 - 统一在顶部
from src.utils.utils import (
    load_data, parse_numbers, calculate_odd_even_ratio,
    calculate_size_ratio_red, calculate_size_ratio_blue,
    ratio_to_state, format_numbers, check_hit_2_plus_1
)
from src.core.analyzer import LotteryAnalyzer
from src.models.markov.markov_model import MarkovModel
from src.models.bayes.bayes_selector import BayesSelector
from src.utils.killer import NumberKiller
from src.utils.universal_killer import UniversalKiller
from src.generators.generator import NumberGenerator
from src.generators.advanced_generator import AdvancedNumberGenerator
from src.models.ensemble.ensemble_predictor import EnsemblePredictor
from src.models.improved_predictor import ImprovedPredictor
from src.generators.insight_based_generator import InsightBasedGenerator
from src.generators.diversified_generator import DiversifiedGenerator
from src.generators.precision_generator import PrecisionGenerator
from src.generators.dynamic_generator import DynamicGenerator

# 优化后的杀号系统导入
try:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from advanced_probabilistic_system import AdvancedProbabilisticSystem
    ADVANCED_KILL_AVAILABLE = True
except ImportError:
    print("⚠️ 优化杀号系统不可用，将使用回退方案")
    ADVANCED_KILL_AVAILABLE = False


class LotteryPredictor:
    """大乐透预测系统 - 清理优化版本"""
    
    def __init__(self, data_file: str = 'dlt_data.csv'):
        """初始化预测系统"""
        self.data = load_data(data_file)
        self.analyzer = LotteryAnalyzer(self.data)
        
        # 核心组件
        self.killer = NumberKiller()
        self.universal_killer = UniversalKiller()
        self.generator = NumberGenerator()
        self.advanced_generator = AdvancedNumberGenerator()
        
        # 集成预测器
        self.red_ensemble = EnsemblePredictor('red')
        self.blue_ensemble = EnsemblePredictor('blue')
        self.improved_predictor = ImprovedPredictor()
        
        # 号码生成器
        self.insight_generator = InsightBasedGenerator()
        self.diversified_generator = DiversifiedGenerator()
        self.precision_generator = PrecisionGenerator()
        self.dynamic_generator = DynamicGenerator()
        
        # 增强模型
        self.red_odd_even_markov = MarkovModel('red', order=2)
        self.red_size_markov = MarkovModel('red', order=2)
        self.blue_size_markov = MarkovModel('blue', order=2)
        
        self.red_odd_even_bayes = BayesSelector('red')
        self.red_size_bayes = BayesSelector('red')
        self.blue_size_bayes = BayesSelector('blue')
    
    def train_models(self, train_data: pd.DataFrame) -> None:
        """训练预测模型"""
        train_analyzer = LotteryAnalyzer(train_data)
        
        # 训练马尔科夫模型
        self.red_odd_even_markov.train(train_analyzer.get_feature_sequence('red_odd_even'))
        self.red_size_markov.train(train_analyzer.get_feature_sequence('red_size'))
        self.blue_size_markov.train(train_analyzer.get_feature_sequence('blue_size'))

        # 训练集成预测器
        self.red_ensemble.train_ensemble(train_analyzer)
        self.blue_ensemble.train_ensemble(train_analyzer)
        
        # 设置贝叶斯先验概率
        historical_red_odd_even = train_analyzer.calculate_state_frequencies('red_odd_even')
        recent_red_odd_even = train_analyzer.analyze_state_trends('red_odd_even', window=20)
        historical_red_size = train_analyzer.calculate_state_frequencies('red_size')
        recent_red_size = train_analyzer.analyze_state_trends('red_size', window=20)
        historical_blue_size = train_analyzer.calculate_state_frequencies('blue_size')
        recent_blue_size = train_analyzer.analyze_state_trends('blue_size', window=20)

        self.red_odd_even_bayes.set_prior_probabilities(
            historical_red_odd_even, recent_red_odd_even, recent_weight=0.4
        )
        self.red_size_bayes.set_prior_probabilities(
            historical_red_size, recent_red_size, recent_weight=0.4
        )
        self.blue_size_bayes.set_prior_probabilities(
            historical_blue_size, recent_blue_size, recent_weight=0.4
        )
    
    def _universal_kill_prediction(self, train_data: pd.DataFrame) -> Dict[str, List]:
        """
        使用优化后的高级概率系统进行杀号预测
        这是主要的杀号调用函数，确保使用最新的优化算法
        """
        if not ADVANCED_KILL_AVAILABLE:
            return self._fallback_kill_prediction(train_data)
        
        try:
            # 构建period_data
            period_data = {
                'current': train_data.iloc[0],
                'last': train_data.iloc[1],
                'prev2': train_data.iloc[2],
                'prev3': train_data.iloc[3],
                'prev4': train_data.iloc[4],
                'prev5': train_data.iloc[5]
            }

            # 初始化优化后的高级概率系统
            advanced_system = AdvancedProbabilisticSystem()
            advanced_system.data = train_data
            advanced_system.initialize_system()

            # 使用集成系统预测杀号（目标5个，提高精准度）
            red_kills_full = advanced_system.ensemble_system.predict_ensemble_kills(period_data, target_count=5)

            # 过滤掉前两期出现的号码
            period1_red, _ = parse_numbers(period_data['current'])
            period2_red, _ = parse_numbers(period_data['last'])
            red_universal_kills = [k for k in red_kills_full if k not in (period1_red + period2_red) and 1 <= k <= 35]

            # 蓝球杀号：使用高级蓝球杀号系统
            blue_universal_kills = advanced_system.predict_blue_kills(period_data, target_count=1)

            print(f"🎯 使用优化高级概率系统:")
            print(f"  红球杀号: {red_universal_kills} (共{len(red_universal_kills)}个)")
            print(f"  蓝球杀号: {blue_universal_kills} (共{len(blue_universal_kills)}个)")

            return {
                'red_universal': red_universal_kills,
                'blue_universal': blue_universal_kills
            }

        except Exception as e:
            print(f"⚠️ 优化高级概率系统调用失败: {e}")
            return self._fallback_kill_prediction(train_data)
    
    def _predict_blue_kills_simple(self, period_data: Dict) -> List[int]:
        """简化的蓝球杀号预测"""
        try:
            # 获取最近3期蓝球数据
            recent_blues = []
            for key in ['current', 'last', 'prev2']:
                if key in period_data:
                    _, blue_balls = parse_numbers(period_data[key])
                    recent_blues.extend(blue_balls)

            # 统计频率，选择最少出现的号码作为杀号
            blue_freq = Counter(recent_blues)
            all_blues = list(range(1, 13))
            min_freq = min(blue_freq.get(b, 0) for b in all_blues)
            candidates = [b for b in all_blues if blue_freq.get(b, 0) == min_freq]

            # 返回1个杀号
            return [candidates[0]] if candidates else [12]

        except Exception as e:
            print(f"⚠️ 蓝球杀号预测失败: {e}")
            return [9]
    
    def _fallback_kill_prediction(self, train_data: pd.DataFrame) -> Dict[str, List]:
        """回退杀号预测方法"""
        try:
            from bayesian_markov_killer import BayesianMarkovKiller

            if len(train_data) < 2:
                return {'red_universal': [], 'blue_universal': []}

            # 获取最近6期数据用于杀号预测
            recent_red_periods = []
            recent_blue_periods = []
            for i in range(min(6, len(train_data))):
                red_balls, blue_balls = parse_numbers(train_data.iloc[i])
                recent_red_periods.append(red_balls)
                recent_blue_periods.append(blue_balls)

            # 初始化贝叶斯+马尔科夫链杀号系统
            killer = BayesianMarkovKiller(train_data)

            # 红球杀号：使用保守精准算法生成5个杀号
            red_universal_kills = killer.calculate_red_kills(recent_red_periods, target_count=5)

            # 蓝球杀号：使用优化算法生成1个杀号
            blue_universal_kills = killer.calculate_blue_kills(recent_blue_periods, target_count=1)

            print(f"🔄 使用回退杀号系统:")
            print(f"  红球杀号: {red_universal_kills} (共{len(red_universal_kills)}个)")
            print(f"  蓝球杀号: {blue_universal_kills} (共{len(blue_universal_kills)}个)")

            return {
                'red_universal': red_universal_kills,
                'blue_universal': blue_universal_kills
            }

        except Exception as e:
            print(f"❌ 回退杀号系统也失败: {e}")
            return {'red_universal': [], 'blue_universal': []}

    def predict_kill_numbers_by_period(self, period_number: str) -> Dict[str, List]:
        """根据期号直接调用advanced_probabilistic_system.py计算杀号"""
        try:
            # 找到指定期号在数据中的位置
            period_index = None
            for i, row in self.data.iterrows():
                if str(row['期号']) == str(period_number):
                    period_index = i
                    break

            if period_index is None:
                raise ValueError(f"未找到期号 {period_number}")

            # 获取该期号之后的数据作为训练数据
            train_data = self.data.iloc[period_index + 1:period_index + 301]

            if len(train_data) < 10:
                raise ValueError(f"期号 {period_number} 之后的训练数据不足")

            # 使用优化后的杀号系统
            return self._universal_kill_prediction(train_data)

        except Exception as e:
            print(f"⚠️ 期号 {period_number} 杀号预测失败: {e}")
            return {'red_universal': [], 'blue_universal': []}

    def run_backtest(self, num_periods: int = 10, display_periods: int = 10):
        """运行回测 - 简化版本，专注于杀号测试"""
        print("🎯 开始杀号系统回测...")
        print("=" * 60)

        results = []
        for i in range(min(num_periods, len(self.data) - 200)):
            try:
                # 获取训练数据
                train_data = self.data.iloc[i + 1:i + 201]

                # 预测杀号
                kill_result = self._universal_kill_prediction(train_data)

                # 获取实际结果
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)

                # 检查杀号成功率
                red_kills = kill_result.get('red_universal', [])
                blue_kills = kill_result.get('blue_universal', [])

                red_success = all(k not in actual_red for k in red_kills) if red_kills else True
                blue_success = all(k not in actual_blue for k in blue_kills) if blue_kills else True

                results.append({
                    'period': actual_row['期号'],
                    'red_kills': red_kills,
                    'blue_kills': blue_kills,
                    'red_success': red_success,
                    'blue_success': blue_success,
                    'actual_red': actual_red,
                    'actual_blue': actual_blue
                })

                print(f"期号 {actual_row['期号']}: 红球杀号{'✅' if red_success else '❌'} 蓝球杀号{'✅' if blue_success else '❌'}")

            except Exception as e:
                print(f"期号处理失败: {e}")
                continue

        # 统计结果
        if results:
            red_success_rate = sum(r['red_success'] for r in results) / len(results)
            blue_success_rate = sum(r['blue_success'] for r in results) / len(results)

            print(f"\n📊 回测结果:")
            print(f"  红球杀号成功率: {red_success_rate:.1%}")
            print(f"  蓝球杀号成功率: {blue_success_rate:.1%}")
            print(f"  总测试期数: {len(results)}")


def main():
    """主函数"""
    try:
        predictor = LotteryPredictor()
        predictor.run_backtest(num_periods=10, display_periods=10)
    except Exception as e:
        print(f"程序运行出错: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
