"""
平衡动态生成器
在多样性和精准度之间找到最佳平衡点
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio, load_data
)


class BalancedDynamicGenerator:
    """平衡动态生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        self.data = load_data()
        
        # 平衡策略权重（根据历史表现动态调整）
        self.strategy_weights = {
            'precision_focused': 0.4,    # 精准导向策略
            'diversity_focused': 0.3,    # 多样性导向策略
            'balanced_hybrid': 0.2,      # 混合平衡策略
            'adaptive_smart': 0.1        # 自适应智能策略
        }
        
        # 性能跟踪
        self.strategy_performance = {
            'precision_focused': {'hits': 0, 'attempts': 0},
            'diversity_focused': {'hits': 0, 'attempts': 0},
            'balanced_hybrid': {'hits': 0, 'attempts': 0},
            'adaptive_smart': {'hits': 0, 'attempts': 0}
        }
        
        # 号码池管理
        self.number_pools = {}
        self._initialize_number_pools()
    
    def _initialize_number_pools(self):
        """初始化号码池"""
        if len(self.data) == 0:
            return
        
        # 分析不同时间窗口的号码频率
        self._analyze_frequency_pools()
        self._analyze_pattern_pools()
        self._analyze_performance_pools()
    
    def _analyze_frequency_pools(self):
        """分析频率池"""
        # 短期频率（最近10期）
        recent_data = self.data.head(10)
        red_freq_short = Counter()
        blue_freq_short = Counter()
        
        for _, row in recent_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq_short.update(red_balls)
            blue_freq_short.update(blue_balls)
        
        # 中期频率（最近30期）
        medium_data = self.data.head(30)
        red_freq_medium = Counter()
        blue_freq_medium = Counter()
        
        for _, row in medium_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq_medium.update(red_balls)
            blue_freq_medium.update(blue_balls)
        
        # 长期频率（最近100期）
        long_data = self.data.head(100) if len(self.data) >= 100 else self.data
        red_freq_long = Counter()
        blue_freq_long = Counter()
        
        for _, row in long_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq_long.update(red_balls)
            blue_freq_long.update(blue_balls)
        
        # 构建频率池
        self.number_pools['red_hot_short'] = [num for num, _ in red_freq_short.most_common(10)]
        self.number_pools['red_hot_medium'] = [num for num, _ in red_freq_medium.most_common(12)]
        self.number_pools['red_hot_long'] = [num for num, _ in red_freq_long.most_common(15)]
        
        self.number_pools['blue_hot_short'] = [num for num, _ in blue_freq_short.most_common(4)]
        self.number_pools['blue_hot_medium'] = [num for num, _ in blue_freq_medium.most_common(5)]
        self.number_pools['blue_hot_long'] = [num for num, _ in blue_freq_long.most_common(6)]
        
        # 冷号池
        all_red_freq = {num: red_freq_long.get(num, 0) for num in self.red_range}
        all_blue_freq = {num: blue_freq_long.get(num, 0) for num in self.blue_range}
        
        self.number_pools['red_cold'] = sorted(all_red_freq.keys(), key=lambda x: all_red_freq[x])[:10]
        self.number_pools['blue_cold'] = sorted(all_blue_freq.keys(), key=lambda x: all_blue_freq[x])[:4]
    
    def _analyze_pattern_pools(self):
        """分析模式池"""
        # 分析最近开奖的模式特征
        if len(self.data) < 5:
            return
        
        recent_patterns = []
        for i in range(5):
            row = self.data.iloc[i]
            red_balls, blue_balls = parse_numbers(row)
            
            # 分析红球模式
            red_sum = sum(red_balls)
            red_span = max(red_balls) - min(red_balls)
            red_odd_count = sum(1 for x in red_balls if x % 2 == 1)
            red_small_count = sum(1 for x in red_balls if x <= 18)
            
            recent_patterns.append({
                'red_sum': red_sum,
                'red_span': red_span,
                'red_odd_count': red_odd_count,
                'red_small_count': red_small_count,
                'blue_sum': sum(blue_balls),
                'blue_gap': abs(blue_balls[1] - blue_balls[0]) if len(blue_balls) == 2 else 0
            })
        
        # 计算模式趋势
        avg_red_sum = np.mean([p['red_sum'] for p in recent_patterns])
        avg_red_span = np.mean([p['red_span'] for p in recent_patterns])
        
        # 基于趋势构建模式池
        if avg_red_sum > 100:
            self.number_pools['red_pattern'] = [num for num in self.red_range if num >= 15]
        else:
            self.number_pools['red_pattern'] = [num for num in self.red_range if num <= 20]
        
        if avg_red_span > 25:
            self.number_pools['red_span_pattern'] = list(range(1, 11)) + list(range(25, 36))
        else:
            self.number_pools['red_span_pattern'] = list(range(10, 26))
    
    def _analyze_performance_pools(self):
        """分析性能池（基于历史命中表现）"""
        # 这里可以基于历史预测的命中情况来构建高性能号码池
        # 简化实现：使用综合评分
        
        red_scores = {}
        blue_scores = {}
        
        # 综合评分：频率 + 最近出现 + 模式匹配
        for num in self.red_range:
            score = 0
            
            # 频率评分
            if num in self.number_pools.get('red_hot_medium', []):
                score += 3
            elif num in self.number_pools.get('red_hot_long', []):
                score += 2
            
            # 最近出现评分
            if num in self.number_pools.get('red_hot_short', []):
                score += 2
            
            # 模式评分
            if num in self.number_pools.get('red_pattern', []):
                score += 1
            
            red_scores[num] = score
        
        for num in self.blue_range:
            score = 0
            
            if num in self.number_pools.get('blue_hot_medium', []):
                score += 3
            elif num in self.number_pools.get('blue_hot_long', []):
                score += 2
            
            if num in self.number_pools.get('blue_hot_short', []):
                score += 2
            
            blue_scores[num] = score
        
        # 构建性能池
        self.number_pools['red_performance'] = sorted(red_scores.keys(), key=lambda x: red_scores[x], reverse=True)[:12]
        self.number_pools['blue_performance'] = sorted(blue_scores.keys(), key=lambda x: blue_scores[x], reverse=True)[:5]
    
    def generate_balanced_numbers(self, 
                                red_odd_even_state: str,
                                red_size_state: str,
                                blue_size_state: str,
                                kill_numbers: Dict[str, List[List[int]]] = None,
                                seed: int = 0,
                                current_period_index: int = 0) -> Tuple[List[int], List[int]]:
        """
        生成平衡的号码组合
        
        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            kill_numbers: 杀号列表
            seed: 随机种子
            current_period_index: 当前期次索引
            
        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        # 动态调整策略权重
        self._update_strategy_weights()
        
        # 生成多个候选组合
        candidates = []
        
        # 策略1: 精准导向
        precision_red, precision_blue = self._generate_precision_focused(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed
        )
        candidates.append((precision_red, precision_blue, 'precision_focused'))
        
        # 策略2: 多样性导向
        diversity_red, diversity_blue = self._generate_diversity_focused(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed + 1
        )
        candidates.append((diversity_red, diversity_blue, 'diversity_focused'))
        
        # 策略3: 混合平衡
        hybrid_red, hybrid_blue = self._generate_balanced_hybrid(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed + 2
        )
        candidates.append((hybrid_red, hybrid_blue, 'balanced_hybrid'))
        
        # 策略4: 自适应智能
        smart_red, smart_blue = self._generate_adaptive_smart(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed + 3, current_period_index
        )
        candidates.append((smart_red, smart_blue, 'adaptive_smart'))
        
        # 根据权重选择策略
        np.random.seed(seed + current_period_index)
        weights = [self.strategy_weights[name] for _, _, name in candidates]
        chosen_idx = np.random.choice(len(candidates), p=weights)
        chosen_red, chosen_blue, strategy = candidates[chosen_idx]
        
        # 记录策略使用
        self.strategy_performance[strategy]['attempts'] += 1
        
        print(f"  平衡策略: {strategy} (权重: {weights[chosen_idx]:.3f})")
        
        return chosen_red, chosen_blue
    
    def _generate_precision_focused(self, odd_even_state: str, size_state: str, 
                                  blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """精准导向策略：优先选择高性能号码"""
        np.random.seed(seed)
        
        # 红球：80%性能池 + 20%热号池
        red_candidates = []
        
        # 性能池
        performance_pool = self._apply_kill_filter(self.number_pools.get('red_performance', []), kill_numbers, 'red')
        red_candidates.extend(performance_pool[:4])
        
        # 短期热号池
        hot_pool = self._apply_kill_filter(self.number_pools.get('red_hot_short', []), kill_numbers, 'red')
        red_candidates.extend([n for n in hot_pool[:2] if n not in red_candidates])
        
        # 确保有足够候选
        if len(red_candidates) < 5:
            remaining = [n for n in self.red_range if n not in red_candidates]
            remaining = self._apply_kill_filter(remaining, kill_numbers, 'red')
            red_candidates.extend(remaining[:5-len(red_candidates)])
        
        red_balls = self._smart_select_red(red_candidates, odd_even_state, size_state, seed)
        
        # 蓝球：优先性能池
        blue_candidates = self._apply_kill_filter(self.number_pools.get('blue_performance', []), kill_numbers, 'blue')
        if len(blue_candidates) < 2:
            hot_blue = self._apply_kill_filter(self.number_pools.get('blue_hot_short', []), kill_numbers, 'blue')
            blue_candidates.extend([n for n in hot_blue if n not in blue_candidates])
        
        blue_balls = self._smart_select_blue(blue_candidates, blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _generate_diversity_focused(self, odd_even_state: str, size_state: str,
                                  blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """多样性导向策略：确保号码分散"""
        np.random.seed(seed)
        
        # 红球：从不同区间选择
        red_candidates = []
        
        # 区间1: 1-10
        zone1 = [n for n in range(1, 11) if n not in self._get_recent_numbers('red', 5)]
        zone1 = self._apply_kill_filter(zone1, kill_numbers, 'red')
        if zone1:
            red_candidates.append(np.random.choice(zone1))
        
        # 区间2: 11-20
        zone2 = [n for n in range(11, 21) if n not in self._get_recent_numbers('red', 5)]
        zone2 = self._apply_kill_filter(zone2, kill_numbers, 'red')
        if zone2:
            red_candidates.append(np.random.choice(zone2))
        
        # 区间3: 21-35
        zone3 = [n for n in range(21, 36) if n not in self._get_recent_numbers('red', 5)]
        zone3 = self._apply_kill_filter(zone3, kill_numbers, 'red')
        if zone3:
            red_candidates.extend(list(np.random.choice(zone3, min(3, len(zone3)), replace=False)))
        
        # 补充到5个
        if len(red_candidates) < 5:
            remaining = [n for n in self.red_range if n not in red_candidates]
            remaining = self._apply_kill_filter(remaining, kill_numbers, 'red')
            needed = 5 - len(red_candidates)
            if len(remaining) >= needed:
                red_candidates.extend(list(np.random.choice(remaining, needed, replace=False)))
        
        red_balls = self._adjust_for_constraints(red_candidates[:5], odd_even_state, size_state, seed)
        
        # 蓝球：避免最近出现的
        blue_candidates = [n for n in self.blue_range if n not in self._get_recent_numbers('blue', 3)]
        blue_candidates = self._apply_kill_filter(blue_candidates, kill_numbers, 'blue')
        
        if len(blue_candidates) < 2:
            blue_candidates = self._apply_kill_filter(self.blue_range, kill_numbers, 'blue')
        
        blue_balls = self._smart_select_blue(blue_candidates, blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _generate_balanced_hybrid(self, odd_even_state: str, size_state: str,
                                blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """混合平衡策略：精准度和多样性并重"""
        np.random.seed(seed)
        
        # 红球：50%精准 + 50%多样性
        red_candidates = []
        
        # 精准部分：性能池
        precision_pool = self._apply_kill_filter(self.number_pools.get('red_performance', []), kill_numbers, 'red')
        red_candidates.extend(precision_pool[:2])
        
        # 多样性部分：避免最近号码
        diversity_pool = [n for n in self.red_range if n not in self._get_recent_numbers('red', 3) and n not in red_candidates]
        diversity_pool = self._apply_kill_filter(diversity_pool, kill_numbers, 'red')
        if len(diversity_pool) >= 3:
            red_candidates.extend(list(np.random.choice(diversity_pool, 3, replace=False)))
        
        # 补充
        if len(red_candidates) < 5:
            remaining = [n for n in self.red_range if n not in red_candidates]
            remaining = self._apply_kill_filter(remaining, kill_numbers, 'red')
            needed = 5 - len(red_candidates)
            if len(remaining) >= needed:
                red_candidates.extend(list(np.random.choice(remaining, needed, replace=False)))
        
        red_balls = self._adjust_for_constraints(red_candidates[:5], odd_even_state, size_state, seed)
        
        # 蓝球：混合策略
        blue_candidates = []
        
        # 1个性能号码
        perf_blue = self._apply_kill_filter(self.number_pools.get('blue_performance', []), kill_numbers, 'blue')
        if perf_blue:
            blue_candidates.append(perf_blue[0])
        
        # 1个多样性号码
        div_blue = [n for n in self.blue_range if n not in self._get_recent_numbers('blue', 2) and n not in blue_candidates]
        div_blue = self._apply_kill_filter(div_blue, kill_numbers, 'blue')
        if div_blue:
            blue_candidates.append(np.random.choice(div_blue))
        
        # 补充
        if len(blue_candidates) < 2:
            remaining = [n for n in self.blue_range if n not in blue_candidates]
            remaining = self._apply_kill_filter(remaining, kill_numbers, 'blue')
            needed = 2 - len(blue_candidates)
            if len(remaining) >= needed:
                blue_candidates.extend(list(np.random.choice(remaining, needed, replace=False)))
        
        blue_balls = self._adjust_blue_for_constraints(blue_candidates[:2], blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _generate_adaptive_smart(self, odd_even_state: str, size_state: str, blue_size_state: str,
                               kill_numbers: Dict, seed: int, period_index: int) -> Tuple[List[int], List[int]]:
        """自适应智能策略：根据历史表现动态调整"""
        np.random.seed(seed)
        
        # 根据期次索引调整策略
        if period_index % 3 == 0:
            # 每3期使用一次保守策略
            return self._generate_precision_focused(odd_even_state, size_state, blue_size_state, kill_numbers, seed)
        elif period_index % 5 == 0:
            # 每5期使用一次激进策略
            return self._generate_diversity_focused(odd_even_state, size_state, blue_size_state, kill_numbers, seed)
        else:
            # 其他时候使用平衡策略
            return self._generate_balanced_hybrid(odd_even_state, size_state, blue_size_state, kill_numbers, seed)
    
    def _get_recent_numbers(self, ball_type: str, periods: int) -> List[int]:
        """获取最近几期的号码"""
        recent_numbers = []
        
        for i in range(min(periods, len(self.data))):
            row = self.data.iloc[i]
            red_balls, blue_balls = parse_numbers(row)
            
            if ball_type == 'red':
                recent_numbers.extend(red_balls)
            else:
                recent_numbers.extend(blue_balls)
        
        return recent_numbers
    
    def _apply_kill_filter(self, candidates: List[int], kill_numbers: Dict, ball_type: str) -> List[int]:
        """应用杀号过滤"""
        if not kill_numbers or ball_type not in kill_numbers:
            return candidates[:]
        
        filtered = set(candidates)
        for kill_list in kill_numbers[ball_type]:
            filtered -= set(kill_list)
        
        return list(filtered)
    
    def _smart_select_red(self, candidates: List[int], odd_even_state: str, 
                         size_state: str, seed: int) -> List[int]:
        """智能选择红球（与动态生成器相同的实现）"""
        if len(candidates) < 5:
            remaining = [n for n in self.red_range if n not in candidates]
            candidates.extend(remaining[:5 - len(candidates)])
        
        return self._adjust_for_constraints(candidates[:5], odd_even_state, size_state, seed)
    
    def _smart_select_blue(self, candidates: List[int], size_state: str, seed: int) -> List[int]:
        """智能选择蓝球"""
        if len(candidates) < 2:
            remaining = [n for n in self.blue_range if n not in candidates]
            candidates.extend(remaining[:2 - len(candidates)])
        
        return self._adjust_blue_for_constraints(candidates[:2], size_state, seed)
    
    def _adjust_for_constraints(self, numbers: List[int], odd_even_state: str, 
                              size_state: str, seed: int) -> List[int]:
        """调整号码以满足约束条件"""
        np.random.seed(seed)
        
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)
        
        # 简化实现：如果约束不满足，进行微调
        current_odd = sum(1 for n in numbers if n % 2 == 1)
        current_small = sum(1 for n in numbers if n <= 18)
        
        # 如果奇偶比不对，尝试调整
        if current_odd != odd_count and len(numbers) == 5:
            # 简单调整策略
            pass  # 这里可以实现更复杂的调整逻辑
        
        return sorted(numbers[:5])
    
    def _adjust_blue_for_constraints(self, numbers: List[int], size_state: str, seed: int) -> List[int]:
        """调整蓝球以满足约束条件"""
        np.random.seed(seed)
        
        small_count, big_count = state_to_ratio(size_state)
        current_small = sum(1 for n in numbers if n <= 6)
        
        # 简单调整策略
        if current_small != small_count and len(numbers) == 2:
            # 可以实现调整逻辑
            pass
        
        return sorted(numbers[:2])
    
    def _update_strategy_weights(self):
        """根据历史表现更新策略权重"""
        total_attempts = sum(perf['attempts'] for perf in self.strategy_performance.values())
        
        if total_attempts < 10:  # 数据不足时保持默认权重
            return
        
        # 计算各策略的成功率
        success_rates = {}
        for strategy, perf in self.strategy_performance.items():
            if perf['attempts'] > 0:
                success_rates[strategy] = perf['hits'] / perf['attempts']
            else:
                success_rates[strategy] = 0.25  # 默认值
        
        # 根据成功率调整权重（简化实现）
        total_rate = sum(success_rates.values())
        if total_rate > 0:
            for strategy in self.strategy_weights:
                self.strategy_weights[strategy] = success_rates[strategy] / total_rate
    
    def update_performance(self, strategy: str, hit: bool):
        """更新策略性能"""
        if strategy in self.strategy_performance:
            if hit:
                self.strategy_performance[strategy]['hits'] += 1


def test_balanced_dynamic_generator():
    """测试平衡动态生成器"""
    generator = BalancedDynamicGenerator()
    
    print("测试平衡动态生成器...")
    
    for i in range(5):
        red, blue = generator.generate_balanced_numbers(
            "3:2", "2:3", "1:1", seed=i, current_period_index=i
        )
        print(f"第{i+1}次: 红球{red}, 蓝球{blue}")


if __name__ == "__main__":
    test_balanced_dynamic_generator()
