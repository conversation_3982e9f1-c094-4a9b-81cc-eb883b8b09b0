#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析杀号生成过程，找出顺序差异的原因
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from advanced_probabilistic_system import AdvancedProbabilisticSystem

def analyze_kill_generation_process():
    """详细分析杀号生成过程"""
    print("🔍 详细分析杀号生成过程")
    print("=" * 60)
    
    # 初始化系统
    advanced_system = AdvancedProbabilisticSystem()
    
    if not advanced_system.load_data():
        print("❌ 数据加载失败")
        return
    
    # 测试期号25068
    period = "25068"
    period_index = None
    for i, row in advanced_system.data.iterrows():
        if str(row['期号']) == str(period):
            period_index = i
            break
    
    if period_index is None:
        print(f"❌ 未找到期号 {period}")
        return
    
    # 获取训练数据
    train_data = advanced_system.data.iloc[period_index + 1:period_index + 301]
    
    # 构建period_data
    period_data = {
        'current': train_data.iloc[0],
        'last': train_data.iloc[1],
        'prev2': train_data.iloc[2],
        'prev3': train_data.iloc[3],
        'prev4': train_data.iloc[4],
        'prev5': train_data.iloc[5]
    }
    
    print(f"📅 分析期号: {period}")
    print(f"📊 训练数据期数: {len(train_data)}")
    print(f"📋 Period data:")
    for key, value in period_data.items():
        red_balls = f"{value['红球1']},{value['红球2']},{value['红球3']},{value['红球4']},{value['红球5']}"
        blue_balls = f"{value['蓝球1']},{value['蓝球2']}"
        print(f"  {key}: 期号{value['期号']} - {red_balls} + {blue_balls}")
    
    # 初始化系统
    advanced_system.data = train_data
    advanced_system.initialize_system()
    
    print(f"\n🔧 分析各个杀号算法的输出:")
    print("-" * 40)
    
    # 1. 贝叶斯算法
    print(f"1️⃣ 贝叶斯算法:")
    bayesian_kills = advanced_system.ensemble_system.bayesian_algo.predict_kill_numbers(period_data, target_count=5)
    print(f"   输出: {bayesian_kills}")

    # 2. 马尔可夫链1
    print(f"2️⃣ 马尔可夫链1:")
    markov1_kills = advanced_system.ensemble_system.markov1_algo.predict_kill_numbers(period_data, target_count=5)
    print(f"   输出: {markov1_kills}")

    # 3. 马尔可夫链2
    print(f"3️⃣ 马尔可夫链2:")
    markov2_kills = advanced_system.ensemble_system.markov2_algo.predict_kill_numbers(period_data, target_count=5)
    print(f"   输出: {markov2_kills}")
    
    # 4. 集成系统
    print(f"4️⃣ 集成系统:")
    ensemble_kills = advanced_system.ensemble_system.predict_ensemble_kills(period_data, target_count=5)
    print(f"   输出: {ensemble_kills}")
    
    # 5. 过滤后的结果
    print(f"5️⃣ 过滤处理:")
    from src.utils.utils import parse_numbers
    period1_red, _ = parse_numbers(period_data['current'])
    period2_red, _ = parse_numbers(period_data['last'])
    
    print(f"   前两期红球: {period1_red + period2_red}")
    filtered_kills = [k for k in ensemble_kills if k not in (period1_red + period2_red) and 1 <= k <= 35]
    print(f"   过滤后: {filtered_kills}")
    
    # 6. 分析权重
    print(f"\n⚖️ 集成权重分析:")
    print(f"   当前权重: {advanced_system.ensemble_system.weights}")
    
    # 7. 多次运行测试随机性
    print(f"\n🎲 随机性测试（连续5次运行）:")
    for i in range(5):
        # 重新初始化系统
        test_system = AdvancedProbabilisticSystem()
        test_system.data = train_data
        test_system.initialize_system()
        
        test_kills = test_system.ensemble_system.predict_ensemble_kills(period_data, target_count=5)
        test_filtered = [k for k in test_kills if k not in (period1_red + period2_red) and 1 <= k <= 35]
        
        print(f"   第{i+1}次: {test_filtered}")
    
    print(f"\n" + "=" * 60)
    print("🎉 杀号生成过程分析完成！")

if __name__ == "__main__":
    analyze_kill_generation_process()
