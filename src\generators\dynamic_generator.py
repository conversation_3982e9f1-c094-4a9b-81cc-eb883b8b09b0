"""
动态多样化生成器
解决号码重复问题，每期生成不同的号码组合
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio, load_data
)


class DynamicGenerator:
    """动态多样化生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        self.data = load_data()
        
        # 动态策略权重（每期会变化）
        self.base_strategy_weights = {
            'hot_recent': 0.3,       # 最近热号
            'hot_medium': 0.25,      # 中期热号  
            'balanced': 0.25,        # 均衡选择
            'cold_comeback': 0.2     # 冷号回归
        }
    
    def generate_dynamic_numbers(self, 
                                red_odd_even_state: str,
                                red_size_state: str,
                                blue_size_state: str,
                                kill_numbers: Dict[str, List[List[int]]] = None,
                                seed: int = 0,
                                current_period_index: int = 0) -> Tuple[List[int], List[int]]:
        """
        动态生成号码组合
        
        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            kill_numbers: 杀号列表
            seed: 随机种子
            current_period_index: 当前期次索引（用于动态分析）
            
        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        # 动态分析当前期次的数据
        self._dynamic_analysis(current_period_index, seed)
        
        # 动态调整策略权重
        strategy_weights = self._adjust_strategy_weights(seed)
        
        # 生成多个候选组合
        candidates = []
        
        # 策略1: 最近热号
        hot_recent_red, hot_recent_blue = self._generate_hot_recent(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed
        )
        candidates.append((hot_recent_red, hot_recent_blue, 'hot_recent'))
        
        # 策略2: 中期热号
        hot_medium_red, hot_medium_blue = self._generate_hot_medium(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed + 1
        )
        candidates.append((hot_medium_red, hot_medium_blue, 'hot_medium'))
        
        # 策略3: 均衡选择
        balanced_red, balanced_blue = self._generate_balanced(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed + 2
        )
        candidates.append((balanced_red, balanced_blue, 'balanced'))
        
        # 策略4: 冷号回归
        cold_red, cold_blue = self._generate_cold_comeback(
            red_odd_even_state, red_size_state, blue_size_state, kill_numbers, seed + 3
        )
        candidates.append((cold_red, cold_blue, 'cold_comeback'))
        
        # 根据动态权重选择策略
        np.random.seed(seed + current_period_index)  # 加入期次因子
        weights = [strategy_weights[name] for _, _, name in candidates]
        chosen_idx = np.random.choice(len(candidates), p=weights)
        chosen_red, chosen_blue, strategy = candidates[chosen_idx]
        
        print(f"  动态策略: {strategy} (权重: {weights[chosen_idx]:.3f})")
        
        return chosen_red, chosen_blue
    
    def _dynamic_analysis(self, current_period_index: int, seed: int) -> None:
        """动态分析当前期次的数据"""
        # 获取训练数据（当前期次之后的数据）
        train_data = self.data.iloc[current_period_index + 1:]
        
        if len(train_data) < 10:
            # 数据不足，使用全部数据
            train_data = self.data
        
        # 分析不同时间窗口的热号
        self._analyze_recent_hot(train_data.head(10))    # 最近10期
        self._analyze_medium_hot(train_data.head(30))    # 最近30期
        self._analyze_cold_numbers(train_data.head(50))  # 最近50期
        
        # 分析当前趋势
        self._analyze_current_trend(train_data.head(5))
    
    def _analyze_recent_hot(self, data) -> None:
        """分析最近热号"""
        red_freq = Counter()
        blue_freq = Counter()
        
        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq.update(red_balls)
            blue_freq.update(blue_balls)
        
        # 选择出现频率最高的号码
        self.recent_hot_red = [num for num, _ in red_freq.most_common(12)]
        self.recent_hot_blue = [num for num, _ in blue_freq.most_common(6)]
    
    def _analyze_medium_hot(self, data) -> None:
        """分析中期热号"""
        red_freq = Counter()
        blue_freq = Counter()
        
        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq.update(red_balls)
            blue_freq.update(blue_balls)
        
        self.medium_hot_red = [num for num, _ in red_freq.most_common(15)]
        self.medium_hot_blue = [num for num, _ in blue_freq.most_common(8)]
    
    def _analyze_cold_numbers(self, data) -> None:
        """分析冷号"""
        red_freq = Counter()
        blue_freq = Counter()
        
        for _, row in data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq.update(red_balls)
            blue_freq.update(blue_balls)
        
        # 找出出现频率最低的号码
        all_red_freq = {num: red_freq.get(num, 0) for num in self.red_range}
        all_blue_freq = {num: blue_freq.get(num, 0) for num in self.blue_range}
        
        self.cold_red = sorted(all_red_freq.keys(), key=lambda x: all_red_freq[x])[:12]
        self.cold_blue = sorted(all_blue_freq.keys(), key=lambda x: all_blue_freq[x])[:6]
    
    def _analyze_current_trend(self, data) -> None:
        """分析当前趋势"""
        if len(data) < 3:
            self.trend_factor = 1.0
            return
        
        # 分析最近几期的变化趋势
        recent_sums = []
        for _, row in data.iterrows():
            red_balls, _ = parse_numbers(row)
            recent_sums.append(sum(red_balls))
        
        # 计算趋势因子
        if len(recent_sums) >= 2:
            trend = recent_sums[0] - recent_sums[-1]
            self.trend_factor = 1.0 + trend / 1000  # 归一化
        else:
            self.trend_factor = 1.0
    
    def _adjust_strategy_weights(self, seed: int) -> Dict[str, float]:
        """动态调整策略权重"""
        np.random.seed(seed)
        
        # 基础权重
        weights = self.base_strategy_weights.copy()
        
        # 根据趋势因子调整
        if hasattr(self, 'trend_factor'):
            if self.trend_factor > 1.05:  # 上升趋势
                weights['hot_recent'] += 0.1
                weights['cold_comeback'] -= 0.1
            elif self.trend_factor < 0.95:  # 下降趋势
                weights['cold_comeback'] += 0.1
                weights['hot_recent'] -= 0.1
        
        # 添加随机扰动
        for key in weights:
            weights[key] += np.random.uniform(-0.05, 0.05)
        
        # 归一化
        total = sum(weights.values())
        for key in weights:
            weights[key] /= total
        
        return weights
    
    def _generate_hot_recent(self, odd_even_state: str, size_state: str, 
                           blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """生成最近热号组合"""
        np.random.seed(seed)
        
        # 红球：从最近热号中选择
        red_candidates = self._apply_kill_filter(self.recent_hot_red, kill_numbers, 'red')
        red_balls = self._smart_select_red(red_candidates, odd_even_state, size_state, seed)
        
        # 蓝球：从最近热号中选择
        blue_candidates = self._apply_kill_filter(self.recent_hot_blue, kill_numbers, 'blue')
        blue_balls = self._smart_select_blue(blue_candidates, blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _generate_hot_medium(self, odd_even_state: str, size_state: str,
                           blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """生成中期热号组合"""
        np.random.seed(seed)
        
        red_candidates = self._apply_kill_filter(self.medium_hot_red, kill_numbers, 'red')
        red_balls = self._smart_select_red(red_candidates, odd_even_state, size_state, seed)
        
        blue_candidates = self._apply_kill_filter(self.medium_hot_blue, kill_numbers, 'blue')
        blue_balls = self._smart_select_blue(blue_candidates, blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _generate_balanced(self, odd_even_state: str, size_state: str,
                         blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """生成均衡组合"""
        np.random.seed(seed)
        
        # 红球：混合热号和冷号
        hot_candidates = self.recent_hot_red[:8]
        cold_candidates = self.cold_red[:8]
        mixed_candidates = hot_candidates + cold_candidates
        
        red_candidates = self._apply_kill_filter(mixed_candidates, kill_numbers, 'red')
        red_balls = self._smart_select_red(red_candidates, odd_even_state, size_state, seed)
        
        # 蓝球：混合选择
        hot_blue = self.recent_hot_blue[:4]
        cold_blue = self.cold_blue[:4]
        mixed_blue = hot_blue + cold_blue
        
        blue_candidates = self._apply_kill_filter(mixed_blue, kill_numbers, 'blue')
        blue_balls = self._smart_select_blue(blue_candidates, blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _generate_cold_comeback(self, odd_even_state: str, size_state: str,
                              blue_size_state: str, kill_numbers: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """生成冷号回归组合"""
        np.random.seed(seed)
        
        # 红球：主要选择冷号
        red_candidates = self._apply_kill_filter(self.cold_red, kill_numbers, 'red')
        red_balls = self._smart_select_red(red_candidates, odd_even_state, size_state, seed)
        
        # 蓝球：主要选择冷号
        blue_candidates = self._apply_kill_filter(self.cold_blue, kill_numbers, 'blue')
        blue_balls = self._smart_select_blue(blue_candidates, blue_size_state, seed)
        
        return red_balls, blue_balls
    
    def _smart_select_red(self, candidates: List[int], odd_even_state: str, 
                         size_state: str, seed: int) -> List[int]:
        """智能选择红球"""
        if len(candidates) < 5:
            # 候选不足，补充
            remaining = [n for n in self.red_range if n not in candidates]
            candidates.extend(remaining[:5 - len(candidates)])
        
        np.random.seed(seed)
        
        # 解析状态要求
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)
        
        # 多次尝试生成满足条件的组合
        for attempt in range(10):
            try:
                selected = []
                
                # 按奇偶分类
                odd_candidates = [n for n in candidates if n % 2 == 1]
                even_candidates = [n for n in candidates if n % 2 == 0]
                
                # 选择奇数
                if odd_count > 0 and odd_candidates:
                    selected.extend(list(np.random.choice(
                        odd_candidates, 
                        min(odd_count, len(odd_candidates)), 
                        replace=False
                    )))
                
                # 选择偶数
                remaining_count = 5 - len(selected)
                if even_count > 0 and even_candidates and remaining_count > 0:
                    available_even = [n for n in even_candidates if n not in selected]
                    if available_even:
                        selected.extend(list(np.random.choice(
                            available_even,
                            min(even_count, len(available_even), remaining_count),
                            replace=False
                        )))
                
                # 如果还不够，随机补充
                remaining_count = 5 - len(selected)
                if remaining_count > 0:
                    remaining_candidates = [n for n in candidates if n not in selected]
                    if remaining_candidates:
                        selected.extend(list(np.random.choice(
                            remaining_candidates,
                            min(remaining_count, len(remaining_candidates)),
                            replace=False
                        )))
                
                if len(set(selected)) == 5:
                    return sorted(selected)
                    
            except:
                continue
        
        # 备选方案
        return sorted(list(np.random.choice(candidates, min(5, len(candidates)), replace=False)))
    
    def _smart_select_blue(self, candidates: List[int], size_state: str, seed: int) -> List[int]:
        """智能选择蓝球"""
        if len(candidates) < 2:
            remaining = [n for n in self.blue_range if n not in candidates]
            candidates.extend(remaining[:2 - len(candidates)])
        
        np.random.seed(seed)
        
        small_count, big_count = state_to_ratio(size_state)
        
        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 6]
        big_candidates = [n for n in candidates if 7 <= n <= 12]
        
        selected = []
        
        # 选择小号
        if small_count > 0 and small_candidates:
            selected.extend(list(np.random.choice(
                small_candidates,
                min(small_count, len(small_candidates)),
                replace=False
            )))
        
        # 选择大号
        remaining_count = 2 - len(selected)
        if big_count > 0 and big_candidates and remaining_count > 0:
            available_big = [n for n in big_candidates if n not in selected]
            if available_big:
                selected.extend(list(np.random.choice(
                    available_big,
                    min(big_count, len(available_big), remaining_count),
                    replace=False
                )))
        
        # 如果还不够，随机补充
        remaining_count = 2 - len(selected)
        if remaining_count > 0:
            remaining_candidates = [n for n in candidates if n not in selected]
            if remaining_candidates:
                selected.extend(list(np.random.choice(
                    remaining_candidates,
                    min(remaining_count, len(remaining_candidates)),
                    replace=False
                )))
        
        return sorted(selected[:2])
    
    def _apply_kill_filter(self, candidates: List[int], kill_numbers: Dict, ball_type: str) -> List[int]:
        """应用杀号过滤"""
        if not kill_numbers or ball_type not in kill_numbers:
            return candidates[:]
        
        filtered = set(candidates)
        for kill_list in kill_numbers[ball_type]:
            filtered -= set(kill_list)
        
        return list(filtered)


def test_dynamic_generator():
    """测试动态生成器"""
    generator = DynamicGenerator()
    
    print("测试动态生成器...")
    
    for i in range(5):
        red, blue = generator.generate_dynamic_numbers(
            "3:2", "2:3", "1:1", seed=i, current_period_index=i
        )
        print(f"第{i+1}次: 红球{red}, 蓝球{blue}")


if __name__ == "__main__":
    test_dynamic_generator()
