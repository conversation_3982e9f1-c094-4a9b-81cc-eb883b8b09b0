#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试蓝球杀号性能
对比原始简单策略和高级策略的效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.utils import load_data, parse_numbers
from advanced_blue_kill_algorithm import AdvancedBlueKillAlgorithm
from collections import Counter


def simple_blue_kill_strategy(period_data, data):
    """原始简单蓝球杀号策略"""
    try:
        # 获取最近3期蓝球数据
        recent_blues = []
        for key in ['current', 'last', 'prev2']:
            if key in period_data:
                _, blue_balls = parse_numbers(period_data[key])
                recent_blues.extend(blue_balls)

        # 统计频率，选择最少出现的号码作为杀号
        blue_freq = Counter(recent_blues)
        all_blues = list(range(1, 13))
        min_freq = min(blue_freq.get(b, 0) for b in all_blues)
        candidates = [b for b in all_blues if blue_freq.get(b, 0) == min_freq]

        # 返回1个杀号
        return [candidates[0]] if candidates else [12]

    except Exception as e:
        print(f"⚠️ 简单蓝球杀号预测失败: {e}")
        return [9]


def test_blue_kill_performance():
    """测试蓝球杀号性能"""
    print("🔵 蓝球杀号性能对比测试")
    print("=" * 60)
    
    # 加载数据
    data = load_data('dlt_data.csv')
    print(f"✅ 加载数据: {len(data)} 期")
    
    # 初始化高级蓝球杀号算法
    advanced_killer = AdvancedBlueKillAlgorithm(data)
    
    # 测试参数
    test_periods = 20  # 测试期数
    
    # 结果统计
    simple_results = []
    advanced_results = []
    
    print(f"\n🎯 开始回测 {test_periods} 期...")
    print("-" * 60)
    
    for i in range(test_periods):
        try:
            # 构建period_data（使用第i期之后的数据作为训练数据）
            train_data = data.iloc[i + 1:i + 201]  # 使用后续200期作为训练数据
            
            if len(train_data) < 10:
                continue
            
            period_data = {
                'current': train_data.iloc[0],
                'last': train_data.iloc[1],
                'prev2': train_data.iloc[2],
                'prev3': train_data.iloc[3],
                'prev4': train_data.iloc[4]
            }
            
            # 获取实际结果
            actual_row = data.iloc[i]
            actual_red, actual_blue = parse_numbers(actual_row)
            
            # 简单策略杀号
            simple_kills = simple_blue_kill_strategy(period_data, train_data)
            simple_success = not any(k in actual_blue for k in simple_kills)
            
            # 高级策略杀号
            advanced_killer.data = train_data  # 更新训练数据
            advanced_kills = advanced_killer.predict_blue_kills(period_data, target_count=1)
            advanced_success = not any(k in actual_blue for k in advanced_kills)
            
            # 记录结果
            simple_results.append(simple_success)
            advanced_results.append(advanced_success)
            
            # 显示详细结果
            period_num = actual_row['期号']
            print(f"期号 {period_num}:")
            print(f"  实际蓝球: {actual_blue}")
            print(f"  简单杀号: {simple_kills} {'✅' if simple_success else '❌'}")
            print(f"  高级杀号: {advanced_kills} {'✅' if advanced_success else '❌'}")
            print()
            
        except Exception as e:
            print(f"❌ 期号处理失败: {e}")
            continue
    
    # 统计结果
    if simple_results and advanced_results:
        simple_success_rate = sum(simple_results) / len(simple_results)
        advanced_success_rate = sum(advanced_results) / len(advanced_results)
        
        print("=" * 60)
        print("📊 蓝球杀号性能对比结果")
        print("=" * 60)
        print(f"测试期数: {len(simple_results)}")
        print()
        print(f"📈 简单策略:")
        print(f"  成功率: {simple_success_rate:.1%} ({sum(simple_results)}/{len(simple_results)})")
        print(f"  策略: 最近3期频率统计")
        print()
        print(f"🚀 高级策略:")
        print(f"  成功率: {advanced_success_rate:.1%} ({sum(advanced_results)}/{len(advanced_results)})")
        print(f"  策略: 6种策略融合（频率+特征+位置+趋势+贝叶斯+马尔可夫）")
        print()
        
        # 计算提升效果
        improvement = advanced_success_rate - simple_success_rate
        improvement_percent = (improvement / simple_success_rate * 100) if simple_success_rate > 0 else 0
        
        print(f"🎯 优化效果:")
        if improvement > 0:
            print(f"  ✅ 成功率提升: +{improvement:.1%} (相对提升 {improvement_percent:.1f}%)")
        elif improvement < 0:
            print(f"  ⚠️ 成功率下降: {improvement:.1%} (相对下降 {abs(improvement_percent):.1f}%)")
        else:
            print(f"  ➖ 成功率持平")
        
        print()
        print(f"💡 分析:")
        if advanced_success_rate >= 0.85:
            print(f"  🎉 高级策略表现优秀 (≥85%)")
        elif advanced_success_rate >= 0.80:
            print(f"  ✅ 高级策略达到目标 (≥80%)")
        elif advanced_success_rate >= 0.75:
            print(f"  📈 高级策略表现良好 (≥75%)")
        else:
            print(f"  ⚠️ 高级策略需要进一步优化")
        
        if improvement > 0.05:
            print(f"  🚀 优化效果显著 (提升>5%)")
        elif improvement > 0:
            print(f"  📈 优化效果明显")
        else:
            print(f"  🔧 需要调整策略权重或参数")
        
        # 策略分析
        print()
        print(f"🔍 策略权重分析:")
        for strategy, weight in advanced_killer.strategies.items():
            print(f"  {strategy}: {weight:.1%}")
        
        print()
        print(f"📋 建议:")
        if advanced_success_rate < 0.80:
            print(f"  • 调整策略权重，增加表现好的策略权重")
            print(f"  • 优化特征提取算法")
            print(f"  • 增加安全性检查机制")
        if improvement <= 0:
            print(f"  • 分析失败案例，找出问题模式")
            print(f"  • 考虑添加新的杀号策略")
            print(f"  • 优化投票机制和权重分配")
        
    else:
        print("❌ 测试数据不足，无法生成统计结果")


def test_strategy_individual_performance():
    """测试各个策略的单独性能"""
    print("\n🔍 各策略单独性能测试")
    print("=" * 60)
    
    # 加载数据
    data = load_data('dlt_data.csv')
    advanced_killer = AdvancedBlueKillAlgorithm(data)
    
    test_periods = 10
    strategy_results = {
        'frequency': [],
        'feature': [],
        'position': [],
        'trend': [],
        'bayesian': [],
        'markov': []
    }
    
    for i in range(test_periods):
        try:
            train_data = data.iloc[i + 1:i + 201]
            if len(train_data) < 10:
                continue
            
            period_data = {
                'current': train_data.iloc[0],
                'last': train_data.iloc[1],
                'prev2': train_data.iloc[2],
                'prev3': train_data.iloc[3],
                'prev4': train_data.iloc[4]
            }
            
            actual_row = data.iloc[i]
            actual_red, actual_blue = parse_numbers(actual_row)
            
            advanced_killer.data = train_data
            
            # 测试各个策略
            for strategy_name in strategy_results.keys():
                try:
                    if strategy_name == 'frequency':
                        kills = advanced_killer.frequency_kill_strategy(period_data, 1)
                    elif strategy_name == 'feature':
                        kills = advanced_killer.feature_kill_strategy(period_data, 1)
                    elif strategy_name == 'position':
                        kills = advanced_killer.position_kill_strategy(period_data, 1)
                    elif strategy_name == 'trend':
                        kills = advanced_killer.trend_kill_strategy(period_data, 1)
                    elif strategy_name == 'bayesian':
                        kills = advanced_killer.bayesian_kill_strategy(period_data, 1)
                    elif strategy_name == 'markov':
                        kills = advanced_killer.markov_kill_strategy(period_data, 1)
                    
                    success = not any(k in actual_blue for k in kills)
                    strategy_results[strategy_name].append(success)
                except:
                    strategy_results[strategy_name].append(False)
        except:
            continue
    
    # 显示各策略性能
    print("📊 各策略单独性能:")
    for strategy, results in strategy_results.items():
        if results:
            success_rate = sum(results) / len(results)
            print(f"  {strategy:10s}: {success_rate:.1%} ({sum(results)}/{len(results)})")
    
    print()


if __name__ == "__main__":
    test_blue_kill_performance()
    test_strategy_individual_performance()
