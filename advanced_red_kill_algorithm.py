#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级红球杀号算法
借鉴蓝球算法成功经验，实现马尔可夫+贝叶斯组合策略
目标：将红球杀号成功率从70%提升到85%+
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from collections import Counter, defaultdict
import random
import math


class AdvancedRedKillAlgorithm:
    """高级红球杀号算法"""
    
    def __init__(self, data: pd.DataFrame):
        self.data = data
        
        # 使用优化后的最佳参数（46.7%成功率）
        self.strategies = {
            'bayesian': 0.5,      # 贝叶斯策略权重
            'markov': 0.5         # 马尔可夫策略权重
        }

        # 算法参数
        self.bayesian_history_periods = 80    # 贝叶斯历史期数
        self.markov_history_periods = 60      # 马尔可夫历史期数
        self.similarity_threshold = 2         # 相似度阈值
        self.default_target_count = 5         # 默认杀号数量
        
        # 危险号码：经常被误杀的热门号码
        self.dangerous_numbers = {1, 2, 3, 10, 11, 12, 18, 20, 21, 29}
        
        self.kill_history = []  # 杀号历史记录
        self.success_history = []  # 成功率历史记录
        
    def extract_red_features(self, red_balls: List[int]) -> Dict:
        """提取红球特征"""
        if len(red_balls) != 5:
            return {}
        
        sorted_balls = sorted(red_balls)
        
        return {
            'size_ratio': self._calculate_red_size_ratio(red_balls),
            'odd_even_ratio': self._calculate_red_odd_even_ratio(red_balls),
            'zone_distribution': self._calculate_zone_distribution(red_balls),
            'sum_value': sum(red_balls),
            'span': max(red_balls) - min(red_balls),
            'consecutive_count': self._count_consecutive_numbers(red_balls),
            'ac_value': self._calculate_ac_value(red_balls),
            'small_count': sum(1 for b in red_balls if b <= 17),
            'large_count': sum(1 for b in red_balls if b >= 18),
            'odd_count': sum(1 for b in red_balls if b % 2 == 1),
            'even_count': sum(1 for b in red_balls if b % 2 == 0)
        }
    
    def _calculate_red_size_ratio(self, red_balls: List[int]) -> str:
        """计算红球大小比"""
        small_count = sum(1 for b in red_balls if b <= 17)
        large_count = len(red_balls) - small_count
        
        if small_count > large_count:
            return "small_more"
        elif large_count > small_count:
            return "large_more"
        else:
            return "balanced"
    
    def _calculate_red_odd_even_ratio(self, red_balls: List[int]) -> str:
        """计算红球奇偶比"""
        odd_count = sum(1 for b in red_balls if b % 2 == 1)
        even_count = len(red_balls) - odd_count
        
        if odd_count > even_count:
            return "odd_more"
        elif even_count > odd_count:
            return "even_more"
        else:
            return "balanced"
    
    def _calculate_zone_distribution(self, red_balls: List[int]) -> str:
        """计算区间分布（5个区间：1-7, 8-14, 15-21, 22-28, 29-35）"""
        zones = [0] * 5
        for ball in red_balls:
            zone_idx = min((ball - 1) // 7, 4)  # 确保不超过索引4
            zones[zone_idx] += 1
        
        max_count = max(zones)
        dominant_zones = [i for i, count in enumerate(zones) if count == max_count]
        
        if len(dominant_zones) == 1:
            return f"zone{dominant_zones[0] + 1}_dominant"
        else:
            return "balanced"
    
    def _count_consecutive_numbers(self, red_balls: List[int]) -> int:
        """计算连号数量"""
        sorted_balls = sorted(red_balls)
        consecutive_count = 0
        
        for i in range(len(sorted_balls) - 1):
            if sorted_balls[i + 1] - sorted_balls[i] == 1:
                consecutive_count += 1
        
        return consecutive_count
    
    def _calculate_ac_value(self, red_balls: List[int]) -> int:
        """计算AC值（号码复杂度）"""
        sorted_balls = sorted(red_balls)
        differences = []
        
        for i in range(len(sorted_balls)):
            for j in range(i + 1, len(sorted_balls)):
                diff = abs(sorted_balls[j] - sorted_balls[i])
                differences.append(diff)
        
        unique_differences = len(set(differences))
        return unique_differences
    
    def predict_red_kills(self, period_data: Dict, target_count: int = 5) -> List[int]:
        """马尔可夫+贝叶斯组合预测红球杀号"""
        print(f"🔴 开始红球杀号预测（马尔可夫+贝叶斯组合，目标{target_count}个）...")
        
        # 获取贝叶斯策略的概率分布
        bayesian_probs = self._get_bayesian_probabilities(period_data)
        print(f"  贝叶斯概率分布: {dict(sorted(bayesian_probs.items(), key=lambda x: x[1])[:5])}")
        
        # 获取马尔可夫策略的状态预测
        markov_predictions = self._get_markov_predictions(period_data)
        print(f"  马尔可夫预测: {markov_predictions[:8]}")
        
        # 组合两种策略：贝叶斯提供概率基础，马尔可夫提供状态约束
        combined_scores = self._combine_strategies(bayesian_probs, markov_predictions)
        
        # 安全性检查：避免杀掉危险号码和最近出现的号码
        safe_scores = self._apply_safety_filter(combined_scores, period_data)
        
        # 选择得分最低的号码作为杀号（概率最低 + 状态最不可能）
        if safe_scores:
            sorted_candidates = sorted(safe_scores.items(), key=lambda x: x[1])
            final_kills = [num for num, score in sorted_candidates[:target_count]]
            print(f"  🎯 最终杀号: {sorted(final_kills)}")
            return final_kills
        else:
            # 默认杀号（选择相对安全的号码）
            default_kill = [32, 33, 34, 35, 31, 30][:target_count]
            print(f"  🔄 使用默认杀号: {default_kill}")
            return default_kill
    
    def _get_bayesian_probabilities(self, period_data: Dict) -> Dict[int, float]:
        """获取贝叶斯概率分布"""
        try:
            from src.utils.utils import parse_numbers
            
            # 计算先验概率（基于历史频率）
            prior_probs = {}
            total_count = 0
            
            for i, row in self.data.head(self.bayesian_history_periods).iterrows():
                red_balls, _ = parse_numbers(row)
                for red in red_balls:
                    prior_probs[red] = prior_probs.get(red, 0) + 1
                    total_count += 1
            
            # 归一化先验概率
            for red in range(1, 36):
                prior_probs[red] = prior_probs.get(red, 0) / total_count if total_count > 0 else 1/35
            
            # 计算条件概率（基于当前状态）
            current_red, _ = parse_numbers(period_data['current'])
            current_features = self.extract_red_features(current_red)
            
            # 收集相似状态的历史数据
            similar_states = []
            for i, row in self.data.head(self.bayesian_history_periods).iterrows():
                red_balls, _ = parse_numbers(row)
                features = self.extract_red_features(red_balls)
                
                # 计算特征相似度
                similarity = 0
                if features.get('size_ratio') == current_features.get('size_ratio'):
                    similarity += 1
                if features.get('odd_even_ratio') == current_features.get('odd_even_ratio'):
                    similarity += 1
                if features.get('zone_distribution') == current_features.get('zone_distribution'):
                    similarity += 1
                if abs(features.get('sum_value', 0) - current_features.get('sum_value', 0)) <= 10:
                    similarity += 1
                if abs(features.get('span', 0) - current_features.get('span', 0)) <= 3:
                    similarity += 1
                
                if similarity >= self.similarity_threshold:  # 相似度阈值
                    similar_states.append(red_balls)
            
            # 计算后验概率
            posterior_probs = {}
            for red in range(1, 36):
                # 在相似状态中的出现频率
                conditional_prob = sum(1 for state in similar_states if red in state) / len(similar_states) if similar_states else 1/35
                
                # 贝叶斯公式：后验概率 = 先验概率 × 条件概率
                posterior_probs[red] = prior_probs[red] * conditional_prob
            
            return posterior_probs
            
        except Exception as e:
            print(f"⚠️ 贝叶斯概率计算失败: {e}")
            # 返回均匀分布
            return {red: 1/35 for red in range(1, 36)}
    
    def _get_markov_predictions(self, period_data: Dict) -> List[int]:
        """获取马尔可夫预测结果"""
        try:
            from src.utils.utils import parse_numbers
            
            # 构建简化的状态转移矩阵
            transition_matrix = defaultdict(lambda: defaultdict(int))
            
            for i in range(len(self.data) - 1):
                if i >= self.markov_history_periods:
                    break
                
                # 当前状态
                current_red, _ = parse_numbers(self.data.iloc[i + 1])
                current_state = self._get_simple_red_state(current_red)
                
                # 下一状态
                next_red, _ = parse_numbers(self.data.iloc[i])
                next_state = self._get_simple_red_state(next_red)
                
                transition_matrix[current_state][next_state] += 1
            
            # 获取当前状态
            current_red, _ = parse_numbers(period_data['current'])
            current_state = self._get_simple_red_state(current_red)
            
            # 预测最不可能的下一状态
            if current_state in transition_matrix:
                next_states = transition_matrix[current_state]
                total_transitions = sum(next_states.values())
                
                # 计算状态概率
                state_probs = {state: count / total_transitions for state, count in next_states.items()}
                
                # 选择概率最低的状态
                if state_probs:
                    min_prob_state = min(state_probs.items(), key=lambda x: x[1])[0]
                    return self._state_to_red_numbers(min_prob_state)
            
            return [32, 33, 34, 35]  # 默认预测
                
        except Exception as e:
            print(f"⚠️ 马尔可夫预测失败: {e}")
            return [32, 33, 34, 35]
    
    def _get_simple_red_state(self, red_balls: List[int]) -> str:
        """获取简化的红球状态"""
        if len(red_balls) != 5:
            return "unknown"
        
        features = self.extract_red_features(red_balls)
        
        # 简化状态：大小比 + 奇偶性 + 区间分布
        size_ratio = features.get('size_ratio', 'balanced')
        odd_even = features.get('odd_even_ratio', 'balanced')
        zone_dist = features.get('zone_distribution', 'balanced')
        
        return f"{size_ratio}_{odd_even}_{zone_dist}"
    
    def _state_to_red_numbers(self, state: str) -> List[int]:
        """根据状态选择对应的杀号"""
        parts = state.split('_')
        if len(parts) != 3:
            return [32, 33, 34, 35]
        
        size_ratio, odd_even, zone_dist = parts
        candidates = []
        
        # 根据大小比选择杀号
        if size_ratio == "small_more":
            candidates.extend([25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35])  # 杀大号
        elif size_ratio == "large_more":
            candidates.extend([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])  # 杀小号
        else:
            candidates.extend([16, 17, 18, 19, 20])  # 杀中间号
        
        # 根据奇偶性选择杀号
        if odd_even == "odd_more":
            candidates.extend([2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34])  # 杀偶数
        elif odd_even == "even_more":
            candidates.extend([1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35])  # 杀奇数
        
        # 根据区间分布选择杀号
        if "zone1_dominant" in zone_dist:
            candidates.extend([29, 30, 31, 32, 33, 34, 35])  # 杀第5区间
        elif "zone5_dominant" in zone_dist:
            candidates.extend([1, 2, 3, 4, 5, 6, 7])  # 杀第1区间
        
        return list(set(candidates))[:10]  # 返回前10个
    
    def _combine_strategies(self, bayesian_probs: Dict[int, float], markov_predictions: List[int]) -> Dict[int, float]:
        """组合贝叶斯和马尔可夫策略"""
        combined_scores = {}
        
        for red in range(1, 36):
            # 贝叶斯概率（越低越好作为杀号）
            bayesian_score = bayesian_probs.get(red, 1/35)
            
            # 马尔可夫预测（在预测列表中的号码得分更低）
            markov_score = 0.3 if red in markov_predictions else 1.0
            
            # 组合得分：贝叶斯权重0.6，马尔可夫权重0.4
            combined_scores[red] = bayesian_score * self.strategies['bayesian'] + markov_score * self.strategies['markov']
        
        return combined_scores
    
    def _apply_safety_filter(self, scores: Dict[int, float], period_data: Dict) -> Dict[int, float]:
        """应用安全性过滤"""
        try:
            from src.utils.utils import parse_numbers
            
            # 收集最近3期出现的红球
            recent_reds = []
            for key in ['current', 'last', 'prev2']:
                if key in period_data:
                    red_balls, _ = parse_numbers(period_data[key])
                    recent_reds.extend(red_balls)
            
            # 过滤掉危险号码和最近出现的号码
            safe_scores = {}
            for num, score in scores.items():
                if num not in self.dangerous_numbers and num not in recent_reds:
                    safe_scores[num] = score
            
            return safe_scores if safe_scores else scores
        except:
            return scores


def main():
    """测试高级红球杀号算法"""
    print("🔴 测试高级红球杀号算法")
    print("=" * 50)
    
    # 加载数据
    import sys
    import os
    from pathlib import Path
    
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    from src.utils.utils import load_data, parse_numbers
    
    data = load_data('dlt_data.csv')
    
    # 初始化算法
    red_killer = AdvancedRedKillAlgorithm(data)
    
    # 构建测试数据
    period_data = {
        'current': data.iloc[0],
        'last': data.iloc[1],
        'prev2': data.iloc[2],
        'prev3': data.iloc[3],
        'prev4': data.iloc[4]
    }
    
    # 测试杀号
    kills = red_killer.predict_red_kills(period_data, target_count=6)
    
    print(f"\n🎯 红球杀号结果: {sorted(kills)}")
    
    # 验证杀号效果
    actual_red = parse_numbers(data.iloc[0])[0]
    success = not any(k in actual_red for k in kills)
    
    print(f"📊 实际红球: {sorted(actual_red)}")
    print(f"✅ 杀号成功: {'是' if success else '否'}")


if __name__ == "__main__":
    main()
