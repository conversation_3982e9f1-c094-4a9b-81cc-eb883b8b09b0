"""
在线学习系统
实现真正的在线学习和实时调整
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional, Callable
from collections import deque, defaultdict
import json
import os
from datetime import datetime, timedelta
import threading
import time

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import parse_numbers, load_data


class OnlineNeuralNetwork:
    """在线神经网络"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int,
                 learning_rate: float = 0.01, momentum: float = 0.9):
        """
        初始化在线神经网络
        
        Args:
            input_size: 输入大小
            hidden_sizes: 隐藏层大小列表
            output_size: 输出大小
            learning_rate: 学习率
            momentum: 动量
        """
        self.input_size = input_size
        self.output_size = output_size
        self.learning_rate = learning_rate
        self.momentum = momentum
        
        # 构建网络层
        self.layers = []
        prev_size = input_size
        
        for hidden_size in hidden_sizes:
            layer = {
                'weights': np.random.randn(prev_size, hidden_size) * 0.1,
                'biases': np.zeros(hidden_size),
                'weight_momentum': np.zeros((prev_size, hidden_size)),
                'bias_momentum': np.zeros(hidden_size),
                'type': 'hidden'
            }
            self.layers.append(layer)
            prev_size = hidden_size
        
        # 输出层
        output_layer = {
            'weights': np.random.randn(prev_size, output_size) * 0.1,
            'biases': np.zeros(output_size),
            'weight_momentum': np.zeros((prev_size, output_size)),
            'bias_momentum': np.zeros(output_size),
            'type': 'output'
        }
        self.layers.append(output_layer)
        
        # 在线学习统计
        self.update_count = 0
        self.recent_losses = deque(maxlen=100)
        self.learning_rate_schedule = []
    
    def forward(self, X):
        """前向传播"""
        activations = [X]
        
        for layer in self.layers:
            z = np.dot(activations[-1], layer['weights']) + layer['biases']
            
            if layer['type'] == 'output':
                a = self._softmax(z)
            else:
                a = self._sigmoid(z)
            
            activations.append(a)
        
        return activations
    
    def online_update(self, X, y):
        """在线更新（单个样本或小批量）"""
        if X.ndim == 1:
            X = X.reshape(1, -1)
        if y.ndim == 1:
            y = y.reshape(1, -1)
        
        # 前向传播
        activations = self.forward(X)
        
        # 计算损失
        loss = self._cross_entropy_loss(activations[-1], y)
        self.recent_losses.append(loss)
        
        # 反向传播和参数更新
        self._backward_online(activations, y)
        
        self.update_count += 1
        
        # 自适应学习率调整
        if self.update_count % 50 == 0:
            self._adjust_learning_rate()
        
        return loss
    
    def _backward_online(self, activations, targets):
        """在线反向传播"""
        batch_size = activations[0].shape[0]
        
        # 计算输出层误差
        output_error = activations[-1] - targets
        
        # 从输出层向前传播误差
        errors = [output_error]
        
        for i in range(len(self.layers) - 2, -1, -1):
            error = np.dot(errors[0], self.layers[i + 1]['weights'].T) * \
                   self._sigmoid_derivative(activations[i + 1])
            errors.insert(0, error)
        
        # 使用动量更新权重和偏置
        for i, layer in enumerate(self.layers):
            # 计算梯度
            weight_grad = np.dot(activations[i].T, errors[i]) / batch_size
            bias_grad = np.mean(errors[i], axis=0)
            
            # 动量更新
            layer['weight_momentum'] = (self.momentum * layer['weight_momentum'] + 
                                      self.learning_rate * weight_grad)
            layer['bias_momentum'] = (self.momentum * layer['bias_momentum'] + 
                                    self.learning_rate * bias_grad)
            
            # 应用更新
            layer['weights'] -= layer['weight_momentum']
            layer['biases'] -= layer['bias_momentum']
    
    def _adjust_learning_rate(self):
        """自适应学习率调整"""
        if len(self.recent_losses) < 20:
            return
        
        # 计算最近损失的趋势
        recent_losses = list(self.recent_losses)[-20:]
        early_losses = recent_losses[:10]
        late_losses = recent_losses[10:]
        
        early_avg = np.mean(early_losses)
        late_avg = np.mean(late_losses)
        
        # 如果损失没有下降，降低学习率
        if late_avg >= early_avg:
            self.learning_rate *= 0.95
            print(f"  学习率调整为: {self.learning_rate:.6f}")
        # 如果损失下降很快，可以稍微提高学习率
        elif late_avg < early_avg * 0.9:
            self.learning_rate *= 1.02
            self.learning_rate = min(self.learning_rate, 0.1)  # 设置上限
        
        self.learning_rate_schedule.append({
            'update_count': self.update_count,
            'learning_rate': self.learning_rate,
            'avg_loss': late_avg
        })
    
    def predict(self, X):
        """预测"""
        activations = self.forward(X)
        return activations[-1]
    
    def get_learning_statistics(self):
        """获取学习统计信息"""
        return {
            'update_count': self.update_count,
            'current_learning_rate': self.learning_rate,
            'recent_avg_loss': np.mean(list(self.recent_losses)) if self.recent_losses else 0,
            'learning_rate_history': self.learning_rate_schedule[-10:]  # 最近10次调整
        }
    
    def _sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def _sigmoid_derivative(self, x):
        """Sigmoid导数"""
        return x * (1 - x)
    
    def _softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    def _cross_entropy_loss(self, predictions, targets):
        """交叉熵损失"""
        epsilon = 1e-15
        predictions = np.clip(predictions, epsilon, 1 - epsilon)
        return -np.mean(np.sum(targets * np.log(predictions), axis=1))


class OnlineLearningSystem:
    """在线学习系统"""
    
    def __init__(self, feature_size: int, num_classes: int = 6):
        """
        初始化在线学习系统
        
        Args:
            feature_size: 特征大小
            num_classes: 类别数量
        """
        self.feature_size = feature_size
        self.num_classes = num_classes
        
        # 在线模型
        self.online_models = {
            'red_odd_even': OnlineNeuralNetwork(feature_size, [64, 32], num_classes),
            'red_size': OnlineNeuralNetwork(feature_size, [64, 32], num_classes),
            'blue_size': OnlineNeuralNetwork(feature_size, [32, 16], 3)
        }
        
        # 数据流管理
        self.data_stream = deque(maxlen=1000)  # 保持最近1000个样本
        self.feature_buffer = deque(maxlen=100)  # 特征缓冲区
        self.label_buffer = deque(maxlen=100)    # 标签缓冲区
        
        # 性能监控
        self.performance_tracker = {
            'red_odd_even': deque(maxlen=200),
            'red_size': deque(maxlen=200),
            'blue_size': deque(maxlen=200)
        }
        
        # 概念漂移检测
        self.drift_detector = ConceptDriftDetector()
        
        # 在线学习状态
        self.is_learning = False
        self.learning_thread = None
        self.stop_learning = False
        
        # 模型集成权重（动态调整）
        self.ensemble_weights = {
            'red_odd_even': 1.0,
            'red_size': 1.0,
            'blue_size': 1.0
        }
    
    def start_online_learning(self):
        """启动在线学习"""
        if self.is_learning:
            print("在线学习已经在运行")
            return
        
        print("启动在线学习系统...")
        self.is_learning = True
        self.stop_learning = False
        
        # 启动学习线程
        self.learning_thread = threading.Thread(target=self._learning_loop)
        self.learning_thread.daemon = True
        self.learning_thread.start()
        
        print("在线学习系统已启动")
    
    def stop_online_learning(self):
        """停止在线学习"""
        if not self.is_learning:
            return
        
        print("停止在线学习系统...")
        self.stop_learning = True
        
        if self.learning_thread:
            self.learning_thread.join(timeout=5)
        
        self.is_learning = False
        print("在线学习系统已停止")
    
    def add_new_data(self, features: np.ndarray, red_balls: List[int], blue_balls: List[int]):
        """
        添加新数据到学习系统
        
        Args:
            features: 特征向量
            red_balls: 红球号码
            blue_balls: 蓝球号码
        """
        # 计算标签
        red_odd_count = sum(1 for x in red_balls if x % 2 == 1)
        red_odd_even_label = f"{red_odd_count}:{5-red_odd_count}"
        
        red_small_count = sum(1 for x in red_balls if x <= 18)
        red_size_label = f"{red_small_count}:{5-red_small_count}"
        
        blue_small_count = sum(1 for x in blue_balls if x <= 6)
        blue_size_label = f"{blue_small_count}:{2-blue_small_count}"
        
        # 编码标签
        labels = {
            'red_odd_even': self._encode_label(red_odd_even_label, 6),
            'red_size': self._encode_label(red_size_label, 6),
            'blue_size': self._encode_label(blue_size_label, 3)
        }
        
        # 添加到缓冲区
        self.feature_buffer.append(features)
        self.label_buffer.append(labels)
        
        # 添加到数据流
        data_point = {
            'timestamp': datetime.now(),
            'features': features,
            'labels': labels,
            'red_balls': red_balls,
            'blue_balls': blue_balls
        }
        self.data_stream.append(data_point)
        
        print(f"添加新数据点，缓冲区大小: {len(self.feature_buffer)}")
    
    def _learning_loop(self):
        """学习循环"""
        while not self.stop_learning:
            try:
                # 检查是否有足够的数据进行学习
                if len(self.feature_buffer) >= 10:
                    self._perform_online_update()
                
                # 检查概念漂移
                if len(self.data_stream) >= 50:
                    self._check_concept_drift()
                
                # 调整集成权重
                self._adjust_ensemble_weights()
                
                # 休眠一段时间
                time.sleep(1)
                
            except Exception as e:
                print(f"在线学习循环错误: {e}")
                time.sleep(5)
    
    def _perform_online_update(self):
        """执行在线更新"""
        if len(self.feature_buffer) == 0:
            return
        
        # 获取批量数据
        batch_size = min(10, len(self.feature_buffer))
        
        features_batch = []
        labels_batch = {task: [] for task in self.online_models.keys()}
        
        for _ in range(batch_size):
            if self.feature_buffer and self.label_buffer:
                features = self.feature_buffer.popleft()
                labels = self.label_buffer.popleft()
                
                features_batch.append(features)
                for task in labels_batch.keys():
                    labels_batch[task].append(labels[task])
        
        if not features_batch:
            return
        
        features_array = np.array(features_batch)
        
        # 更新每个模型
        for task, model in self.online_models.items():
            if task in labels_batch and labels_batch[task]:
                labels_array = np.array(labels_batch[task])
                
                # 在线更新
                loss = model.online_update(features_array, labels_array)
                
                # 记录性能
                self.performance_tracker[task].append({
                    'timestamp': datetime.now(),
                    'loss': loss,
                    'update_count': model.update_count
                })
        
        print(f"在线更新完成，批量大小: {batch_size}")
    
    def _check_concept_drift(self):
        """检查概念漂移"""
        recent_data = list(self.data_stream)[-50:]
        older_data = list(self.data_stream)[-100:-50] if len(self.data_stream) >= 100 else []
        
        if len(older_data) < 20:
            return
        
        # 检测每个任务的概念漂移
        for task in self.online_models.keys():
            drift_detected = self.drift_detector.detect_drift(
                recent_data, older_data, task
            )
            
            if drift_detected:
                print(f"检测到 {task} 任务的概念漂移，调整学习率")
                
                # 增加学习率以快速适应新概念
                model = self.online_models[task]
                model.learning_rate *= 1.5
                model.learning_rate = min(model.learning_rate, 0.1)
    
    def _adjust_ensemble_weights(self):
        """调整集成权重"""
        for task in self.online_models.keys():
            if len(self.performance_tracker[task]) >= 10:
                # 基于最近性能调整权重
                recent_performance = list(self.performance_tracker[task])[-10:]
                avg_loss = np.mean([p['loss'] for p in recent_performance])
                
                # 损失越低，权重越高
                new_weight = 1.0 / (1.0 + avg_loss)
                
                # 平滑调整
                self.ensemble_weights[task] = (
                    0.9 * self.ensemble_weights[task] + 0.1 * new_weight
                )
    
    def predict_online(self, features: np.ndarray) -> Dict[str, Tuple[str, float]]:
        """
        在线预测
        
        Args:
            features: 特征向量
            
        Returns:
            Dict[str, Tuple[str, float]]: 预测结果
        """
        if features.ndim == 1:
            features = features.reshape(1, -1)
        
        predictions = {}
        
        for task, model in self.online_models.items():
            # 获取预测概率
            pred_probs = model.predict(features)[0]
            
            # 找到最大概率的类别
            pred_class_idx = np.argmax(pred_probs)
            pred_prob = pred_probs[pred_class_idx]
            
            # 应用集成权重
            weighted_prob = pred_prob * self.ensemble_weights[task]
            
            # 解码标签
            pred_label = self._decode_label(pred_class_idx, task)
            
            predictions[task] = (pred_label, float(weighted_prob))
        
        return predictions
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'is_learning': self.is_learning,
            'data_stream_size': len(self.data_stream),
            'buffer_sizes': {
                'features': len(self.feature_buffer),
                'labels': len(self.label_buffer)
            },
            'model_statistics': {},
            'ensemble_weights': self.ensemble_weights.copy(),
            'recent_performance': {}
        }
        
        # 获取模型统计
        for task, model in self.online_models.items():
            status['model_statistics'][task] = model.get_learning_statistics()
        
        # 获取最近性能
        for task, tracker in self.performance_tracker.items():
            if tracker:
                recent_losses = [p['loss'] for p in list(tracker)[-10:]]
                status['recent_performance'][task] = {
                    'avg_loss': np.mean(recent_losses),
                    'loss_trend': 'decreasing' if len(recent_losses) >= 2 and recent_losses[-1] < recent_losses[0] else 'stable'
                }
        
        return status
    
    def _encode_label(self, label_str: str, num_classes: int) -> np.ndarray:
        """编码标签"""
        # 简化的标签编码
        label_map = {
            '0:5': 0, '1:4': 1, '2:3': 2, '3:2': 3, '4:1': 4, '5:0': 5,  # 6类
            '0:2': 0, '1:1': 1, '2:0': 2  # 3类
        }
        
        label_idx = label_map.get(label_str, 0)
        one_hot = np.zeros(num_classes)
        one_hot[label_idx] = 1
        return one_hot
    
    def _decode_label(self, class_idx: int, task: str) -> str:
        """解码标签"""
        if task == 'blue_size':
            label_map = {0: '0:2', 1: '1:1', 2: '2:0'}
        else:
            label_map = {0: '0:5', 1: '1:4', 2: '2:3', 3: '3:2', 4: '4:1', 5: '5:0'}
        
        return label_map.get(class_idx, '3:2')


class ConceptDriftDetector:
    """概念漂移检测器"""
    
    def __init__(self, threshold: float = 0.1):
        """
        初始化概念漂移检测器
        
        Args:
            threshold: 漂移检测阈值
        """
        self.threshold = threshold
    
    def detect_drift(self, recent_data: List[Dict], older_data: List[Dict], task: str) -> bool:
        """
        检测概念漂移
        
        Args:
            recent_data: 最近数据
            older_data: 较早数据
            task: 任务名称
            
        Returns:
            bool: 是否检测到漂移
        """
        if len(recent_data) < 10 or len(older_data) < 10:
            return False
        
        # 计算标签分布
        recent_labels = [data['labels'][task] for data in recent_data]
        older_labels = [data['labels'][task] for data in older_data]
        
        # 计算分布差异（简化实现）
        recent_dist = self._calculate_distribution(recent_labels)
        older_dist = self._calculate_distribution(older_labels)
        
        # 计算KL散度
        kl_divergence = self._kl_divergence(recent_dist, older_dist)
        
        return kl_divergence > self.threshold
    
    def _calculate_distribution(self, labels: List[np.ndarray]) -> np.ndarray:
        """计算标签分布"""
        if not labels:
            return np.array([])
        
        # 计算每个类别的频率
        num_classes = len(labels[0])
        class_counts = np.zeros(num_classes)
        
        for label in labels:
            class_counts += label
        
        # 归一化
        total = np.sum(class_counts)
        if total > 0:
            return class_counts / total
        else:
            return np.ones(num_classes) / num_classes
    
    def _kl_divergence(self, p: np.ndarray, q: np.ndarray) -> float:
        """计算KL散度"""
        if len(p) != len(q):
            return 0.0
        
        # 避免除零
        p = np.clip(p, 1e-10, 1.0)
        q = np.clip(q, 1e-10, 1.0)
        
        return np.sum(p * np.log(p / q))


def test_online_learning_system():
    """测试在线学习系统"""
    # 创建在线学习系统
    system = OnlineLearningSystem(feature_size=50)
    
    print("测试在线学习系统...")
    
    # 启动在线学习
    system.start_online_learning()
    
    # 模拟添加新数据
    for i in range(20):
        # 生成模拟特征和标签
        features = np.random.randn(50)
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        
        system.add_new_data(features, red_balls, blue_balls)
        
        # 测试预测
        if i % 5 == 0:
            predictions = system.predict_online(features)
            print(f"第 {i+1} 次预测: {predictions}")
        
        time.sleep(0.5)
    
    # 获取系统状态
    status = system.get_system_status()
    print(f"\n系统状态:")
    print(f"  数据流大小: {status['data_stream_size']}")
    print(f"  缓冲区大小: {status['buffer_sizes']}")
    print(f"  集成权重: {status['ensemble_weights']}")
    
    # 停止在线学习
    system.stop_online_learning()
    
    print("在线学习系统测试完成")


if __name__ == "__main__":
    test_online_learning_system()
