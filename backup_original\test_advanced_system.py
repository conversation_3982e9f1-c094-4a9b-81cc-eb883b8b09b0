"""
测试高级预测系统
使用现有数据测试所有新功能
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict
import os

from neural_predictor import NeuralPredictor
from adaptive_learning_system import AdaptiveLearningSystem
from dynamic_generator import DynamicGenerator
from main import LotteryPredictor
from utils import load_data, parse_numbers, check_hit_2_plus_1


class SimplifiedAdvancedSystem:
    """简化的高级预测系统"""
    
    def __init__(self):
        """初始化系统"""
        print("初始化简化高级预测系统...")
        
        # 核心组件
        self.neural_predictor = NeuralPredictor()
        self.adaptive_system = AdaptiveLearningSystem()
        self.dynamic_generator = DynamicGenerator()
        self.base_predictor = LotteryPredictor()
        
        # 系统状态
        self.is_initialized = False
        self.data = None
        
        # 性能跟踪
        self.performance_metrics = {
            'total_predictions': 0,
            'red_odd_even_hits': 0,
            'red_size_hits': 0,
            'blue_size_hits': 0,
            'hit_2_plus_1': 0
        }
    
    def initialize_system(self, train_neural: bool = True) -> bool:
        """初始化系统"""
        try:
            print("=" * 60)
            print("🚀 简化高级预测系统初始化")
            print("=" * 60)
            
            # 1. 加载数据
            print("\n1. 加载数据...")
            self.data = load_data()
            if len(self.data) == 0:
                print("❌ 无法加载数据")
                return False
            print(f"✅ 数据加载成功: {len(self.data)} 期")
            
            # 2. 训练神经网络（可选）
            if train_neural:
                print("\n2. 训练神经网络...")
                
                # 使用后80%的数据训练
                train_size = int(len(self.data) * 0.8)
                train_data = self.data.iloc[train_size:]
                
                if len(train_data) >= 50:
                    self.neural_predictor.train_models(train_data, lookback=5, epochs=30)
                    print("✅ 神经网络训练完成")
                else:
                    print("⚠️ 训练数据不足，跳过神经网络训练")
            
            # 3. 初始化自适应系统
            print("\n3. 初始化自适应系统...")
            
            # 使用最近20期数据初始化
            recent_data = self.data.head(20)
            for _, row in recent_data.iterrows():
                try:
                    red_balls, blue_balls = parse_numbers(row)
                    self.adaptive_system.update_with_new_result(
                        row['期号'], red_balls, blue_balls
                    )
                except:
                    continue
            print("✅ 自适应系统初始化完成")
            
            self.is_initialized = True
            print("\n✅ 系统初始化完成!")
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    def predict_with_all_methods(self, current_period_index: int = 0) -> Dict:
        """使用所有方法进行预测"""
        if not self.is_initialized:
            raise ValueError("系统尚未初始化")
        
        print(f"\n🎯 多方法预测第 {current_period_index + 1} 期...")
        
        results = {}
        
        # 1. 基础预测器
        try:
            print("1. 基础预测器...")
            base_prediction = self.base_predictor.predict_next_period(current_period_index)
            results['base_predictor'] = base_prediction
            print("   ✅ 基础预测完成")
        except Exception as e:
            print(f"   ❌ 基础预测失败: {e}")
            results['base_predictor'] = None
        
        # 2. 神经网络预测
        try:
            if self.neural_predictor.is_trained:
                print("2. 神经网络预测...")
                recent_data = self.data.iloc[current_period_index:]
                neural_predictions = self.neural_predictor.predict(recent_data, lookback=5)
                results['neural_predictor'] = neural_predictions
                print(f"   ✅ 神经网络预测: {neural_predictions}")
            else:
                print("2. 神经网络未训练，跳过")
                results['neural_predictor'] = None
        except Exception as e:
            print(f"   ❌ 神经网络预测失败: {e}")
            results['neural_predictor'] = None
        
        # 3. 动态生成器
        try:
            print("3. 动态生成器...")
            
            # 使用基础预测的状态，如果没有则使用默认值
            if results['base_predictor']:
                red_odd_even = results['base_predictor'].get('red_odd_even_prediction', ('3:2', 0.5))[0]
                red_size = results['base_predictor'].get('red_size_prediction', ('2:3', 0.5))[0]
                blue_size = results['base_predictor'].get('blue_size_prediction', ('1:1', 0.5))[0]
                kill_numbers = results['base_predictor'].get('kill_numbers', {'red': [], 'blue': []})
            else:
                red_odd_even, red_size, blue_size = '3:2', '2:3', '1:1'
                kill_numbers = {'red': [], 'blue': []}
            
            dynamic_numbers = self.dynamic_generator.generate_dynamic_numbers(
                red_odd_even, red_size, blue_size, kill_numbers, 
                current_period_index, current_period_index
            )
            results['dynamic_generator'] = dynamic_numbers
            print(f"   ✅ 动态生成: {dynamic_numbers}")
        except Exception as e:
            print(f"   ❌ 动态生成失败: {e}")
            results['dynamic_generator'] = None
        
        # 4. 自适应调整
        try:
            print("4. 自适应调整...")
            if results['base_predictor']:
                adaptive_predictions = self.adaptive_system.get_adaptive_predictions(
                    results['base_predictor']
                )
                results['adaptive_system'] = adaptive_predictions
                print("   ✅ 自适应调整完成")
            else:
                results['adaptive_system'] = None
        except Exception as e:
            print(f"   ❌ 自适应调整失败: {e}")
            results['adaptive_system'] = None
        
        return results
    
    def run_comprehensive_test(self, num_periods: int = 10) -> Dict:
        """运行综合测试"""
        print(f"\n🧪 开始综合测试 ({num_periods} 期)...")
        
        if len(self.data) < num_periods + 10:
            raise ValueError("数据不足以进行测试")
        
        test_results = []
        
        for i in range(num_periods):
            try:
                print(f"\n--- 测试第 {i+1}/{num_periods} 期 ---")
                
                # 多方法预测
                predictions = self.predict_with_all_methods(i)
                
                # 获取实际结果
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                
                # 评估各种预测方法
                evaluations = {}
                
                # 评估基础预测器
                if predictions['base_predictor']:
                    base_eval = self._evaluate_prediction(
                        predictions['base_predictor'], actual_red, actual_blue
                    )
                    evaluations['base_predictor'] = base_eval
                
                # 评估动态生成器
                if predictions['dynamic_generator']:
                    dynamic_eval = self._evaluate_numbers(
                        predictions['dynamic_generator'], actual_red, actual_blue
                    )
                    evaluations['dynamic_generator'] = dynamic_eval
                
                test_results.append({
                    'period_index': i,
                    'period_num': actual_row['期号'],
                    'actual': (actual_red, actual_blue),
                    'predictions': predictions,
                    'evaluations': evaluations
                })
                
                # 更新自适应系统
                if predictions['base_predictor']:
                    update_data = {
                        'red_odd_even': predictions['base_predictor'].get('red_odd_even_prediction', ('3:2', 0.5))[0],
                        'red_size': predictions['base_predictor'].get('red_size_prediction', ('2:3', 0.5))[0],
                        'blue_size': predictions['base_predictor'].get('blue_size_prediction', ('1:1', 0.5))[0],
                        'predicted_numbers': predictions['dynamic_generator'] if predictions['dynamic_generator'] else ([1,2,3,4,5], [1,2])
                    }
                    
                    self.adaptive_system.update_with_new_result(
                        actual_row['期号'], actual_red, actual_blue, update_data
                    )
                
            except Exception as e:
                print(f"测试第 {i+1} 期失败: {e}")
                continue
        
        # 计算统计结果
        stats = self._calculate_test_stats(test_results)
        
        print(f"\n✅ 综合测试完成!")
        return {
            'test_results': test_results,
            'statistics': stats,
            'adaptive_performance': self.adaptive_system.get_performance_summary(),
            'adaptive_weights': self.adaptive_system.get_current_weights()
        }
    
    def _evaluate_prediction(self, prediction: Dict, actual_red: List[int], 
                           actual_blue: List[int]) -> Dict:
        """评估预测结果"""
        evaluation = {}
        
        # 状态预测评估
        if 'red_odd_even_prediction' in prediction:
            actual_odd = sum(1 for x in actual_red if x % 2 == 1)
            actual_state = f"{actual_odd}:{5-actual_odd}"
            pred_state = prediction['red_odd_even_prediction'][0]
            evaluation['red_odd_even_hit'] = pred_state == actual_state
        
        if 'red_size_prediction' in prediction:
            actual_small = sum(1 for x in actual_red if x <= 18)
            actual_state = f"{actual_small}:{5-actual_small}"
            pred_state = prediction['red_size_prediction'][0]
            evaluation['red_size_hit'] = pred_state == actual_state
        
        if 'blue_size_prediction' in prediction:
            actual_small = sum(1 for x in actual_blue if x <= 6)
            actual_state = f"{actual_small}:{2-actual_small}"
            pred_state = prediction['blue_size_prediction'][0]
            evaluation['blue_size_hit'] = pred_state == actual_state
        
        # 号码预测评估
        if 'generated_numbers' in prediction:
            pred_red, pred_blue = prediction['generated_numbers']
            red_hits = len(set(pred_red) & set(actual_red))
            blue_hits = len(set(pred_blue) & set(actual_blue))
            
            evaluation['red_hits'] = red_hits
            evaluation['blue_hits'] = blue_hits
            evaluation['hit_2_plus_1'] = check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue))
        
        return evaluation
    
    def _evaluate_numbers(self, predicted_numbers: Tuple[List[int], List[int]], 
                         actual_red: List[int], actual_blue: List[int]) -> Dict:
        """评估号码预测"""
        pred_red, pred_blue = predicted_numbers
        
        red_hits = len(set(pred_red) & set(actual_red))
        blue_hits = len(set(pred_blue) & set(actual_blue))
        
        return {
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'hit_2_plus_1': check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue)),
            'total_hits': red_hits + blue_hits
        }
    
    def _calculate_test_stats(self, test_results: List[Dict]) -> Dict:
        """计算测试统计"""
        if not test_results:
            return {}
        
        stats = {}
        
        # 基础预测器统计
        base_evals = [r['evaluations'].get('base_predictor', {}) for r in test_results if 'base_predictor' in r['evaluations']]
        if base_evals:
            stats['base_predictor'] = {
                'red_odd_even_rate': np.mean([e.get('red_odd_even_hit', 0) for e in base_evals]),
                'red_size_rate': np.mean([e.get('red_size_hit', 0) for e in base_evals]),
                'blue_size_rate': np.mean([e.get('blue_size_hit', 0) for e in base_evals]),
                'hit_2_plus_1_rate': np.mean([e.get('hit_2_plus_1', 0) for e in base_evals]),
                'avg_red_hits': np.mean([e.get('red_hits', 0) for e in base_evals]),
                'avg_blue_hits': np.mean([e.get('blue_hits', 0) for e in base_evals])
            }
        
        # 动态生成器统计
        dynamic_evals = [r['evaluations'].get('dynamic_generator', {}) for r in test_results if 'dynamic_generator' in r['evaluations']]
        if dynamic_evals:
            stats['dynamic_generator'] = {
                'hit_2_plus_1_rate': np.mean([e.get('hit_2_plus_1', 0) for e in dynamic_evals]),
                'avg_red_hits': np.mean([e.get('red_hits', 0) for e in dynamic_evals]),
                'avg_blue_hits': np.mean([e.get('blue_hits', 0) for e in dynamic_evals]),
                'avg_total_hits': np.mean([e.get('total_hits', 0) for e in dynamic_evals])
            }
        
        return stats


def main():
    """主函数"""
    system = SimplifiedAdvancedSystem()
    
    # 初始化系统
    if not system.initialize_system(train_neural=True):
        print("系统初始化失败")
        return
    
    # 运行综合测试
    try:
        results = system.run_comprehensive_test(num_periods=10)
        
        print("\n" + "=" * 60)
        print("📊 综合测试结果")
        print("=" * 60)
        
        # 显示统计结果
        stats = results['statistics']
        
        if 'base_predictor' in stats:
            print("\n🔹 基础预测器性能:")
            base_stats = stats['base_predictor']
            for key, value in base_stats.items():
                if 'rate' in key:
                    print(f"  {key}: {value:.1%}")
                else:
                    print(f"  {key}: {value:.2f}")
        
        if 'dynamic_generator' in stats:
            print("\n🔹 动态生成器性能:")
            dynamic_stats = stats['dynamic_generator']
            for key, value in dynamic_stats.items():
                if 'rate' in key:
                    print(f"  {key}: {value:.1%}")
                else:
                    print(f"  {key}: {value:.2f}")
        
        # 显示自适应系统性能
        adaptive_perf = results['adaptive_performance']
        if adaptive_perf:
            print("\n🔹 自适应系统性能:")
            for metric, data in adaptive_perf.items():
                print(f"  {metric}: 最近平均 {data['recent_avg']:.1%}, 总体平均 {data['overall_avg']:.1%}")
        
        # 显示当前权重
        weights = results['adaptive_weights']
        print(f"\n🔹 当前特征权重:")
        for feature, weight in weights.items():
            print(f"  {feature}: {weight:.3f}")
        
        print("\n✅ 高级预测系统测试完成!")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
