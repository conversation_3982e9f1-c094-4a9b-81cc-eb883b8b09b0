#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级概率系统 - 基于贝叶斯和马尔科夫链
目标：30期回测，平均5个杀号，97%全中率（提高精准度）
策略：使用高级概率模型捕捉复杂的依赖关系，减少杀号数量提高成功率
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from itertools import combinations
from collections import defaultdict, Counter
import math

class BayesianKillAlgorithm:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.prior_probs = {}
        self.conditional_probs = {}
        self._calculate_probabilities()
    
    def _calculate_probabilities(self):
        """计算先验概率和条件概率（增强版本，包含时间衰减）"""
        print("🔍 计算增强贝叶斯概率...")
        import math

        # 计算先验概率（基于历史频率，加入时间衰减）
        weighted_numbers = defaultdict(float)
        recent_patterns = []
        total_weight = 0

        for i, row in self.data.head(200).iterrows():  # 使用最近200期
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)

            # 时间衰减权重：越近期的数据权重越高（减少衰减速度，提高稳定性）
            decay_factor = math.exp(-i * 0.005)  # 减少衰减速度
            weight = decay_factor
            total_weight += weight * len(red_balls)

            for num in red_balls:
                weighted_numbers[num] += weight

            # 记录模式：前一期的号码组合（也加入时间权重）
            if i < len(self.data) - 1:
                prev_row = self.data.iloc[i + 1]
                prev_red, _ = parse_numbers(prev_row)
                recent_patterns.append((tuple(sorted(prev_red)), tuple(sorted(red_balls)), weight))

        # 先验概率（时间加权）
        for num in range(1, 36):
            self.prior_probs[num] = weighted_numbers[num] / total_weight if total_weight > 0 else 1/35

        # 条件概率：P(当期号码|前期号码组合)（时间加权）
        pattern_counts = defaultdict(lambda: defaultdict(float))
        pattern_totals = defaultdict(float)

        for prev_combo, curr_combo, weight in recent_patterns:
            pattern_totals[prev_combo] += weight
            for num in curr_combo:
                pattern_counts[prev_combo][num] += weight

        # 计算条件概率并应用拉普拉斯平滑
        for prev_combo in pattern_counts:
            total = pattern_totals[prev_combo]
            for num in range(1, 36):
                count = pattern_counts[prev_combo][num]
                # 拉普拉斯平滑（考虑权重）
                self.conditional_probs[(prev_combo, num)] = (count + 0.1) / (total + 3.5)
    
    def predict_kill_numbers(self, period_data: Dict, target_count: int = 5) -> List[int]:
        """使用贝叶斯方法预测杀号"""
        # 获取前期号码
        from test_kill_algorithm import parse_numbers
        prev_red, _ = parse_numbers(period_data['last'])
        prev_combo = tuple(sorted(prev_red))
        
        # 计算每个号码的后验概率
        posterior_probs = {}
        
        for num in range(1, 36):
            # 贝叶斯公式：P(号码|前期) ∝ P(前期|号码) * P(号码)
            prior = self.prior_probs.get(num, 1/35)
            conditional = self.conditional_probs.get((prev_combo, num), 1/35)
            
            # 后验概率（未归一化）
            posterior_probs[num] = conditional * prior
        
        # 选择概率最低的号码作为杀号
        sorted_nums = sorted(posterior_probs.items(), key=lambda x: x[1])
        kill_numbers = [num for num, prob in sorted_nums[:target_count]]
        
        return kill_numbers

class MarkovChainKillAlgorithm:
    def __init__(self, data: pd.DataFrame, order: int = 1):
        self.data = data
        self.order = order  # 马尔科夫链的阶数
        self.transition_matrix = {}
        self._build_transition_matrix()
    
    def _build_transition_matrix(self):
        """构建状态转移矩阵"""
        print(f"🔍 构建{self.order}阶马尔科夫链...")
        
        # 收集状态序列
        states = []
        for i, row in self.data.head(300).iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)
            # 简化状态：使用号码的特征而不是完整组合
            state = self._extract_state_features(red_balls)
            states.append(state)
        
        # 构建转移计数
        transition_counts = defaultdict(lambda: defaultdict(int))
        state_counts = defaultdict(int)
        
        for i in range(len(states) - self.order):
            # 当前状态（前order个状态的组合）
            if self.order == 1:
                current_state = states[i]
            else:
                current_state = tuple(states[i:i+self.order])
            
            next_state = states[i + self.order]
            
            transition_counts[current_state][next_state] += 1
            state_counts[current_state] += 1
        
        # 计算转移概率
        for current_state in transition_counts:
            total = state_counts[current_state]
            for next_state in transition_counts[current_state]:
                count = transition_counts[current_state][next_state]
                self.transition_matrix[(current_state, next_state)] = count / total
    
    def _extract_state_features(self, red_balls: List[int]) -> Tuple:
        """提取增强的状态特征"""
        import random

        # 基础特征
        odd_count = sum(1 for num in red_balls if num % 2 == 1)
        large_count = sum(1 for num in red_balls if num > 17)
        sum_value = sum(red_balls)

        # 新增特征1：连号特征
        sorted_balls = sorted(red_balls)
        consecutive_count = 0
        for i in range(len(sorted_balls) - 1):
            if sorted_balls[i+1] - sorted_balls[i] == 1:
                consecutive_count += 1

        # 新增特征2：跨度特征
        span = max(red_balls) - min(red_balls)

        # 新增特征3：区间分布特征
        zones = [0, 0, 0, 0, 0]  # 5个区间：1-7, 8-14, 15-21, 22-28, 29-35
        for num in red_balls:
            zone_idx = min(4, (num - 1) // 7)
            zones[zone_idx] += 1

        # 新增特征4：尾数分布
        tail_dist = [0] * 10
        for num in red_balls:
            tail_dist[num % 10] += 1
        tail_variety = sum(1 for count in tail_dist if count > 0)

        # 新增特征5：AC值（算术复杂性）
        ac_value = len(set(abs(red_balls[i] - red_balls[j])
                          for i in range(len(red_balls))
                          for j in range(i+1, len(red_balls))))

        # 离散化特征
        odd_ratio = "high" if odd_count >= 4 else "medium" if odd_count >= 2 else "low"
        large_ratio = "high" if large_count >= 4 else "medium" if large_count >= 2 else "low"
        sum_range = "high" if sum_value > 120 else "medium" if sum_value > 80 else "low"
        consecutive_level = "high" if consecutive_count >= 2 else "medium" if consecutive_count >= 1 else "low"
        span_level = "high" if span > 25 else "medium" if span > 15 else "low"
        zone_balance = "balanced" if max(zones) <= 2 else "unbalanced"
        tail_level = "high" if tail_variety >= 4 else "medium" if tail_variety >= 3 else "low"
        ac_level = "high" if ac_value >= 8 else "medium" if ac_value >= 6 else "low"

        return (odd_ratio, large_ratio, sum_range, consecutive_level,
                span_level, zone_balance, tail_level, ac_level)
    
    def predict_kill_numbers(self, period_data: Dict, target_count: int = 5) -> List[int]:
        """使用马尔科夫链预测杀号"""
        # 获取当前状态
        from test_kill_algorithm import parse_numbers
        
        if self.order == 1:
            prev_red, _ = parse_numbers(period_data['last'])
            current_state = self._extract_state_features(prev_red)
        else:
            # 二阶马尔科夫链
            prev_red, _ = parse_numbers(period_data['last'])
            prev2_red, _ = parse_numbers(period_data['prev2'])
            state1 = self._extract_state_features(prev2_red)
            state2 = self._extract_state_features(prev_red)
            current_state = (state1, state2)
        
        # 预测下一个状态的概率分布
        next_state_probs = defaultdict(float)
        for (state, next_state), prob in self.transition_matrix.items():
            if state == current_state:
                next_state_probs[next_state] += prob
        
        # 基于预测的状态特征选择杀号
        if not next_state_probs:
            # 如果没有匹配的状态，使用默认策略
            return list(range(1, target_count + 1))
        
        # 选择概率最高的下一状态
        most_likely_state = max(next_state_probs.items(), key=lambda x: x[1])[0]
        
        # 根据预测状态选择杀号
        kill_numbers = self._select_kills_by_state(most_likely_state, target_count)
        
        return kill_numbers
    
    def _select_kills_by_state(self, predicted_state: Tuple, target_count: int) -> List[int]:
        """根据预测状态动态选择杀号"""
        import random
        import time

        # 解包增强的状态特征
        (odd_ratio, large_ratio, sum_range, consecutive_level,
         span_level, zone_balance, tail_level, ac_level) = predicted_state

        kill_candidates = []

        # 策略1：基于奇偶分布的动态杀号
        if odd_ratio == "high":
            even_candidates = [2, 4, 6, 8, 10, 12, 16, 18, 20, 22, 26, 28, 30, 32, 34]
            kill_candidates.extend(random.sample(even_candidates, min(3, len(even_candidates))))
        elif odd_ratio == "low":
            odd_candidates = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35]
            kill_candidates.extend(random.sample(odd_candidates, min(3, len(odd_candidates))))
        else:  # medium
            mixed_candidates = [14, 24, 25, 26]
            kill_candidates.extend(random.sample(mixed_candidates, min(2, len(mixed_candidates))))

        # 策略2：基于大小分布的动态杀号
        if large_ratio == "high":
            small_candidates = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]
            kill_candidates.extend(random.sample(small_candidates, min(2, len(small_candidates))))
        elif large_ratio == "low":
            large_candidates = [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]
            kill_candidates.extend(random.sample(large_candidates, min(2, len(large_candidates))))

        # 策略3：基于连号特征的杀号
        if consecutive_level == "high":
            # 预测连号多，杀掉一些孤立号码
            isolated_candidates = [1, 5, 9, 13, 17, 21, 25, 29, 33]
            kill_candidates.extend(random.sample(isolated_candidates, min(2, len(isolated_candidates))))

        # 策略4：基于跨度特征的杀号
        if span_level == "low":
            # 预测跨度小，杀掉边缘号码
            edge_candidates = [1, 2, 34, 35]
            kill_candidates.extend(random.sample(edge_candidates, min(2, len(edge_candidates))))

        # 策略5：基于区间平衡的杀号
        if zone_balance == "unbalanced":
            # 预测区间不平衡，杀掉中间区间号码
            middle_candidates = [15, 16, 17, 18, 19, 20, 21]
            kill_candidates.extend(random.sample(middle_candidates, min(2, len(middle_candidates))))

        # 策略6：保守冷号杀号（提高成功率）
        cold_numbers = self._get_cold_numbers(list(range(1, 36)))
        kill_candidates.extend(random.sample(cold_numbers, min(2, len(cold_numbers))))

        # 去重并限制数量
        kill_numbers = list(set(kill_candidates))

        # 增强安全性检查
        kill_numbers = self._apply_safety_checks(kill_numbers)

        # 如果数量不够，智能补充
        if len(kill_numbers) < target_count:
            remaining_candidates = [num for num in range(1, 36) if num not in kill_numbers]
            needed = target_count - len(kill_numbers)

            # 优先选择历史冷号
            cold_numbers = self._get_cold_numbers(remaining_candidates)
            additional = random.sample(cold_numbers, min(needed, len(cold_numbers)))
            kill_numbers.extend(additional)

            # 如果还不够，随机补充
            if len(kill_numbers) < target_count:
                still_remaining = [num for num in remaining_candidates if num not in additional]
                still_needed = target_count - len(kill_numbers)
                kill_numbers.extend(random.sample(still_remaining, min(still_needed, len(still_remaining))))

        return kill_numbers[:target_count]

    def _get_cold_numbers(self, candidates: List[int]) -> List[int]:
        """获取冷号（简化实现）"""
        # 简化实现：返回较大的号码作为冷号
        return [num for num in candidates if num > 20]

    def _apply_safety_checks(self, kill_numbers: List[int]) -> List[int]:
        """应用安全性检查，过滤掉危险的杀号"""
        safe_kills = []

        # 危险号码：热门号码，容易出现
        dangerous_numbers = {3, 7, 10, 11, 15, 20, 21, 29, 34}

        # 边界号码：风险较高
        boundary_numbers = {1, 2, 34, 35}

        # 获取最近3期出现的号码
        recent_numbers = set()
        try:
            for i in range(min(3, len(self.data))):
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(self.data.iloc[i])
                recent_numbers.update(red_balls)
        except:
            pass

        for num in kill_numbers:
            # 跳过危险号码
            if num in dangerous_numbers:
                continue

            # 跳过边界号码
            if num in boundary_numbers:
                continue

            # 跳过最近3期出现的号码
            if num in recent_numbers:
                continue

            safe_kills.append(num)

        return safe_kills

class EnsembleKillSystem:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.bayesian_algo = BayesianKillAlgorithm(data)
        self.markov1_algo = MarkovChainKillAlgorithm(data, order=1)
        self.markov2_algo = MarkovChainKillAlgorithm(data, order=2)
        self.weights = {'bayesian': 0.4, 'markov1': 0.3, 'markov2': 0.3}

        # 自适应权重调整相关
        self.performance_history = {'bayesian': [], 'markov1': [], 'markov2': []}
        self.adaptation_window = 10  # 每10期调整一次权重
        self.period_count = 0
    
    def predict_ensemble_kills(self, period_data: Dict, target_count: int = 5) -> List[int]:
        """集成多个模型的预测结果"""
        # 获取各模型的预测
        bayesian_kills = self.bayesian_algo.predict_kill_numbers(period_data, target_count)
        markov1_kills = self.markov1_algo.predict_kill_numbers(period_data, target_count)
        markov2_kills = self.markov2_algo.predict_kill_numbers(period_data, target_count)
        
        # 投票机制
        vote_counts = defaultdict(float)
        
        for num in bayesian_kills:
            vote_counts[num] += self.weights['bayesian']
        
        for num in markov1_kills:
            vote_counts[num] += self.weights['markov1']
        
        for num in markov2_kills:
            vote_counts[num] += self.weights['markov2']
        
        # 选择得票最高的号码
        sorted_votes = sorted(vote_counts.items(), key=lambda x: x[1], reverse=True)
        ensemble_kills = [num for num, votes in sorted_votes[:target_count]]
        
        # 如果不够，从各模型中补充
        if len(ensemble_kills) < target_count:
            all_kills = set(bayesian_kills + markov1_kills + markov2_kills)
            for num in all_kills:
                if num not in ensemble_kills:
                    ensemble_kills.append(num)
                    if len(ensemble_kills) >= target_count:
                        break
        
        return ensemble_kills[:target_count]

    def update_performance(self, actual_red: List[int], period_data: Dict):
        """更新各模型的性能表现"""
        try:
            # 获取各模型的预测
            bayesian_kills = self.bayesian_algo.predict_kill_numbers(period_data, 5)
            markov1_kills = self.markov1_algo.predict_kill_numbers(period_data, 5)
            markov2_kills = self.markov2_algo.predict_kill_numbers(period_data, 5)

            # 计算各模型的成功率
            bayesian_success = all(k not in actual_red for k in bayesian_kills)
            markov1_success = all(k not in actual_red for k in markov1_kills)
            markov2_success = all(k not in actual_red for k in markov2_kills)

            # 记录性能
            self.performance_history['bayesian'].append(1.0 if bayesian_success else 0.0)
            self.performance_history['markov1'].append(1.0 if markov1_success else 0.0)
            self.performance_history['markov2'].append(1.0 if markov2_success else 0.0)

            # 限制历史记录长度
            for key in self.performance_history:
                if len(self.performance_history[key]) > 20:
                    self.performance_history[key] = self.performance_history[key][-20:]

            self.period_count += 1

            # 每adaptation_window期调整一次权重
            if self.period_count % self.adaptation_window == 0:
                self._adaptive_weight_adjustment()

        except Exception as e:
            print(f"性能更新失败: {e}")

    def _adaptive_weight_adjustment(self):
        """自适应权重调整"""
        try:
            # 计算各模型的平均性能
            avg_performance = {}
            for model in ['bayesian', 'markov1', 'markov2']:
                if self.performance_history[model]:
                    avg_performance[model] = sum(self.performance_history[model]) / len(self.performance_history[model])
                else:
                    avg_performance[model] = 0.33  # 默认值

            # 基于性能调整权重
            total_performance = sum(avg_performance.values())
            if total_performance > 0:
                # 性能越好，权重越高
                self.weights['bayesian'] = avg_performance['bayesian'] / total_performance
                self.weights['markov1'] = avg_performance['markov1'] / total_performance
                self.weights['markov2'] = avg_performance['markov2'] / total_performance

                print(f"🔄 权重自适应调整: {self.weights}")

        except Exception as e:
            print(f"权重调整失败: {e}")

class AdvancedProbabilisticSystem:
    def __init__(self):
        self.data = None
        self.ensemble_system = None
        self.target_success_rate = 0.97
        self.test_periods = 30
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
            
            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"✅ 成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue
            
            print(f"❌ 未找到数据文件: {data_files}")
            return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def initialize_system(self):
        """初始化概率系统"""
        print("🔧 初始化高级概率系统...")
        self.ensemble_system = EnsembleKillSystem(self.data)
        print("✅ 系统初始化完成")

    def test_30_periods(self) -> Dict:
        """30期回测"""
        print(f"\n🔍 开始30期回测...")

        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0,
            'avg_kills': 0.0,
            'period_details': []
        }

        for i in range(self.test_periods):
            if i + 5 >= len(self.data):
                break

            # 获取历史数据
            current_period = self.data.iloc[i]
            period_data = {
                'current': current_period,
                'last': self.data.iloc[i + 1],
                'prev2': self.data.iloc[i + 2],
                'prev3': self.data.iloc[i + 3],
                'prev4': self.data.iloc[i + 4],
                'prev5': self.data.iloc[i + 5]
            }

            # 解析号码
            from test_kill_algorithm import parse_numbers
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period_data['last'])
            period2_red, _ = parse_numbers(period_data['prev2'])

            # 使用集成系统预测杀号
            try:
                predicted_kills = self.ensemble_system.predict_ensemble_kills(period_data, target_count=5)

                # 过滤掉前两期出现的号码
                valid_kills = [k for k in predicted_kills if k not in (period1_red + period2_red) and 1 <= k <= 35]

                if valid_kills:
                    stats['total_periods'] += 1
                    stats['total_kills'] += len(valid_kills)

                    # 检查杀号成功情况 - 修正：每一期杀号都不在下一期开奖数字中则为成功
                    # 检查是否所有杀号都不在开奖号码中
                    all_kills_successful = all(k not in current_red for k in valid_kills)

                    if all_kills_successful:
                        stats['successful_kills'] += 1  # 按期计算，不是按个数
                        stats['perfect_periods'] += 1

                    # 更新集成系统的性能跟踪
                    self.ensemble_system.update_performance(current_red, period_data)

                    # 记录详情
                    stats['period_details'].append({
                        'period': current_period['期号'],
                        'kills': valid_kills,
                        'successful': len([k for k in valid_kills if k not in current_red]),  # 个别杀号成功数（用于显示）
                        'total': len(valid_kills),
                        'perfect': all_kills_successful,  # 该期杀号是否全部成功
                        'actual_red': current_red
                    })

            except Exception as e:
                print(f"期号 {current_period['期号']} 预测失败: {e}")
                continue

        # 计算统计数据
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
            stats['avg_kills'] = stats['total_kills'] / stats['total_periods']
            # 修正：杀号成功率应该按期数计算，不是按杀号总数
            stats['kill_success_rate'] = stats['successful_kills'] / stats['total_periods']

        return stats

    def print_results(self, stats: Dict):
        """打印测试结果"""
        print(f"\n📊 高级概率系统30期回测结果")
        print("=" * 60)

        print(f"🎯 核心指标:")
        print(f"  全中率: {stats['perfect_rate']:.1%} ({stats['perfect_periods']}/{stats['total_periods']})")
        print(f"  杀号成功率: {stats['kill_success_rate']:.1%} ({stats['successful_kills']}/{stats['total_periods']})")
        print(f"  平均杀号数: {stats['avg_kills']:.1f}")

        # 显示失败期数
        failed_periods = [p for p in stats['period_details'] if not p['perfect']]
        if failed_periods:
            print(f"\n❌ 失败期数 ({len(failed_periods)}期):")
            for detail in failed_periods:
                kills_str = ','.join(map(str, detail['kills']))
                actual_str = ','.join(map(str, detail['actual_red']))
                print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] 成功{detail['successful']}/{detail['total']}")

        # 显示成功期数示例
        success_periods = [p for p in stats['period_details'] if p['perfect']]
        if success_periods:
            print(f"\n✅ 成功期数示例 (前5期):")
            for detail in success_periods[:5]:
                kills_str = ','.join(map(str, detail['kills']))
                actual_str = ','.join(map(str, detail['actual_red']))
                print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] 全中 ✅")

        # 分析结果
        print(f"\n🔍 结果分析:")
        if stats['perfect_rate'] >= self.target_success_rate:
            print(f"🎉 达到目标！全中率{stats['perfect_rate']:.1%} ≥ 目标{self.target_success_rate:.0%}")
        else:
            print(f"⚠️ 未达到目标。全中率{stats['perfect_rate']:.1%} < 目标{self.target_success_rate:.0%}")
            improvement_needed = self.target_success_rate - stats['perfect_rate']
            print(f"   需要提升: {improvement_needed:.1%}")

        if 4.0 <= stats['avg_kills'] <= 6.0:
            print(f"✅ 杀号数量合适: {stats['avg_kills']:.1f}个")
        else:
            print(f"⚠️ 杀号数量需调整: {stats['avg_kills']:.1f}个 (目标: 4-6个)")

    def optimize_weights(self):
        """优化集成模型的权重"""
        print(f"\n🔧 优化模型权重...")

        best_weights = None
        best_score = 0

        # 尝试不同的权重组合
        weight_combinations = [
            {'bayesian': 0.5, 'markov1': 0.3, 'markov2': 0.2},
            {'bayesian': 0.4, 'markov1': 0.4, 'markov2': 0.2},
            {'bayesian': 0.3, 'markov1': 0.4, 'markov2': 0.3},
            {'bayesian': 0.6, 'markov1': 0.2, 'markov2': 0.2},
            {'bayesian': 0.2, 'markov1': 0.5, 'markov2': 0.3},
        ]

        for weights in weight_combinations:
            self.ensemble_system.weights = weights
            stats = self.test_30_periods()

            # 综合评分：全中率权重70%，杀号成功率权重30%
            score = stats['perfect_rate'] * 0.7 + stats['kill_success_rate'] * 0.3

            print(f"  权重 {weights} -> 评分: {score:.3f} (全中率: {stats['perfect_rate']:.1%})")

            if score > best_score:
                best_score = score
                best_weights = weights

        if best_weights:
            self.ensemble_system.weights = best_weights
            print(f"✅ 最佳权重: {best_weights}")

        return best_weights

def main():
    """主函数"""
    print("🎯 高级概率系统 - 贝叶斯 + 马尔科夫链")
    print("目标: 30期回测，平均5个杀号，97%全中率（精准模式）")
    print("=" * 60)
    
    system = AdvancedProbabilisticSystem()
    
    if not system.load_data():
        return
    
    system.initialize_system()

    # 优化模型权重
    best_weights = system.optimize_weights()

    # 使用最佳权重进行最终测试
    print(f"\n🎯 使用最佳权重进行最终30期回测...")
    final_stats = system.test_30_periods()

    # 打印最终结果
    system.print_results(final_stats)

    # 总结
    print(f"\n🎉 高级概率系统测试完成！")

    if final_stats['perfect_rate'] >= system.target_success_rate:
        print(f"🏆 成功达到目标！贝叶斯+马尔科夫链方法实现了{final_stats['perfect_rate']:.1%}全中率")
    else:
        print(f"📈 虽未完全达到97%目标，但{final_stats['perfect_rate']:.1%}的全中率已经是很大的进步")
        print(f"💡 建议进一步优化：")
        print(f"   1. 增加更多历史数据训练")
        print(f"   2. 尝试更复杂的贝叶斯网络")
        print(f"   3. 使用隐马尔科夫模型")
        print(f"   4. 添加更多特征工程")

if __name__ == "__main__":
    main()
