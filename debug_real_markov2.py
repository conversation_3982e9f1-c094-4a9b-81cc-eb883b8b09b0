#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实数据调试马尔可夫链2为什么输出[1,2,3,4,5]
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from advanced_probabilistic_system import AdvancedProbabilisticSystem

def debug_real_markov2():
    """使用真实数据调试马尔可夫链2"""
    print("🔍 使用真实数据调试马尔可夫链2")
    print("=" * 60)
    
    # 初始化系统
    advanced_system = AdvancedProbabilisticSystem()
    
    if not advanced_system.load_data():
        print("❌ 数据加载失败")
        return
    
    # 测试期号25068
    period = "25068"
    period_index = None
    for i, row in advanced_system.data.iterrows():
        if str(row['期号']) == str(period):
            period_index = i
            break
    
    if period_index is None:
        print(f"❌ 未找到期号 {period}")
        return
    
    # 获取训练数据
    train_data = advanced_system.data.iloc[period_index + 1:period_index + 301]
    
    # 构建period_data
    period_data = {
        'current': train_data.iloc[0],
        'last': train_data.iloc[1],
        'prev2': train_data.iloc[2],
        'prev3': train_data.iloc[3],
        'prev4': train_data.iloc[4],
        'prev5': train_data.iloc[5]
    }
    
    print(f"📅 调试期号: {period}")
    
    # 初始化系统
    advanced_system.data = train_data
    advanced_system.initialize_system()
    
    # 获取马尔可夫链2算法
    markov2 = advanced_system.ensemble_system.markov2_algo
    
    print(f"\n🔧 真实马尔可夫链2预测过程:")
    
    # 手动执行predict_kill_numbers的逻辑
    from src.utils.utils import parse_numbers
    from collections import defaultdict
    
    # 获取当前状态
    current_red, _ = parse_numbers(period_data['current'])
    prev_red, _ = parse_numbers(period_data['last'])
    
    print(f"  当前期红球: {current_red}")
    print(f"  上期红球: {prev_red}")
    
    # 计算状态特征
    current_features = markov2._extract_state_features(current_red)
    prev_features = markov2._extract_state_features(prev_red)
    
    print(f"  当前期特征: {current_features}")
    print(f"  上期特征: {prev_features}")
    
    # 构建状态键
    if markov2.order == 2:
        state_key = (prev_features, current_features)
    else:
        state_key = current_features
    
    print(f"  状态键: {state_key}")
    
    # 查找转移状态
    next_state_probs = defaultdict(float)
    for (state, next_state), prob in markov2.transition_matrix.items():
        if state == state_key:
            next_state_probs[next_state] += prob
    
    if next_state_probs:
        print(f"  找到{len(next_state_probs)}个转移状态")
        most_likely_state = max(next_state_probs.items(), key=lambda x: x[1])[0]
        print(f"  选择状态: {most_likely_state}")
    else:
        print(f"  ❌ 未找到转移状态，使用默认状态")
        most_likely_state = markov2._extract_state_features([1, 2, 3, 4, 5])
        print(f"  默认状态: {most_likely_state}")
    
    # 调用_select_kills_by_state方法
    print(f"\n🎯 调用_select_kills_by_state:")
    print(f"  输入状态: {most_likely_state}")
    
    # 手动模拟_select_kills_by_state的逻辑
    (odd_ratio, large_ratio, sum_range, consecutive_level,
     span_level, zone_balance, tail_level, ac_level) = most_likely_state
    
    print(f"  状态解包:")
    print(f"    奇偶比: {odd_ratio}")
    print(f"    大小比: {large_ratio}")
    print(f"    和值范围: {sum_range}")
    print(f"    连号级别: {consecutive_level}")
    print(f"    跨度级别: {span_level}")
    print(f"    区间平衡: {zone_balance}")
    print(f"    尾数级别: {tail_level}")
    print(f"    AC值级别: {ac_level}")
    
    # 实际调用方法
    kill_result = markov2._select_kills_by_state(most_likely_state, 5)
    print(f"  实际输出: {kill_result}")
    
    # 多次测试
    print(f"\n🎲 多次测试:")
    for i in range(5):
        test_result = markov2._select_kills_by_state(most_likely_state, 5)
        print(f"  第{i+1}次: {test_result}")
    
    # 完整的predict_kill_numbers调用
    print(f"\n🔄 完整predict_kill_numbers调用:")
    full_result = markov2.predict_kill_numbers(period_data, target_count=5)
    print(f"  完整结果: {full_result}")
    
    print(f"\n" + "=" * 60)
    print("🎉 真实马尔可夫链2调试完成！")

if __name__ == "__main__":
    debug_real_markov2()
