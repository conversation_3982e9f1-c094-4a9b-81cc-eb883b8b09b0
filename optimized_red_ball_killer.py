#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的红球杀号算法
基于多维度分析和动态权重调整的高精度红球杀号系统
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import defaultdict, Counter
import math

class OptimizedRedBallKiller:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.frequency_analyzer = RedBallFrequencyAnalyzer(data)
        self.pattern_analyzer = RedBallPatternAnalyzer(data)
        self.position_analyzer = RedBallPositionAnalyzer(data)
        self.trend_analyzer = RedBallTrendAnalyzer(data)
        self.enhanced_bayesian = EnhancedRedBallBayesian(data)
        
        # 动态权重配置
        self.weights = {
            'frequency': 0.25,
            'pattern': 0.25,
            'position': 0.20,
            'trend': 0.15,
            'bayesian': 0.15
        }
    
    def calculate_optimized_red_kills(self, recent_periods: List[List[int]], target_count: int = 13) -> List[int]:
        """
        计算优化的红球杀号
        
        Args:
            recent_periods: 最近几期的红球号码
            target_count: 目标杀号数量
            
        Returns:
            List[int]: 优化的杀号列表
        """
        if len(recent_periods) < 2:
            return list(range(1, target_count + 1))
        
        # 获取各分析器的杀号建议
        frequency_kills = self.frequency_analyzer.get_frequency_kills(recent_periods, target_count)
        pattern_kills = self.pattern_analyzer.get_pattern_kills(recent_periods, target_count)
        position_kills = self.position_analyzer.get_position_kills(recent_periods, target_count)
        trend_kills = self.trend_analyzer.get_trend_kills(recent_periods, target_count)
        bayesian_kills = self.enhanced_bayesian.get_bayesian_kills(recent_periods, target_count)
        
        # 动态调整权重
        adjusted_weights = self._adjust_weights_dynamically(recent_periods)
        
        # 综合评分
        kill_scores = defaultdict(float)
        
        # 频率分析评分
        for i, num in enumerate(frequency_kills):
            kill_scores[num] += adjusted_weights['frequency'] * (target_count - i) / target_count
        
        # 模式分析评分
        for i, num in enumerate(pattern_kills):
            kill_scores[num] += adjusted_weights['pattern'] * (target_count - i) / target_count
        
        # 位置分析评分
        for i, num in enumerate(position_kills):
            kill_scores[num] += adjusted_weights['position'] * (target_count - i) / target_count
        
        # 趋势分析评分
        for i, num in enumerate(trend_kills):
            kill_scores[num] += adjusted_weights['trend'] * (target_count - i) / target_count
        
        # 贝叶斯分析评分
        for i, num in enumerate(bayesian_kills):
            kill_scores[num] += adjusted_weights['bayesian'] * (target_count - i) / target_count
        
        # 应用过滤规则
        filtered_scores = self._apply_filtering_rules(kill_scores, recent_periods)
        
        # 选择得分最高的杀号
        sorted_kills = sorted(filtered_scores.items(), key=lambda x: x[1], reverse=True)
        final_kills = [num for num, score in sorted_kills[:target_count]]
        
        return final_kills
    
    def _adjust_weights_dynamically(self, recent_periods: List[List[int]]) -> Dict[str, float]:
        """动态调整权重"""
        adjusted_weights = self.weights.copy()
        
        # 根据最近期数的特征调整权重
        if len(recent_periods) >= 3:
            # 分析最近3期的稳定性
            stability = self._calculate_stability(recent_periods[:3])
            
            if stability > 0.7:  # 高稳定性，增加频率分析权重
                adjusted_weights['frequency'] += 0.1
                adjusted_weights['pattern'] -= 0.05
                adjusted_weights['trend'] -= 0.05
            elif stability < 0.3:  # 低稳定性，增加趋势分析权重
                adjusted_weights['trend'] += 0.1
                adjusted_weights['frequency'] -= 0.05
                adjusted_weights['pattern'] -= 0.05
        
        return adjusted_weights
    
    def _calculate_stability(self, periods: List[List[int]]) -> float:
        """计算期数稳定性"""
        if len(periods) < 2:
            return 0.5
        
        overlaps = []
        for i in range(len(periods) - 1):
            overlap = len(set(periods[i]) & set(periods[i + 1]))
            overlaps.append(overlap / 6)  # 6个红球
        
        return sum(overlaps) / len(overlaps)
    
    def _apply_filtering_rules(self, kill_scores: Dict[int, float], recent_periods: List[List[int]]) -> Dict[int, float]:
        """应用过滤规则"""
        filtered_scores = {}
        
        # 获取前两期号码，避免杀掉刚出现的号码
        recent_numbers = set()
        if len(recent_periods) >= 2:
            recent_numbers.update(recent_periods[0])
            recent_numbers.update(recent_periods[1])
        
        for num, score in kill_scores.items():
            # 基本范围检查
            if not (1 <= num <= 35):
                continue
            
            # 避免杀掉前两期出现的号码
            if num in recent_numbers:
                score *= 0.3  # 大幅降低评分但不完全排除
            
            filtered_scores[num] = score
        
        return filtered_scores

class RedBallFrequencyAnalyzer:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self._analyze_frequencies()
    
    def _analyze_frequencies(self):
        """分析红球频率"""
        self.frequencies = Counter()
        self.recent_frequencies = Counter()
        
        # 全历史频率
        for i, row in self.data.iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)
            self.frequencies.update(red_balls)
        
        # 最近50期频率
        for i, row in self.data.head(50).iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)
            self.recent_frequencies.update(red_balls)
    
    def get_frequency_kills(self, recent_periods: List[List[int]], target_count: int) -> List[int]:
        """基于频率分析的杀号"""
        # 综合历史频率和近期频率
        combined_scores = {}
        
        for num in range(1, 36):
            historical_freq = self.frequencies.get(num, 0)
            recent_freq = self.recent_frequencies.get(num, 0)
            
            # 综合评分：历史频率权重60%，近期频率权重40%
            combined_scores[num] = historical_freq * 0.6 + recent_freq * 0.4
        
        # 选择频率最低的号码作为杀号
        sorted_nums = sorted(combined_scores.items(), key=lambda x: x[1])
        return [num for num, freq in sorted_nums[:target_count]]

class RedBallPatternAnalyzer:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self._analyze_patterns()
    
    def _analyze_patterns(self):
        """分析红球模式"""
        self.odd_even_patterns = []
        self.size_patterns = []
        self.sum_patterns = []
        
        for i, row in self.data.head(100).iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)
            
            # 奇偶模式
            odd_count = sum(1 for num in red_balls if num % 2 == 1)
            self.odd_even_patterns.append(odd_count)
            
            # 大小模式
            large_count = sum(1 for num in red_balls if num > 17)
            self.size_patterns.append(large_count)
            
            # 和值模式
            self.sum_patterns.append(sum(red_balls))
    
    def get_pattern_kills(self, recent_periods: List[List[int]], target_count: int) -> List[int]:
        """基于模式分析的杀号"""
        if not recent_periods:
            return list(range(1, target_count + 1))
        
        last_period = recent_periods[0]
        
        # 分析上期特征
        last_odd_count = sum(1 for num in last_period if num % 2 == 1)
        last_large_count = sum(1 for num in last_period if num > 17)
        last_sum = sum(last_period)
        
        kill_candidates = []
        
        # 基于奇偶模式杀号
        if last_odd_count >= 4:  # 上期奇数多，本期可能偶数多
            kill_candidates.extend([1, 3, 5, 7, 9, 11, 13, 15])
        else:  # 上期偶数多，本期可能奇数多
            kill_candidates.extend([2, 4, 6, 8, 10, 12, 14, 16])
        
        # 基于大小模式杀号
        if last_large_count >= 4:  # 上期大数多，本期可能小数多
            kill_candidates.extend([19, 21, 23, 25, 27, 29, 31, 33, 35])
        else:  # 上期小数多，本期可能大数多
            kill_candidates.extend([1, 2, 3, 4, 5, 6, 7, 8, 9])
        
        # 去重并限制数量
        unique_candidates = list(set(kill_candidates))
        return unique_candidates[:target_count]

class RedBallPositionAnalyzer:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self._analyze_positions()
    
    def _analyze_positions(self):
        """分析红球位置特征"""
        self.position_frequencies = {i: Counter() for i in range(1, 6)}
        
        for i, row in self.data.head(100).iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)
            sorted_balls = sorted(red_balls)
            
            for pos, num in enumerate(sorted_balls[:5], 1):
                self.position_frequencies[pos][num] += 1
    
    def get_position_kills(self, recent_periods: List[List[int]], target_count: int) -> List[int]:
        """基于位置分析的杀号"""
        kill_candidates = []
        
        # 分析每个位置的冷号
        for pos in range(1, 6):
            pos_freqs = self.position_frequencies[pos]
            if pos_freqs:
                # 选择该位置频率最低的号码
                sorted_pos = sorted(pos_freqs.items(), key=lambda x: x[1])
                cold_nums = [num for num, freq in sorted_pos[:3]]
                kill_candidates.extend(cold_nums)
        
        # 去重并限制数量
        unique_candidates = list(set(kill_candidates))
        return unique_candidates[:target_count]

class RedBallTrendAnalyzer:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self._analyze_trends()
    
    def _analyze_trends(self):
        """分析红球趋势"""
        self.recent_trends = Counter()
        
        # 分析最近20期的趋势
        for i, row in self.data.head(20).iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, _ = parse_numbers(row)
            self.recent_trends.update(red_balls)
    
    def get_trend_kills(self, recent_periods: List[List[int]], target_count: int) -> List[int]:
        """基于趋势分析的杀号"""
        # 选择最近趋势中最冷的号码
        sorted_trends = sorted(self.recent_trends.items(), key=lambda x: x[1])
        
        # 补充没有出现的号码
        all_numbers = set(range(1, 36))
        appeared_numbers = set(self.recent_trends.keys())
        missing_numbers = list(all_numbers - appeared_numbers)
        
        # 优先选择完全没出现的号码，然后是出现频率最低的
        kill_candidates = missing_numbers + [num for num, freq in sorted_trends]
        
        return kill_candidates[:target_count]

class EnhancedRedBallBayesian:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self._calculate_enhanced_probabilities()
    
    def _calculate_enhanced_probabilities(self):
        """计算增强的贝叶斯概率"""
        self.transition_probs = defaultdict(lambda: defaultdict(float))
        
        # 分析号码转移概率
        for i in range(len(self.data) - 1):
            if i >= 100:  # 只分析最近100期
                break
            
            from test_kill_algorithm import parse_numbers
            curr_red, _ = parse_numbers(self.data.iloc[i])
            next_red, _ = parse_numbers(self.data.iloc[i + 1])
            
            # 计算每个号码在下期出现的概率
            for curr_num in curr_red:
                for next_num in next_red:
                    self.transition_probs[curr_num][next_num] += 1
        
        # 归一化概率
        for curr_num in self.transition_probs:
            total = sum(self.transition_probs[curr_num].values())
            if total > 0:
                for next_num in self.transition_probs[curr_num]:
                    self.transition_probs[curr_num][next_num] /= total
    
    def get_bayesian_kills(self, recent_periods: List[List[int]], target_count: int) -> List[int]:
        """基于增强贝叶斯分析的杀号"""
        if not recent_periods:
            return list(range(1, target_count + 1))
        
        last_period = recent_periods[0]
        
        # 计算每个号码的出现概率
        next_probs = defaultdict(float)
        
        for last_num in last_period:
            for next_num in range(1, 36):
                prob = self.transition_probs[last_num].get(next_num, 0.01)
                next_probs[next_num] += prob
        
        # 选择概率最低的号码作为杀号
        sorted_probs = sorted(next_probs.items(), key=lambda x: x[1])
        return [num for num, prob in sorted_probs[:target_count]]
