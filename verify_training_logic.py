#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证主程序的训练期数和回测期数逻辑
"""

import pandas as pd
from src.systems.main import LotteryPredictor

def analyze_training_logic():
    """分析训练逻辑"""
    print("🔍 分析主程序的训练期数和回测期数逻辑")
    print("=" * 80)
    
    # 初始化预测器
    predictor = LotteryPredictor()
    
    # 获取数据信息
    total_periods = len(predictor.data)
    print(f"📊 数据集信息:")
    print(f"  总期数: {total_periods}")
    print(f"  最新期号: {predictor.data.iloc[0]['期号']}")
    print(f"  最早期号: {predictor.data.iloc[-1]['期号']}")
    print()
    
    # 分析回测逻辑
    print("🔄 回测逻辑分析:")
    
    # 模拟回测参数
    num_periods = 10  # 回测期数
    max_backtest = min(num_periods, total_periods - 20)  # 保留20期作为最小训练集
    
    print(f"  请求回测期数: {num_periods}")
    print(f"  实际回测期数: {max_backtest}")
    print(f"  保留训练期数: 20期（最小训练集）")
    print(f"  数据使用率: {max_backtest/total_periods:.1%}")
    print()
    
    # 分析训练数据逻辑
    print("📚 训练数据逻辑分析:")
    
    for i in range(min(5, max_backtest)):
        print(f"\n  回测第{i+1}期 (预测期号: {predictor.data.iloc[i]['期号']}):")
        
        # 模拟训练数据获取逻辑
        train_start_index = i + 1
        train_data = predictor.data.iloc[train_start_index:].copy()
        train_periods = len(train_data)
        
        print(f"    训练数据范围: 第{train_start_index+1}期 到 第{total_periods}期")
        print(f"    训练期数: {train_periods}")
        print(f"    训练数据起始期号: {train_data.iloc[0]['期号'] if len(train_data) > 0 else 'N/A'}")
        print(f"    训练数据结束期号: {train_data.iloc[-1]['期号'] if len(train_data) > 0 else 'N/A'}")
        
        # 检查训练数据充足性
        if train_periods < 10:
            print(f"    ⚠️  训练数据不足: {train_periods} < 10期")
        else:
            print(f"    ✅ 训练数据充足: {train_periods}期")
        
        # 分析历史数据使用
        historical_count = min(20, train_periods)
        recent_count = min(6, train_periods)
        sum_count = min(10, train_periods)
        
        print(f"    历史数据使用: 前{historical_count}期")
        print(f"    近期数据使用: 前{recent_count}期")
        print(f"    和值分析使用: 前{sum_count}期")

def verify_data_flow():
    """验证数据流向"""
    print("\n🔄 验证数据流向")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    
    # 测试单次预测的数据流
    print("📊 单次预测数据流验证:")
    
    current_period_index = 0  # 预测最新期的下一期
    
    # 获取训练数据（模拟主程序逻辑）
    train_data = predictor.data.iloc[current_period_index + 1:].copy()
    
    print(f"  当前期索引: {current_period_index}")
    print(f"  当前期号: {predictor.data.iloc[current_period_index]['期号']}")
    print(f"  训练数据期数: {len(train_data)}")
    
    if len(train_data) > 0:
        print(f"  训练数据起始: {train_data.iloc[0]['期号']}")
        print(f"  训练数据结束: {train_data.iloc[-1]['期号']}")
    
    # 验证各个组件的数据使用
    print(f"\n  各组件数据使用验证:")
    
    # 1. 杀号预测数据
    kill_recent_count = min(6, len(train_data))
    print(f"    杀号算法使用: 前{kill_recent_count}期训练数据")
    
    # 2. 历史数据生成
    historical_count = min(20, len(train_data))
    print(f"    号码生成使用: 前{historical_count}期训练数据")
    
    # 3. 和值分析数据
    sum_analysis_count = min(10, len(train_data))
    print(f"    和值分析使用: 前{sum_analysis_count}期训练数据")
    
    # 4. 贝叶斯选择数据
    print(f"    贝叶斯选择使用: 全部{len(train_data)}期训练数据")

def check_time_series_integrity():
    """检查时间序列完整性"""
    print("\n⏰ 检查时间序列完整性")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    
    print("📅 时间序列验证:")
    
    # 检查数据排序
    periods = predictor.data['期号'].tolist()
    is_descending = all(periods[i] >= periods[i+1] for i in range(len(periods)-1))
    is_ascending = all(periods[i] <= periods[i+1] for i in range(len(periods)-1))
    
    print(f"  数据排序: {'降序' if is_descending else '升序' if is_ascending else '无序'}")
    print(f"  最新期在: {'开头' if is_descending else '结尾' if is_ascending else '未知'}")
    
    # 检查期号连续性
    print(f"\n  期号连续性检查:")
    gaps = []
    for i in range(len(periods)-1):
        current = int(str(periods[i])[-3:])  # 取后3位
        next_period = int(str(periods[i+1])[-3:])
        
        if is_descending:
            gap = current - next_period
        else:
            gap = next_period - current
        
        if gap != 1:
            gaps.append((periods[i], periods[i+1], gap))
    
    if gaps:
        print(f"    发现{len(gaps)}个期号间隔:")
        for current, next_p, gap in gaps[:5]:  # 只显示前5个
            print(f"      {current} -> {next_p} (间隔{gap})")
    else:
        print(f"    ✅ 期号连续，无间隔")

def validate_prediction_logic():
    """验证预测逻辑"""
    print("\n🎯 验证预测逻辑")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    
    print("🧪 预测逻辑验证:")
    
    # 测试不同索引的预测
    test_indices = [0, 1, 2]  # 测试前3期
    
    for idx in test_indices:
        print(f"\n  测试索引 {idx}:")
        
        if idx >= len(predictor.data):
            print(f"    ❌ 索引超出范围")
            continue
        
        # 获取当前期信息
        current_period = predictor.data.iloc[idx]['期号']
        print(f"    预测目标: {current_period}期的下一期")
        
        # 获取训练数据
        train_data = predictor.data.iloc[idx + 1:].copy()
        train_count = len(train_data)
        
        print(f"    可用训练数据: {train_count}期")
        
        if train_count < 10:
            print(f"    ⚠️  训练数据不足，将使用默认预测")
        else:
            print(f"    ✅ 训练数据充足，可进行正常预测")
            
            # 检查训练数据的时间关系
            if train_count > 0:
                train_start = train_data.iloc[0]['期号']
                train_end = train_data.iloc[-1]['期号']
                print(f"    训练数据时间范围: {train_start} 到 {train_end}")
                
                # 验证时间逻辑：训练数据应该都在当前期之前
                current_num = int(str(current_period)[-3:])
                train_start_num = int(str(train_start)[-3:])
                
                if train_start_num < current_num:
                    print(f"    ✅ 时间逻辑正确: 训练数据在预测目标之前")
                else:
                    print(f"    ❌ 时间逻辑错误: 训练数据不在预测目标之前")

def main():
    """主函数"""
    try:
        analyze_training_logic()
        verify_data_flow()
        check_time_series_integrity()
        validate_prediction_logic()
        
        print("\n🎯 总结:")
        print("=" * 80)
        print("✅ 训练期数逻辑: 使用当前期之后的所有数据作为训练集")
        print("✅ 回测期数逻辑: 保留20期作为最小训练集，其余用于回测")
        print("✅ 数据流向: 各组件正确使用相应的训练数据子集")
        print("⚠️  注意: 需要验证数据的时间排序是否正确")
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
