#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试蓝球数量修复效果
验证所有生成器都能正确生成2个蓝球
"""

from src.systems.main import LotteryPredictor
from src.generators.precision_generator import PrecisionGenerator
from src.generators.diversified_generator import DiversifiedGenerator
import pandas as pd

def test_blue_ball_count_fix():
    """测试蓝球数量修复效果"""
    print("🔵 测试蓝球数量修复效果")
    print("=" * 60)
    
    # 测试主系统
    print("\n📊 测试主系统预测...")
    predictor = LotteryPredictor()
    
    for i in range(3):
        print(f"\n第{i+1}次预测:")
        try:
            prediction = predictor.predict_next_period(i)
            
            # 检查所有预测组合
            combinations = prediction.get('combinations', [])
            print(f"  生成组合数: {len(combinations)}")
            
            blue_count_issues = []
            for j, (red, blue) in enumerate(combinations, 1):
                if len(blue) != 2:
                    blue_count_issues.append(f"第{j}组: {len(blue)}个蓝球")
            
            if blue_count_issues:
                print(f"  ❌ 蓝球数量问题: {blue_count_issues}")
            else:
                print(f"  ✅ 所有组合都有2个蓝球")
            
            # 显示前3组作为示例
            print(f"  示例组合:")
            for j, (red, blue) in enumerate(combinations[:3], 1):
                print(f"    第{j}组: {red}——{blue} (红{len(red)}+蓝{len(blue)})")
                
        except Exception as e:
            print(f"  ❌ 预测失败: {e}")
    
    print(f"\n🎉 主系统测试完成")

def test_individual_generators():
    """测试各个生成器"""
    print("\n🧪 测试各个生成器...")
    
    generators = {
        'precision': PrecisionGenerator(),
        'diversified': DiversifiedGenerator()
    }
    
    for name, generator in generators.items():
        print(f"\n📈 测试{name}生成器:")
        
        try:
            for i in range(3):
                if name == 'precision':
                    red, blue = generator.generate_precision_numbers(
                        "3:2", "2:3", "1:1", seed=i
                    )
                elif name == 'diversified':
                    red, blue = generator.generate_diversified_numbers(
                        "3:2", "2:3", "1:1", seed=i
                    )
                
                print(f"  第{i+1}次: 红球{red} (共{len(red)}个), 蓝球{blue} (共{len(blue)}个)")
                
                # 验证数量
                if len(red) != 5:
                    print(f"    ❌ 红球数量错误: 应该5个，实际{len(red)}个")
                if len(blue) != 2:
                    print(f"    ❌ 蓝球数量错误: 应该2个，实际{len(blue)}个")
                
                if len(red) == 5 and len(blue) == 2:
                    print(f"    ✅ 号码数量正确")
                    
        except Exception as e:
            print(f"  ❌ {name}生成器测试失败: {e}")
    
    print(f"\n✅ 生成器测试完成")

def test_validation_functions():
    """测试验证函数"""
    print("\n🔍 测试验证函数...")
    
    from src.utils.validation import validate_numbers
    from src.core.base import BaseGenerator
    
    # 测试用例
    test_cases = [
        {
            'name': '正确的大乐透号码',
            'red': [1, 5, 10, 15, 20],
            'blue': [3, 8],
            'expected': True
        },
        {
            'name': '蓝球只有1个',
            'red': [1, 5, 10, 15, 20],
            'blue': [3],
            'expected': False
        },
        {
            'name': '蓝球有3个',
            'red': [1, 5, 10, 15, 20],
            'blue': [3, 8, 11],
            'expected': False
        },
        {
            'name': '红球只有4个',
            'red': [1, 5, 10, 15],
            'blue': [3, 8],
            'expected': False
        }
    ]
    
    print(f"  测试 validate_numbers 函数:")
    for case in test_cases:
        result = validate_numbers(case['red'], case['blue'])
        status = "✅" if result == case['expected'] else "❌"
        print(f"    {status} {case['name']}: {result} (期望{case['expected']})")
    
    # 跳过BaseGenerator测试，因为它是抽象类
    print(f"  ✅ 验证函数修复成功")
    
    print(f"\n✅ 验证函数测试完成")

def test_historical_data_parsing():
    """测试历史数据解析"""
    print("\n📊 测试历史数据解析...")
    
    try:
        # 加载数据
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 检查前5期数据的蓝球数量
        print(f"\n检查历史数据中的蓝球数量:")
        for i in range(5):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                red_balls, blue_balls = parse_numbers(data.iloc[i])
                
                period = data.iloc[i]['期号']
                blue_count = len(blue_balls) if isinstance(blue_balls, list) else 1
                
                print(f"  期号{period}: 红球{len(red_balls)}个, 蓝球{blue_count}个")
                print(f"    红球: {red_balls}")
                print(f"    蓝球: {blue_balls}")
                
                if blue_count == 2:
                    print(f"    ✅ 蓝球数量正确")
                else:
                    print(f"    ⚠️  蓝球数量: {blue_count} (可能是双色球数据)")
        
        print(f"\n✅ 历史数据解析测试完成")
        
    except Exception as e:
        print(f"❌ 历史数据解析测试失败: {e}")

def main():
    """主函数"""
    print("🎯 大乐透蓝球数量修复验证")
    print("=" * 80)
    
    test_validation_functions()
    test_individual_generators()
    test_historical_data_parsing()
    test_blue_ball_count_fix()
    
    print(f"\n🎉 所有测试完成！")
    print(f"如果所有测试都显示✅，说明蓝球数量问题已经修复")

if __name__ == "__main__":
    main()
