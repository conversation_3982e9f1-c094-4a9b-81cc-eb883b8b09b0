#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现实组合分析系统
分析为什么难以找到高杀号数量+高全中率的组合，并提供现实的解决方案
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from itertools import combinations
import random

# 导入现有的算法系统
from single_output_algorithm_system import SingleOutputAlgorithmSystem

class RealisticComboAnalyzer:
    def __init__(self):
        self.system = SingleOutputAlgorithmSystem()
        
    def load_data(self) -> bool:
        """加载数据"""
        return self.system.load_data()

    def analyze_kill_count_vs_success_rate(self, test_periods: int = 50) -> Dict:
        """分析杀号数量与成功率的关系"""
        print(f"\n🔍 分析杀号数量与成功率的关系")
        print("=" * 80)
        
        # 测试所有算法
        all_results = self.system.test_all_algorithms(test_periods)
        
        # 筛选高质量算法
        quality_algorithms = []
        for algo_name, result in all_results.items():
            if result['success_rate'] >= 0.85 and result['total_kills'] >= test_periods * 0.4:
                quality_algorithms.append(algo_name)
        
        print(f"✅ 筛选出{len(quality_algorithms)}个高质量算法")
        
        # 测试不同杀号数量的组合
        analysis_results = {}
        
        for target_kills in range(2, 10):  # 测试2-9个杀号的组合
            print(f"\n🎯 寻找平均杀号{target_kills}个的组合...")
            
            best_combos = []
            
            # 测试不同规模的算法组合
            for combo_size in range(target_kills-1, target_kills+3):  # 算法数量在目标杀号数附近
                if combo_size > len(quality_algorithms):
                    continue
                    
                combo_count = 0
                max_test = 50  # 限制测试数量
                
                for combo in combinations(quality_algorithms, combo_size):
                    if combo_count >= max_test:
                        break
                        
                    combo_stats = self._test_combination_detailed(combo, test_periods)
                    
                    if combo_stats['total_periods'] == 0:
                        continue
                        
                    avg_kills = combo_stats['total_kills'] / combo_stats['total_periods']
                    
                    # 如果平均杀号数接近目标
                    if abs(avg_kills - target_kills) <= 1.0:
                        best_combos.append((combo, combo_stats, avg_kills))
                    
                    combo_count += 1
            
            # 按全中率排序
            best_combos.sort(key=lambda x: x[1]['perfect_rate'], reverse=True)
            analysis_results[target_kills] = best_combos[:5]  # 保留前5个
        
        return analysis_results

    def _test_combination_detailed(self, algorithms: Tuple[str], test_periods: int = 50) -> Dict:
        """详细测试组合"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0
        }
        
        for i in range(test_periods):
            if i + 5 >= len(self.system.data):
                break
                
            # 获取历史数据
            current_period = self.system.data.iloc[i]
            period_data = {
                'current': current_period,
                'last': self.system.data.iloc[i + 1],
                'prev2': self.system.data.iloc[i + 2],
                'prev3': self.system.data.iloc[i + 3],
                'prev4': self.system.data.iloc[i + 4],
                'prev5': self.system.data.iloc[i + 5]
            }
            
            # 解析号码
            from test_kill_algorithm import parse_numbers
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period_data['last'])
            period2_red, _ = parse_numbers(period_data['prev2'])
            
            # 收集本期所有杀号
            period_kills = set()  # 使用set去重
            
            for algo_name in algorithms:
                try:
                    kill_number = self.system.derived_algorithms[algo_name](period_data)
                    if 1 <= kill_number <= 35 and kill_number not in (period1_red + period2_red):
                        period_kills.add(kill_number)
                except Exception:
                    continue
            
            period_kills = list(period_kills)
            
            if period_kills:
                stats['total_periods'] += 1
                stats['total_kills'] += len(period_kills)
                
                # 检查杀号成功情况
                successful_kills = sum(1 for kill in period_kills if kill not in current_red)
                stats['successful_kills'] += successful_kills
                
                # 检查是否全中
                if successful_kills == len(period_kills):
                    stats['perfect_periods'] += 1
        
        # 计算最终统计
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
        
        if stats['total_kills'] > 0:
            stats['kill_success_rate'] = stats['successful_kills'] / stats['total_kills']
        
        return stats

    def print_analysis_results(self, results: Dict):
        """打印分析结果"""
        print(f"\n📊 杀号数量与成功率关系分析")
        print("=" * 80)
        
        print("杀号数量 | 最高全中率 | 最高杀号成功率 | 最佳组合算法数")
        print("-" * 60)
        
        summary_data = []
        
        for target_kills, combos in results.items():
            if combos:
                best_combo = combos[0]  # 全中率最高的组合
                combo, stats, avg_kills = best_combo
                
                print(f"   {target_kills:2d}个   |   {stats['perfect_rate']:6.1%}   |     {stats['kill_success_rate']:6.1%}     |      {len(combo):2d}个")
                
                summary_data.append({
                    'target_kills': target_kills,
                    'perfect_rate': stats['perfect_rate'],
                    'kill_success_rate': stats['kill_success_rate'],
                    'combo_size': len(combo),
                    'actual_avg_kills': avg_kills
                })
            else:
                print(f"   {target_kills:2d}个   |     无     |       无       |      无")
        
        # 分析趋势
        print(f"\n📈 趋势分析:")
        if summary_data:
            # 找出最佳平衡点
            best_balance = max(summary_data, key=lambda x: x['perfect_rate'] * 0.7 + x['kill_success_rate'] * 0.3)
            
            print(f"✅ 最佳平衡点: 平均杀号{best_balance['target_kills']}个")
            print(f"   全中率: {best_balance['perfect_rate']:.1%}")
            print(f"   杀号成功率: {best_balance['kill_success_rate']:.1%}")
            print(f"   算法数量: {best_balance['combo_size']}个")
            
            # 分析为什么高杀号数量难以达到高全中率
            print(f"\n🔍 深度分析:")
            print(f"1. 杀号数量越多，全中率越难保证")
            print(f"2. 单个算法成功率即使达到95%，多个算法组合的全中率会快速下降")
            print(f"3. 数学期望：如果每个算法95%成功率，6个算法全中率 = 0.95^6 = 73.5%")
            print(f"4. 实际情况可能更差，因为算法间可能存在相关性")

    def find_practical_solutions(self, test_periods: int = 50) -> List[Tuple]:
        """寻找实用的解决方案"""
        print(f"\n🎯 寻找实用解决方案")
        print("=" * 60)
        
        # 测试所有算法
        all_results = self.system.test_all_algorithms(test_periods)
        
        # 筛选算法
        quality_algorithms = []
        for algo_name, result in all_results.items():
            if result['success_rate'] >= 0.85 and result['total_kills'] >= test_periods * 0.4:
                quality_algorithms.append(algo_name)
        
        practical_solutions = []
        
        # 方案1: 高全中率，少杀号（2-4个）
        print("方案1: 高全中率策略 (2-4个杀号, 全中率>80%)")
        for combo_size in range(2, 5):
            combo_count = 0
            for combo in combinations(quality_algorithms, combo_size):
                if combo_count >= 30:
                    break
                    
                combo_stats = self._test_combination_detailed(combo, test_periods)
                
                if combo_stats['total_periods'] == 0:
                    continue
                    
                avg_kills = combo_stats['total_kills'] / combo_stats['total_periods']
                
                if combo_stats['perfect_rate'] >= 0.80 and 2.0 <= avg_kills <= 4.0:
                    practical_solutions.append(('高全中率策略', combo, combo_stats, avg_kills))
                
                combo_count += 1
        
        # 方案2: 平衡策略（4-6个杀号，全中率>70%）
        print("方案2: 平衡策略 (4-6个杀号, 全中率>70%)")
        for combo_size in range(4, 7):
            combo_count = 0
            for combo in combinations(quality_algorithms, combo_size):
                if combo_count >= 30:
                    break
                    
                combo_stats = self._test_combination_detailed(combo, test_periods)
                
                if combo_stats['total_periods'] == 0:
                    continue
                    
                avg_kills = combo_stats['total_kills'] / combo_stats['total_periods']
                
                if combo_stats['perfect_rate'] >= 0.70 and 4.0 <= avg_kills <= 6.0:
                    practical_solutions.append(('平衡策略', combo, combo_stats, avg_kills))
                
                combo_count += 1
        
        # 方案3: 高覆盖策略（6-8个杀号，全中率>60%）
        print("方案3: 高覆盖策略 (6-8个杀号, 全中率>60%)")
        for combo_size in range(6, 9):
            combo_count = 0
            for combo in combinations(quality_algorithms, combo_size):
                if combo_count >= 20:
                    break
                    
                combo_stats = self._test_combination_detailed(combo, test_periods)
                
                if combo_stats['total_periods'] == 0:
                    continue
                    
                avg_kills = combo_stats['total_kills'] / combo_stats['total_periods']
                
                if combo_stats['perfect_rate'] >= 0.60 and 6.0 <= avg_kills <= 8.0:
                    practical_solutions.append(('高覆盖策略', combo, combo_stats, avg_kills))
                
                combo_count += 1
        
        # 按策略分组并排序
        practical_solutions.sort(key=lambda x: (x[0], -x[2]['perfect_rate']))
        
        return practical_solutions

    def print_practical_solutions(self, solutions: List[Tuple]):
        """打印实用解决方案"""
        print(f"\n📋 实用解决方案")
        print("=" * 80)
        
        if not solutions:
            print("❌ 未找到符合条件的实用解决方案")
            return
        
        current_strategy = None
        strategy_count = 0
        
        for strategy, combo, stats, avg_kills in solutions:
            if strategy != current_strategy:
                current_strategy = strategy
                strategy_count = 0
                print(f"\n🎯 {strategy}:")
                print("-" * 40)
            
            strategy_count += 1
            if strategy_count > 3:  # 每个策略只显示前3个
                continue
            
            status = "🎯" if stats['perfect_rate'] >= 0.8 else "✅" if stats['perfect_rate'] >= 0.7 else "⚠️"
            
            print(f"{strategy_count}. 全中率:{stats['perfect_rate']:6.1%} "
                  f"杀号成功率:{stats['kill_success_rate']:6.1%} "
                  f"平均杀号:{avg_kills:.1f} "
                  f"算法数:{len(combo)} {status}")
            
            print(f"   算法: {', '.join(combo[:4])}{'...' if len(combo) > 4 else ''}")

def main():
    """主函数"""
    print("🎯 现实组合分析系统")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = RealisticComboAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    print(f"✅ 成功加载数据: {len(analyzer.system.data)} 期")
    
    # 设置随机种子
    random.seed(42)
    
    # 分析杀号数量与成功率的关系
    analysis_results = analyzer.analyze_kill_count_vs_success_rate(test_periods=50)
    analyzer.print_analysis_results(analysis_results)
    
    # 寻找实用解决方案
    practical_solutions = analyzer.find_practical_solutions(test_periods=50)
    analyzer.print_practical_solutions(practical_solutions)
    
    print(f"\n💡 结论与建议:")
    print("1. 杀号数量与全中率存在明显的反比关系")
    print("2. 要达到平均6个杀号且全中率>90%在数学上极其困难")
    print("3. 建议采用分层策略：高全中率(2-4杀号) + 平衡策略(4-6杀号)")
    print("4. 实际应用中可以根据风险偏好选择合适的策略")
    
    print(f"\n🎉 现实分析完成！")

if __name__ == "__main__":
    main()
