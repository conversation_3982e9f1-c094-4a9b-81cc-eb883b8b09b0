{"current_weights": {"markov_chain": 0.6004225461987359, "frequency_analysis": 0.2995070294348081, "trend_following": 0.050035212183228, "pattern_recognition": 0.050035212183228}, "current_confidences": {"markov_chain": 0.7144739183020927, "frequency_analysis": 0.6144739183020926, "trend_following": 0.5144739183020925, "pattern_recognition": 0.4144739183020923}, "evidence_performance": {"markov_chain": {"hits": 0, "total": 30, "accuracy": 0.0}, "frequency_analysis": {"hits": 0, "total": 30, "accuracy": 0.0}, "trend_following": {"hits": 0, "total": 30, "accuracy": 0.0}, "pattern_recognition": {"hits": 0, "total": 30, "accuracy": 0.0}}, "prediction_history": [{"period": "25068", "predicted_state": ["2:3", 0.48890120608562765], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.48890120608562765], "frequency_analysis": ["2:3", 0.48890120608562765], "trend_following": ["2:3", 0.48890120608562765], "pattern_recognition": ["2:3", 0.48890120608562765]}, "weights_used": {"markov_chain": 0.39017274286856674, "frequency_analysis": 0.24999999999999997, "trend_following": 0.17991362856571663, "pattern_recognition": 0.17991362856571663}, "confidences_used": {"markov_chain": 0.7834475761564001, "frequency_analysis": 0.6834475761564001, "trend_following": 0.5834475761564001, "pattern_recognition": 0.4834475761564001}}, {"period": "25067", "predicted_state": ["2:3", 0.47131578066143537], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.47131578066143537], "frequency_analysis": ["2:3", 0.47131578066143537], "trend_following": ["2:3", 0.47131578066143537], "pattern_recognition": ["2:3", 0.47131578066143537]}, "weights_used": {"markov_chain": 0.3983144893517248, "frequency_analysis": 0.25, "trend_following": 0.17584275532413762, "pattern_recognition": 0.17584275532413762}, "confidences_used": {"markov_chain": 0.7807028185407602, "frequency_analysis": 0.6807028185407601, "trend_following": 0.5807028185407601, "pattern_recognition": 0.4807028185407601}}, {"period": "25066", "predicted_state": ["2:3", 0.45261389246319306], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45261389246319306], "frequency_analysis": ["2:3", 0.45261389246319306], "trend_following": ["2:3", 0.45261389246319306], "pattern_recognition": ["2:3", 0.45261389246319306]}, "weights_used": {"markov_chain": 0.4073470998622466, "frequency_analysis": 0.24999999999999997, "trend_following": 0.1713264500688767, "pattern_recognition": 0.1713264500688767}, "confidences_used": {"markov_chain": 0.7778325366866842, "frequency_analysis": 0.6778325366866841, "trend_following": 0.5778325366866841, "pattern_recognition": 0.4778325366866841}}, {"period": "25065", "predicted_state": ["2:3", 0.4758026992041149], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4758026992041149], "frequency_analysis": ["2:3", 0.4758026992041149], "trend_following": ["2:3", 0.4758026992041149], "pattern_recognition": ["2:3", 0.4758026992041149]}, "weights_used": {"markov_chain": 0.4173309109411849, "frequency_analysis": 0.24999999999999997, "trend_following": 0.16633454452940755, "pattern_recognition": 0.16633454452940755}, "confidences_used": {"markov_chain": 0.7748492830180158, "frequency_analysis": 0.6748492830180157, "trend_following": 0.5748492830180157, "pattern_recognition": 0.47484928301801566}}, {"period": "25064", "predicted_state": ["2:3", 0.48754490563478], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.48754490563478], "frequency_analysis": ["2:3", 0.48754490563478], "trend_following": ["2:3", 0.48754490563478], "pattern_recognition": ["2:3", 0.48754490563478]}, "weights_used": {"markov_chain": 0.42833385400923357, "frequency_analysis": 0.24999999999999997, "trend_following": 0.16083307299538321, "pattern_recognition": 0.16083307299538321}, "confidences_used": {"markov_chain": 0.7717643547162143, "frequency_analysis": 0.6717643547162142, "trend_following": 0.5717643547162142, "pattern_recognition": 0.4717643547162141}}, {"period": "25063", "predicted_state": ["3:2", 0.4576341119996916], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4576341119996916], "frequency_analysis": ["3:2", 0.4576341119996916], "trend_following": ["3:2", 0.4576341119996916], "pattern_recognition": ["3:2", 0.4576341119996916]}, "weights_used": {"markov_chain": 0.4404317364616163, "frequency_analysis": 0.25, "trend_following": 0.15478413176919187, "pattern_recognition": 0.15478413176919187}, "confidences_used": {"markov_chain": 0.7685879192445929, "frequency_analysis": 0.6685879192445928, "trend_following": 0.5685879192445928, "pattern_recognition": 0.4685879192445927}}, {"period": "25062", "predicted_state": ["3:2", 0.4856500890875534], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4856500890875534], "frequency_analysis": ["3:2", 0.4856500890875534], "trend_following": ["3:2", 0.4856500890875534], "pattern_recognition": ["3:2", 0.4856500890875534]}, "weights_used": {"markov_chain": 0.45370861633898113, "frequency_analysis": 0.24999999999999997, "trend_following": 0.14814569183050944, "pattern_recognition": 0.14814569183050944}, "confidences_used": {"markov_chain": 0.7653291273201337, "frequency_analysis": 0.6653291273201336, "trend_following": 0.5653291273201336, "pattern_recognition": 0.46532912732013343}}, {"period": "25061", "predicted_state": ["3:2", 0.5269662288229712], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5269662288229712], "frequency_analysis": ["3:2", 0.5269662288229712], "trend_following": ["3:2", 0.5269662288229712], "pattern_recognition": ["3:2", 0.5269662288229712]}, "weights_used": {"markov_chain": 0.46825726466404516, "frequency_analysis": 0.24999999999999997, "trend_following": 0.14087136766797742, "pattern_recognition": 0.14087136766797742}, "confidences_used": {"markov_chain": 0.7619962145881204, "frequency_analysis": 0.6619962145881203, "trend_following": 0.5619962145881203, "pattern_recognition": 0.46199621458812007}}, {"period": "25060", "predicted_state": ["3:2", 0.5562757556794357], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5562757556794357], "frequency_analysis": ["3:2", 0.5562757556794357], "trend_following": ["3:2", 0.5562757556794357], "pattern_recognition": ["3:2", 0.5562757556794357]}, "weights_used": {"markov_chain": 0.4841797121574209, "frequency_analysis": 0.25, "trend_following": 0.13291014392128955, "pattern_recognition": 0.13291014392128955}, "confidences_used": {"markov_chain": 0.7585965931293084, "frequency_analysis": 0.6585965931293083, "trend_following": 0.5585965931293083, "pattern_recognition": 0.45859659312930806}}, {"period": "25059", "predicted_state": ["3:2", 0.5275242917623699], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5275242917623699], "frequency_analysis": ["3:2", 0.5275242917623699], "trend_following": ["3:2", 0.5275242917623699], "pattern_recognition": ["3:2", 0.5275242917623699]}, "weights_used": {"markov_chain": 0.5015878791391031, "frequency_analysis": 0.25000000000000006, "trend_following": 0.12420606043044849, "pattern_recognition": 0.12420606043044849}, "confidences_used": {"markov_chain": 0.7551369338163776, "frequency_analysis": 0.6551369338163775, "trend_following": 0.5551369338163775, "pattern_recognition": 0.45513693381637726}}, {"period": "25068", "predicted_state": ["2:3", 0.4662020259000598], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4662020259000598], "frequency_analysis": ["2:3", 0.4662020259000598], "trend_following": ["2:3", 0.4662020259000598], "pattern_recognition": ["2:3", 0.4662020259000598]}, "weights_used": {"markov_chain": 0.5206042891346038, "frequency_analysis": 0.25000000000000006, "trend_following": 0.1146978554326981, "pattern_recognition": 0.1146978554326981}, "confidences_used": {"markov_chain": 0.7516232404347398, "frequency_analysis": 0.6516232404347397, "trend_following": 0.5516232404347398, "pattern_recognition": 0.45162324043473956}}, {"period": "25067", "predicted_state": ["2:3", 0.46264132156201926], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.46264132156201926], "frequency_analysis": ["2:3", 0.46264132156201926], "trend_following": ["2:3", 0.46264132156201926], "pattern_recognition": ["2:3", 0.46264132156201926]}, "weights_used": {"markov_chain": 0.5413628681460476, "frequency_analysis": 0.25000000000000006, "trend_following": 0.10431856592697614, "pattern_recognition": 0.10431856592697614}, "confidences_used": {"markov_chain": 0.7480609163912659, "frequency_analysis": 0.6480609163912658, "trend_following": 0.5480609163912658, "pattern_recognition": 0.4480609163912656}}, {"period": "25066", "predicted_state": ["2:3", 0.4311948039095762], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4311948039095762], "frequency_analysis": ["2:3", 0.4311948039095762], "trend_following": ["2:3", 0.4311948039095762], "pattern_recognition": ["2:3", 0.4311948039095762]}, "weights_used": {"markov_chain": 0.564009832799267, "frequency_analysis": 0.25000000000000006, "trend_following": 0.09299508360036648, "pattern_recognition": 0.09299508360036648}, "confidences_used": {"markov_chain": 0.7444548247521394, "frequency_analysis": 0.6444548247521393, "trend_following": 0.5444548247521392, "pattern_recognition": 0.44445482475213904}}, {"period": "25065", "predicted_state": ["2:3", 0.45848600731286965], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45848600731286965], "frequency_analysis": ["2:3", 0.45848600731286965], "trend_following": ["2:3", 0.45848600731286965], "pattern_recognition": ["2:3", 0.45848600731286965]}, "weights_used": {"markov_chain": 0.5887046716982478, "frequency_analysis": 0.25000000000000006, "trend_following": 0.08064766415087608, "pattern_recognition": 0.08064766415087608}, "confidences_used": {"markov_chain": 0.7408093422769255, "frequency_analysis": 0.6408093422769254, "trend_following": 0.5408093422769253, "pattern_recognition": 0.44080934227692514}}, {"period": "25064", "predicted_state": ["2:3", 0.4673474356693134], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4673474356693134], "frequency_analysis": ["2:3", 0.4673474356693134], "trend_following": ["2:3", 0.4673474356693134], "pattern_recognition": ["2:3", 0.4673474356693134]}, "weights_used": {"markov_chain": 0.6095214722763839, "frequency_analysis": 0.25396728011516, "trend_following": 0.06825562380422802, "pattern_recognition": 0.06825562380422802}, "confidences_used": {"markov_chain": 0.737128408049233, "frequency_analysis": 0.6371284080492329, "trend_following": 0.5371284080492328, "pattern_recognition": 0.43712840804923264}}, {"period": "25063", "predicted_state": ["3:2", 0.4714354793013091], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4714354793013091], "frequency_analysis": ["3:2", 0.4714354793013091], "trend_following": ["3:2", 0.4714354793013091], "pattern_recognition": ["3:2", 0.4714354793013091]}, "weights_used": {"markov_chain": 0.6239340069905913, "frequency_analysis": 0.26442896020772694, "trend_following": 0.05581851640084085, "pattern_recognition": 0.05581851640084085}, "confidences_used": {"markov_chain": 0.7334155672443097, "frequency_analysis": 0.6334155672443096, "trend_following": 0.5334155672443095, "pattern_recognition": 0.4334155672443094}}, {"period": "25062", "predicted_state": ["3:2", 0.4980941688927516], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4980941688927516], "frequency_analysis": ["3:2", 0.4980941688927516], "trend_following": ["3:2", 0.4980941688927516], "pattern_recognition": ["3:2", 0.4980941688927516]}, "weights_used": {"markov_chain": 0.6213778662578019, "frequency_analysis": 0.27505915603256453, "trend_following": 0.05178148885481683, "pattern_recognition": 0.05178148885481683}, "confidences_used": {"markov_chain": 0.7296740105198788, "frequency_analysis": 0.6296740105198787, "trend_following": 0.5296740105198786, "pattern_recognition": 0.42967401051987847}}, {"period": "25061", "predicted_state": ["3:2", 0.5294297148161806], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5294297148161806], "frequency_analysis": ["3:2", 0.5294297148161806], "trend_following": ["3:2", 0.5294297148161806], "pattern_recognition": ["3:2", 0.5294297148161806]}, "weights_used": {"markov_chain": 0.6140612811953273, "frequency_analysis": 0.28359517193878475, "trend_following": 0.05117177343294395, "pattern_recognition": 0.05117177343294395}, "confidences_used": {"markov_chain": 0.725906609467891, "frequency_analysis": 0.6259066094678909, "trend_following": 0.5259066094678908, "pattern_recognition": 0.4259066094678906}}, {"period": "25060", "predicted_state": ["3:2", 0.5569521386894155], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5569521386894155], "frequency_analysis": ["3:2", 0.5569521386894155], "trend_following": ["3:2", 0.5569521386894155], "pattern_recognition": ["3:2", 0.5569521386894155]}, "weights_used": {"markov_chain": 0.6083026850004432, "frequency_analysis": 0.2903135341661497, "trend_following": 0.0506918904167036, "pattern_recognition": 0.0506918904167036}, "confidences_used": {"markov_chain": 0.722115948521102, "frequency_analysis": 0.6221159485211019, "trend_following": 0.5221159485211018, "pattern_recognition": 0.42211594852110157}}, {"period": "25059", "predicted_state": ["3:2", 0.5196100067545627], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5196100067545627], "frequency_analysis": ["3:2", 0.5196100067545627], "trend_following": ["3:2", 0.5196100067545627], "pattern_recognition": ["3:2", 0.5196100067545627]}, "weights_used": {"markov_chain": 0.6038402315936632, "frequency_analysis": 0.2955197298073929, "trend_following": 0.05032001929947194, "pattern_recognition": 0.05032001929947194}, "confidences_used": {"markov_chain": 0.7183043536689918, "frequency_analysis": 0.6183043536689917, "trend_following": 0.5183043536689916, "pattern_recognition": 0.41830435366899144}}], "weight_momentum": {"markov_chain": -0.01915217683449568, "frequency_analysis": -0.01915217683449568, "trend_following": -0.01915217683449568, "pattern_recognition": -0.01915217683449568}, "confidence_momentum": {"markov_chain": -0.0038304353668991375, "frequency_analysis": -0.0038304353668991375, "trend_following": -0.0038304353668991375, "pattern_recognition": -0.0038304353668991375}}