{"current_weights": {"markov_chain": 0.589825081377796, "frequency_analysis": 0.3118707383925713, "trend_following": 0.04915209011481633, "pattern_recognition": 0.04915209011481633}, "current_confidences": {"markov_chain": 0.3879997299379518, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}, "evidence_performance": {"markov_chain": {"hits": 0, "total": 112, "accuracy": 0.0}, "frequency_analysis": {"hits": 0, "total": 112, "accuracy": 0.0}, "trend_following": {"hits": 0, "total": 112, "accuracy": 0.0}, "pattern_recognition": {"hits": 0, "total": 112, "accuracy": 0.0}}, "prediction_history": [{"period": "25068", "predicted_state": ["2:3", 0.438447275172026], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.438447275172026], "frequency_analysis": ["2:3", 0.438447275172026], "trend_following": ["2:3", 0.438447275172026], "pattern_recognition": ["2:3", 0.438447275172026]}, "weights_used": {"markov_chain": 0.5898260855708535, "frequency_analysis": 0.3118695668340043, "trend_following": 0.04915217379757113, "pattern_recognition": 0.04915217379757113}, "confidences_used": {"markov_chain": 0.46799777866853143, "frequency_analysis": 0.36799777866853134, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.44856198894568927], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44856198894568927], "frequency_analysis": ["2:3", 0.44856198894568927], "trend_following": ["2:3", 0.44856198894568927], "pattern_recognition": ["2:3", 0.44856198894568927]}, "weights_used": {"markov_chain": 0.5898259712483932, "frequency_analysis": 0.311869700210208, "trend_following": 0.04915216427069943, "pattern_recognition": 0.04915216427069943}, "confidences_used": {"markov_chain": 0.46399800080167836, "frequency_analysis": 0.36399800080167827, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.41419885747991314], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.41419885747991314], "frequency_analysis": ["2:3", 0.41419885747991314], "trend_following": ["2:3", 0.41419885747991314], "pattern_recognition": ["2:3", 0.41419885747991314]}, "weights_used": {"markov_chain": 0.5898258683597902, "frequency_analysis": 0.31186982024691146, "trend_following": 0.049152155696649186, "pattern_recognition": 0.049152155696649186}, "confidences_used": {"markov_chain": 0.45999820072151054, "frequency_analysis": 0.35999820072151045, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.44548717339154176], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44548717339154176], "frequency_analysis": ["2:3", 0.44548717339154176], "trend_following": ["2:3", 0.44548717339154176], "pattern_recognition": ["2:3", 0.44548717339154176]}, "weights_used": {"markov_chain": 0.589825775761178, "frequency_analysis": 0.31186992827862564, "trend_following": 0.04915214798009817, "pattern_recognition": 0.04915214798009817}, "confidences_used": {"markov_chain": 0.45599838064935955, "frequency_analysis": 0.35599838064935946, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.45547160362266725], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45547160362266725], "frequency_analysis": ["2:3", 0.45547160362266725], "trend_following": ["2:3", 0.45547160362266725], "pattern_recognition": ["2:3", 0.45547160362266725]}, "weights_used": {"markov_chain": 0.5898256924232144, "frequency_analysis": 0.31187002550625, "trend_following": 0.04915214103526787, "pattern_recognition": 0.04915214103526787}, "confidences_used": {"markov_chain": 0.4519985425844236, "frequency_analysis": 0.35199854258442353, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.4718882356148014], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4718882356148014], "frequency_analysis": ["3:2", 0.4718882356148014], "trend_following": ["3:2", 0.4718882356148014], "pattern_recognition": ["3:2", 0.4718882356148014]}, "weights_used": {"markov_chain": 0.5898256174195902, "frequency_analysis": 0.311870113010478, "trend_following": 0.049152134784965856, "pattern_recognition": 0.049152134784965856}, "confidences_used": {"markov_chain": 0.44799868832598133, "frequency_analysis": 0.34799868832598124, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.497482644439447], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.497482644439447], "frequency_analysis": ["3:2", 0.497482644439447], "trend_following": ["3:2", 0.497482644439447], "pattern_recognition": ["3:2", 0.497482644439447]}, "weights_used": {"markov_chain": 0.5898255499166991, "frequency_analysis": 0.31187019176385095, "trend_following": 0.04915212915972493, "pattern_recognition": 0.04915212915972493}, "confidences_used": {"markov_chain": 0.44399881949338327, "frequency_analysis": 0.3439988194933832, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.5279408413139212], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5279408413139212], "frequency_analysis": ["3:2", 0.5279408413139212], "trend_following": ["3:2", 0.5279408413139212], "pattern_recognition": ["3:2", 0.5279408413139212]}, "weights_used": {"markov_chain": 0.5898254891643464, "frequency_analysis": 0.3118702626415959, "trend_following": 0.04915212409702887, "pattern_recognition": 0.04915212409702887}, "confidences_used": {"markov_chain": 0.439998937544045, "frequency_analysis": 0.3399989375440449, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.55520409063999], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.55520409063999], "frequency_analysis": ["3:2", 0.55520409063999], "trend_following": ["3:2", 0.55520409063999], "pattern_recognition": ["3:2", 0.55520409063999]}, "weights_used": {"markov_chain": 0.5898254344873931, "frequency_analysis": 0.31187032643137474, "trend_following": 0.0491521195406161, "pattern_recognition": 0.0491521195406161}, "confidences_used": {"markov_chain": 0.43599904378964055, "frequency_analysis": 0.33599904378964046, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5193170251612453], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5193170251612453], "frequency_analysis": ["3:2", 0.5193170251612453], "trend_following": ["3:2", 0.5193170251612453], "pattern_recognition": ["3:2", 0.5193170251612453]}, "weights_used": {"markov_chain": 0.5898253852782406, "frequency_analysis": 0.3118703838420526, "trend_following": 0.04915211543985338, "pattern_recognition": 0.04915211543985338}, "confidences_used": {"markov_chain": 0.4319991394106765, "frequency_analysis": 0.33199913941067644, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25068", "predicted_state": ["2:3", 0.4408709262373323], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4408709262373323], "frequency_analysis": ["2:3", 0.4408709262373323], "trend_following": ["2:3", 0.4408709262373323], "pattern_recognition": ["2:3", 0.4408709262373323]}, "weights_used": {"markov_chain": 0.5898253409900686, "frequency_analysis": 0.31187043551158683, "trend_following": 0.04915211174917238, "pattern_recognition": 0.04915211174917238}, "confidences_used": {"markov_chain": 0.42799922546960895, "frequency_analysis": 0.32799922546960886, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.45029551771231097], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45029551771231097], "frequency_analysis": ["2:3", 0.45029551771231097], "trend_following": ["2:3", 0.45029551771231097], "pattern_recognition": ["2:3", 0.45029551771231097]}, "weights_used": {"markov_chain": 0.5898253011307514, "frequency_analysis": 0.3118704820141234, "trend_following": 0.049152108427562624, "pattern_recognition": 0.049152108427562624}, "confidences_used": {"markov_chain": 0.4239993029226481, "frequency_analysis": 0.323999302922648, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.4164707033026271], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4164707033026271], "frequency_analysis": ["2:3", 0.4164707033026271], "trend_following": ["2:3", 0.4164707033026271], "pattern_recognition": ["2:3", 0.4164707033026271]}, "weights_used": {"markov_chain": 0.5898252652573857, "frequency_analysis": 0.3118705238663833, "trend_following": 0.049152105438115476, "pattern_recognition": 0.049152105438115476}, "confidences_used": {"markov_chain": 0.41999937263038334, "frequency_analysis": 0.31999937263038325, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.4474969143458805], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4474969143458805], "frequency_analysis": ["2:3", 0.4474969143458805], "trend_following": ["2:3", 0.4474969143458805], "pattern_recognition": ["2:3", 0.4474969143458805]}, "weights_used": {"markov_chain": 0.5898252329713645, "frequency_analysis": 0.31187056153340814, "trend_following": 0.04915210274761371, "pattern_recognition": 0.04915210274761371}, "confidences_used": {"markov_chain": 0.41599943536734507, "frequency_analysis": 0.315999435367345, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.45804598068649804], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45804598068649804], "frequency_analysis": ["2:3", 0.45804598068649804], "trend_following": ["2:3", 0.45804598068649804], "pattern_recognition": ["2:3", 0.45804598068649804]}, "weights_used": {"markov_chain": 0.5898252039139458, "frequency_analysis": 0.31187059543372997, "trend_following": 0.04915210032616216, "pattern_recognition": 0.04915210032616216}, "confidences_used": {"markov_chain": 0.41199949183061063, "frequency_analysis": 0.31199949183061054, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.4711011325926464], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4711011325926464], "frequency_analysis": ["3:2", 0.4711011325926464], "trend_following": ["3:2", 0.4711011325926464], "pattern_recognition": ["3:2", 0.4711011325926464]}, "weights_used": {"markov_chain": 0.589825177762265, "frequency_analysis": 0.31187062594402426, "trend_following": 0.04915209814685542, "pattern_recognition": 0.04915209814685542}, "confidences_used": {"markov_chain": 0.4079995426475496, "frequency_analysis": 0.30799954264754953, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.4968098821924423], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4968098821924423], "frequency_analysis": ["3:2", 0.4968098821924423], "trend_following": ["3:2", 0.4968098821924423], "pattern_recognition": ["3:2", 0.4968098821924423]}, "weights_used": {"markov_chain": 0.5898251542257457, "frequency_analysis": 0.3118706534032966, "trend_following": 0.04915209618547881, "pattern_recognition": 0.04915209618547881}, "confidences_used": {"markov_chain": 0.4039995883827947, "frequency_analysis": 0.3039995883827946, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.5277846918268649], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5277846918268649], "frequency_analysis": ["3:2", 0.5277846918268649], "trend_following": ["3:2", 0.5277846918268649], "pattern_recognition": ["3:2", 0.5277846918268649]}, "weights_used": {"markov_chain": 0.5898251330428709, "frequency_analysis": 0.31187067811665065, "trend_following": 0.049152094420239247, "pattern_recognition": 0.049152094420239247}, "confidences_used": {"markov_chain": 0.3999996295445153, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.5552870817590196], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5552870817590196], "frequency_analysis": ["3:2", 0.5552870817590196], "trend_following": ["3:2", 0.5552870817590196], "pattern_recognition": ["3:2", 0.5552870817590196]}, "weights_used": {"markov_chain": 0.5898251139782756, "frequency_analysis": 0.31187070035867853, "trend_following": 0.04915209283152297, "pattern_recognition": 0.04915209283152297}, "confidences_used": {"markov_chain": 0.3959996665900638, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5200766749498702], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5200766749498702], "frequency_analysis": ["3:2", 0.5200766749498702], "trend_following": ["3:2", 0.5200766749498702], "pattern_recognition": ["3:2", 0.5200766749498702]}, "weights_used": {"markov_chain": 0.5898250968201322, "frequency_analysis": 0.3118707203765124, "trend_following": 0.04915209140167768, "pattern_recognition": 0.04915209140167768}, "confidences_used": {"markov_chain": 0.3919996999310575, "frequency_analysis": 0.3, "trend_following": 0.3, "pattern_recognition": 0.3}}], "weight_momentum": {"markov_chain": -0.01999984996552849, "frequency_analysis": -0.01999984996552849, "trend_following": -0.01999984996552849, "pattern_recognition": -0.01999984996552849}, "confidence_momentum": {"markov_chain": -0.003999969993105696, "frequency_analysis": -0.003999969993105696, "trend_following": -0.003999969993105696, "pattern_recognition": -0.003999969993105696}}