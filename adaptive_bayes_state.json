{"current_weights": {"markov_chain": 0.5898253409900686, "frequency_analysis": 0.31187043551158683, "trend_following": 0.04915211174917238, "pattern_recognition": 0.04915211174917238}, "current_confidences": {"markov_chain": 0.42799922546960895, "frequency_analysis": 0.32799922546960886, "trend_following": 0.3, "pattern_recognition": 0.3}, "evidence_performance": {"markov_chain": {"hits": 0, "total": 102, "accuracy": 0.0}, "frequency_analysis": {"hits": 0, "total": 102, "accuracy": 0.0}, "trend_following": {"hits": 0, "total": 102, "accuracy": 0.0}, "pattern_recognition": {"hits": 0, "total": 102, "accuracy": 0.0}}, "prediction_history": [{"period": "25068", "predicted_state": ["2:3", 0.4367006127750872], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4367006127750872], "frequency_analysis": ["2:3", 0.4367006127750872], "trend_following": ["2:3", 0.4367006127750872], "pattern_recognition": ["2:3", 0.4367006127750872]}, "weights_used": {"markov_chain": 0.5898282217682255, "frequency_analysis": 0.3118670746037369, "trend_following": 0.049152351814018794, "pattern_recognition": 0.049152351814018794}, "confidences_used": {"markov_chain": 0.5079936292835651, "frequency_analysis": 0.40799362928356503, "trend_following": 0.30799362928356483, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.44721280915628325], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44721280915628325], "frequency_analysis": ["2:3", 0.44721280915628325], "trend_following": ["2:3", 0.44721280915628325], "pattern_recognition": ["2:3", 0.44721280915628325]}, "weights_used": {"markov_chain": 0.5898278936712914, "frequency_analysis": 0.31186745738349325, "trend_following": 0.049152324472607624, "pattern_recognition": 0.049152324472607624}, "confidences_used": {"markov_chain": 0.5039942663552086, "frequency_analysis": 0.4039942663552086, "trend_following": 0.3039942663552084, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.4122921100117741], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4122921100117741], "frequency_analysis": ["2:3", 0.4122921100117741], "trend_following": ["2:3", 0.4122921100117741], "pattern_recognition": ["2:3", 0.4122921100117741]}, "weights_used": {"markov_chain": 0.5898275984286879, "frequency_analysis": 0.31186780183319734, "trend_following": 0.049152299869057337, "pattern_recognition": 0.049152299869057337}, "confidences_used": {"markov_chain": 0.4999948397196878, "frequency_analysis": 0.39999483971968774, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.44380032230865113], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44380032230865113], "frequency_analysis": ["2:3", 0.44380032230865113], "trend_following": ["2:3", 0.44380032230865113], "pattern_recognition": ["2:3", 0.44380032230865113]}, "weights_used": {"markov_chain": 0.5898273327427191, "frequency_analysis": 0.311868111800161, "trend_following": 0.049152277728559936, "pattern_recognition": 0.049152277728559936}, "confidences_used": {"markov_chain": 0.495995355747719, "frequency_analysis": 0.395995355747719, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.45329654124205543], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45329654124205543], "frequency_analysis": ["2:3", 0.45329654124205543], "trend_following": ["2:3", 0.45329654124205543], "pattern_recognition": ["2:3", 0.45329654124205543]}, "weights_used": {"markov_chain": 0.5898270936487897, "frequency_analysis": 0.3118683907430786, "trend_following": 0.04915225780406582, "pattern_recognition": 0.04915225780406582}, "confidences_used": {"markov_chain": 0.4919958201729472, "frequency_analysis": 0.39199582017294715, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.4725107566698212], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4725107566698212], "frequency_analysis": ["3:2", 0.4725107566698212], "trend_following": ["3:2", 0.4725107566698212], "pattern_recognition": ["3:2", 0.4725107566698212]}, "weights_used": {"markov_chain": 0.5898268784811972, "frequency_analysis": 0.31186864177193657, "trend_following": 0.049152239873433104, "pattern_recognition": 0.049152239873433104}, "confidences_used": {"markov_chain": 0.4879962381556525, "frequency_analysis": 0.3879962381556525, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.4980041293931438], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4980041293931438], "frequency_analysis": ["3:2", 0.4980041293931438], "trend_following": ["3:2", 0.4980041293931438], "pattern_recognition": ["3:2", 0.4980041293931438]}, "weights_used": {"markov_chain": 0.5898266848425852, "frequency_analysis": 0.3118688676836506, "trend_following": 0.0491522237368821, "pattern_recognition": 0.0491522237368821}, "confidences_used": {"markov_chain": 0.48399661434008734, "frequency_analysis": 0.38399661434008725, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.528027539494748], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.528027539494748], "frequency_analysis": ["3:2", 0.528027539494748], "trend_following": ["3:2", 0.528027539494748], "pattern_recognition": ["3:2", 0.528027539494748]}, "weights_used": {"markov_chain": 0.5898265105766283, "frequency_analysis": 0.31186907099393363, "trend_following": 0.04915220921471903, "pattern_recognition": 0.04915220921471903}, "confidences_used": {"markov_chain": 0.47999695290607863, "frequency_analysis": 0.37999695290607854, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.5551455450195577], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5551455450195577], "frequency_analysis": ["3:2", 0.5551455450195577], "trend_following": ["3:2", 0.5551455450195577], "pattern_recognition": ["3:2", 0.5551455450195577]}, "weights_used": {"markov_chain": 0.5898263537435775, "frequency_analysis": 0.3118692539658263, "trend_following": 0.04915219614529812, "pattern_recognition": 0.04915219614529812}, "confidences_used": {"markov_chain": 0.4759972576154708, "frequency_analysis": 0.3759972576154707, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5187884054098001], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5187884054098001], "frequency_analysis": ["3:2", 0.5187884054098001], "trend_following": ["3:2", 0.5187884054098001], "pattern_recognition": ["3:2", 0.5187884054098001]}, "weights_used": {"markov_chain": 0.5898262125983454, "frequency_analysis": 0.3118694186352636, "trend_following": 0.049152184383195456, "pattern_recognition": 0.049152184383195456}, "confidences_used": {"markov_chain": 0.47199753185392374, "frequency_analysis": 0.37199753185392365, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25068", "predicted_state": ["2:3", 0.438447275172026], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.438447275172026], "frequency_analysis": ["2:3", 0.438447275172026], "trend_following": ["2:3", 0.438447275172026], "pattern_recognition": ["2:3", 0.438447275172026]}, "weights_used": {"markov_chain": 0.5898260855708535, "frequency_analysis": 0.3118695668340043, "trend_following": 0.04915217379757113, "pattern_recognition": 0.04915217379757113}, "confidences_used": {"markov_chain": 0.46799777866853143, "frequency_analysis": 0.36799777866853134, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.44856198894568927], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44856198894568927], "frequency_analysis": ["2:3", 0.44856198894568927], "trend_following": ["2:3", 0.44856198894568927], "pattern_recognition": ["2:3", 0.44856198894568927]}, "weights_used": {"markov_chain": 0.5898259712483932, "frequency_analysis": 0.311869700210208, "trend_following": 0.04915216427069943, "pattern_recognition": 0.04915216427069943}, "confidences_used": {"markov_chain": 0.46399800080167836, "frequency_analysis": 0.36399800080167827, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.41419885747991314], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.41419885747991314], "frequency_analysis": ["2:3", 0.41419885747991314], "trend_following": ["2:3", 0.41419885747991314], "pattern_recognition": ["2:3", 0.41419885747991314]}, "weights_used": {"markov_chain": 0.5898258683597902, "frequency_analysis": 0.31186982024691146, "trend_following": 0.049152155696649186, "pattern_recognition": 0.049152155696649186}, "confidences_used": {"markov_chain": 0.45999820072151054, "frequency_analysis": 0.35999820072151045, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.44548717339154176], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44548717339154176], "frequency_analysis": ["2:3", 0.44548717339154176], "trend_following": ["2:3", 0.44548717339154176], "pattern_recognition": ["2:3", 0.44548717339154176]}, "weights_used": {"markov_chain": 0.589825775761178, "frequency_analysis": 0.31186992827862564, "trend_following": 0.04915214798009817, "pattern_recognition": 0.04915214798009817}, "confidences_used": {"markov_chain": 0.45599838064935955, "frequency_analysis": 0.35599838064935946, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.45547160362266725], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45547160362266725], "frequency_analysis": ["2:3", 0.45547160362266725], "trend_following": ["2:3", 0.45547160362266725], "pattern_recognition": ["2:3", 0.45547160362266725]}, "weights_used": {"markov_chain": 0.5898256924232144, "frequency_analysis": 0.31187002550625, "trend_following": 0.04915214103526787, "pattern_recognition": 0.04915214103526787}, "confidences_used": {"markov_chain": 0.4519985425844236, "frequency_analysis": 0.35199854258442353, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.4718882356148014], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4718882356148014], "frequency_analysis": ["3:2", 0.4718882356148014], "trend_following": ["3:2", 0.4718882356148014], "pattern_recognition": ["3:2", 0.4718882356148014]}, "weights_used": {"markov_chain": 0.5898256174195902, "frequency_analysis": 0.311870113010478, "trend_following": 0.049152134784965856, "pattern_recognition": 0.049152134784965856}, "confidences_used": {"markov_chain": 0.44799868832598133, "frequency_analysis": 0.34799868832598124, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.497482644439447], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.497482644439447], "frequency_analysis": ["3:2", 0.497482644439447], "trend_following": ["3:2", 0.497482644439447], "pattern_recognition": ["3:2", 0.497482644439447]}, "weights_used": {"markov_chain": 0.5898255499166991, "frequency_analysis": 0.31187019176385095, "trend_following": 0.04915212915972493, "pattern_recognition": 0.04915212915972493}, "confidences_used": {"markov_chain": 0.44399881949338327, "frequency_analysis": 0.3439988194933832, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.5279408413139212], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5279408413139212], "frequency_analysis": ["3:2", 0.5279408413139212], "trend_following": ["3:2", 0.5279408413139212], "pattern_recognition": ["3:2", 0.5279408413139212]}, "weights_used": {"markov_chain": 0.5898254891643464, "frequency_analysis": 0.3118702626415959, "trend_following": 0.04915212409702887, "pattern_recognition": 0.04915212409702887}, "confidences_used": {"markov_chain": 0.439998937544045, "frequency_analysis": 0.3399989375440449, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.55520409063999], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.55520409063999], "frequency_analysis": ["3:2", 0.55520409063999], "trend_following": ["3:2", 0.55520409063999], "pattern_recognition": ["3:2", 0.55520409063999]}, "weights_used": {"markov_chain": 0.5898254344873931, "frequency_analysis": 0.31187032643137474, "trend_following": 0.0491521195406161, "pattern_recognition": 0.0491521195406161}, "confidences_used": {"markov_chain": 0.43599904378964055, "frequency_analysis": 0.33599904378964046, "trend_following": 0.3, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5193170251612453], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5193170251612453], "frequency_analysis": ["3:2", 0.5193170251612453], "trend_following": ["3:2", 0.5193170251612453], "pattern_recognition": ["3:2", 0.5193170251612453]}, "weights_used": {"markov_chain": 0.5898253852782406, "frequency_analysis": 0.3118703838420526, "trend_following": 0.04915211543985338, "pattern_recognition": 0.04915211543985338}, "confidences_used": {"markov_chain": 0.4319991394106765, "frequency_analysis": 0.33199913941067644, "trend_following": 0.3, "pattern_recognition": 0.3}}], "weight_momentum": {"markov_chain": -0.019999569705338026, "frequency_analysis": -0.019999569705338026, "trend_following": -0.019999569705338026, "pattern_recognition": -0.019999569705338026}, "confidence_momentum": {"markov_chain": -0.003999913941067604, "frequency_analysis": -0.003999913941067604, "trend_following": -0.003999913941067604, "pattern_recognition": -0.003999913941067604}}