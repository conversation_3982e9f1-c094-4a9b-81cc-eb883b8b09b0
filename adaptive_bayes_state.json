{"current_weights": {"markov_chain": 0.5898343674729596, "frequency_analysis": 0.3118599046148805, "trend_following": 0.04915286395607997, "pattern_recognition": 0.04915286395607997}, "current_confidences": {"markov_chain": 0.5479817289637025, "frequency_analysis": 0.4479817289637025, "trend_following": 0.3479817289637023, "pattern_recognition": 0.3}, "evidence_performance": {"markov_chain": {"hits": 0, "total": 72, "accuracy": 0.0}, "frequency_analysis": {"hits": 0, "total": 72, "accuracy": 0.0}, "trend_following": {"hits": 0, "total": 72, "accuracy": 0.0}, "pattern_recognition": {"hits": 0, "total": 72, "accuracy": 0.0}}, "prediction_history": [{"period": "25061", "predicted_state": ["3:2", 0.5303994204771677], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5303994204771677], "frequency_analysis": ["3:2", 0.5303994204771677], "trend_following": ["3:2", 0.5303994204771677], "pattern_recognition": ["3:2", 0.5303994204771677]}, "weights_used": {"markov_chain": 0.5899138762998662, "frequency_analysis": 0.31176714431682256, "trend_following": 0.04915948969165553, "pattern_recognition": 0.04915948969165553}, "confidences_used": {"markov_chain": 0.627849715914955, "frequency_analysis": 0.5278497159149549, "trend_following": 0.42784971591495485, "pattern_recognition": 0.32784971591495454}}, {"period": "25060", "predicted_state": ["3:2", 0.5574525220446169], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5574525220446169], "frequency_analysis": ["3:2", 0.5574525220446169], "trend_following": ["3:2", 0.5574525220446169], "pattern_recognition": ["3:2", 0.5574525220446169]}, "weights_used": {"markov_chain": 0.5899030581229558, "frequency_analysis": 0.3117797655232181, "trend_following": 0.049158588176912994, "pattern_recognition": 0.049158588176912994}, "confidences_used": {"markov_chain": 0.6238647443234595, "frequency_analysis": 0.5238647443234594, "trend_following": 0.4238647443234594, "pattern_recognition": 0.3238647443234591}}, {"period": "25059", "predicted_state": ["3:2", 0.5194213208113839], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5194213208113839], "frequency_analysis": ["3:2", 0.5194213208113839], "trend_following": ["3:2", 0.5194213208113839], "pattern_recognition": ["3:2", 0.5194213208113839]}, "weights_used": {"markov_chain": 0.5898938327366815, "frequency_analysis": 0.3117905284738716, "trend_following": 0.04915781939472346, "pattern_recognition": 0.04915781939472346}, "confidences_used": {"markov_chain": 0.6198782698911136, "frequency_analysis": 0.5198782698911135, "trend_following": 0.4198782698911135, "pattern_recognition": 0.3198782698911132}}, {"period": "25058", "predicted_state": ["3:2", 0.576347309093719], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.576347309093719], "frequency_analysis": ["3:2", 0.576347309093719], "trend_following": ["3:2", 0.576347309093719], "pattern_recognition": ["3:2", 0.576347309093719]}, "weights_used": {"markov_chain": 0.5898859053077683, "frequency_analysis": 0.31179977714093693, "trend_following": 0.049157158775647365, "pattern_recognition": 0.049157158775647365}, "confidences_used": {"markov_chain": 0.6158904429020023, "frequency_analysis": 0.5158904429020023, "trend_following": 0.41589044290200217, "pattern_recognition": 0.31589044290200186}}, {"period": "25057", "predicted_state": ["3:2", 0.5348094373873141], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5348094373873141], "frequency_analysis": ["3:2", 0.5348094373873141], "trend_following": ["3:2", 0.5348094373873141], "pattern_recognition": ["3:2", 0.5348094373873141]}, "weights_used": {"markov_chain": 0.5898790464288798, "frequency_analysis": 0.3118077791663069, "trend_following": 0.04915658720240665, "pattern_recognition": 0.04915658720240665}, "confidences_used": {"markov_chain": 0.6119013986118021, "frequency_analysis": 0.5119013986118021, "trend_following": 0.41190139861180197, "pattern_recognition": 0.31190139861180166}}, {"period": "25056", "predicted_state": ["3:2", 0.5551264086169904], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5551264086169904], "frequency_analysis": ["3:2", 0.5551264086169904], "trend_following": ["3:2", 0.5551264086169904], "pattern_recognition": ["3:2", 0.5551264086169904]}, "weights_used": {"markov_chain": 0.5898730760495583, "frequency_analysis": 0.31181474460884856, "trend_following": 0.04915608967079653, "pattern_recognition": 0.04915608967079653}, "confidences_used": {"markov_chain": 0.607911258750622, "frequency_analysis": 0.5079112587506219, "trend_following": 0.4079112587506218, "pattern_recognition": 0.3079112587506215}}, {"period": "25055", "predicted_state": ["3:2", 0.5558340797819225], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5558340797819225], "frequency_analysis": ["3:2", 0.5558340797819225], "trend_following": ["3:2", 0.5558340797819225], "pattern_recognition": ["3:2", 0.5558340797819225]}, "weights_used": {"markov_chain": 0.5898678515380369, "frequency_analysis": 0.31182083987229037, "trend_following": 0.049155654294836405, "pattern_recognition": 0.049155654294836405}, "confidences_used": {"markov_chain": 0.6039201328755599, "frequency_analysis": 0.5039201328755598, "trend_following": 0.4039201328755596, "pattern_recognition": 0.3039201328755593}}, {"period": "25054", "predicted_state": ["3:2", 0.5206665438723141], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5206665438723141], "frequency_analysis": ["3:2", 0.5206665438723141], "trend_following": ["3:2", 0.5206665438723141], "pattern_recognition": ["3:2", 0.5206665438723141]}, "weights_used": {"markov_chain": 0.5898632587924666, "frequency_analysis": 0.3118261980754557, "trend_following": 0.04915527156603888, "pattern_recognition": 0.04915527156603888}, "confidences_used": {"markov_chain": 0.599928119588004, "frequency_analysis": 0.49992811958800387, "trend_following": 0.39992811958800367, "pattern_recognition": 0.3}}, {"period": "25053", "predicted_state": ["3:2", 0.485163258820469], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.485163258820469], "frequency_analysis": ["3:2", 0.485163258820469], "trend_following": ["3:2", 0.485163258820469], "pattern_recognition": ["3:2", 0.485163258820469]}, "weights_used": {"markov_chain": 0.5898592056051009, "frequency_analysis": 0.311830926794049, "trend_following": 0.04915493380042507, "pattern_recognition": 0.04915493380042507}, "confidences_used": {"markov_chain": 0.5959353076292037, "frequency_analysis": 0.4959353076292035, "trend_following": 0.3959353076292033, "pattern_recognition": 0.3}}, {"period": "25052", "predicted_state": ["3:2", 0.5140394746860119], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5140394746860119], "frequency_analysis": ["3:2", 0.5140394746860119], "trend_following": ["3:2", 0.5140394746860119], "pattern_recognition": ["3:2", 0.5140394746860119]}, "weights_used": {"markov_chain": 0.5898556166927377, "frequency_analysis": 0.31183511385847273, "trend_following": 0.04915463472439481, "pattern_recognition": 0.04915463472439481}, "confidences_used": {"markov_chain": 0.5919417768662834, "frequency_analysis": 0.4919417768662832, "trend_following": 0.391941776866283, "pattern_recognition": 0.3}}, {"period": "25068", "predicted_state": ["2:3", 0.43598067981592525], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.43598067981592525], "frequency_analysis": ["2:3", 0.43598067981592525], "trend_following": ["2:3", 0.43598067981592525], "pattern_recognition": ["2:3", 0.43598067981592525]}, "weights_used": {"markov_chain": 0.5898524299611022, "frequency_analysis": 0.3118388317120475, "trend_following": 0.049154369163425186, "pattern_recognition": 0.049154369163425186}, "confidences_used": {"markov_chain": 0.5879475991796551, "frequency_analysis": 0.4879475991796549, "trend_following": 0.3879475991796547, "pattern_recognition": 0.3}}, {"period": "25067", "predicted_state": ["2:3", 0.44680001256702706], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44680001256702706], "frequency_analysis": ["2:3", 0.44680001256702706], "trend_following": ["2:3", 0.44680001256702706], "pattern_recognition": ["2:3", 0.44680001256702706]}, "weights_used": {"markov_chain": 0.5898495936844638, "frequency_analysis": 0.3118421407014589, "trend_following": 0.049154132807038654, "pattern_recognition": 0.049154132807038654}, "confidences_used": {"markov_chain": 0.5839528392616896, "frequency_analysis": 0.48395283926168947, "trend_following": 0.38395283926168927, "pattern_recognition": 0.3}}, {"period": "25066", "predicted_state": ["2:3", 0.411204235393924], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.411204235393924], "frequency_analysis": ["2:3", 0.411204235393924], "trend_following": ["2:3", 0.411204235393924], "pattern_recognition": ["2:3", 0.411204235393924]}, "weights_used": {"markov_chain": 0.5898470643653985, "frequency_analysis": 0.3118450915737018, "trend_following": 0.049153922030449874, "pattern_recognition": 0.049153922030449874}, "confidences_used": {"markov_chain": 0.5799575553355207, "frequency_analysis": 0.4799575553355206, "trend_following": 0.3799575553355204, "pattern_recognition": 0.3}}, {"period": "25065", "predicted_state": ["2:3", 0.44352197959814055], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44352197959814055], "frequency_analysis": ["2:3", 0.44352197959814055], "trend_following": ["2:3", 0.44352197959814055], "pattern_recognition": ["2:3", 0.44352197959814055]}, "weights_used": {"markov_chain": 0.5898448051011577, "frequency_analysis": 0.31184772738198274, "trend_following": 0.04915373375842981, "pattern_recognition": 0.04915373375842981}, "confidences_used": {"markov_chain": 0.5759617998019687, "frequency_analysis": 0.47596179980196857, "trend_following": 0.37596179980196837, "pattern_recognition": 0.3}}, {"period": "25064", "predicted_state": ["2:3", 0.45279234503216], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45279234503216], "frequency_analysis": ["2:3", 0.45279234503216], "trend_following": ["2:3", 0.45279234503216], "pattern_recognition": ["2:3", 0.45279234503216]}, "weights_used": {"markov_chain": 0.5898427843284245, "frequency_analysis": 0.31185008495017136, "trend_following": 0.049153565360702055, "pattern_recognition": 0.049153565360702055}, "confidences_used": {"markov_chain": 0.5719656198217719, "frequency_analysis": 0.47196561982177176, "trend_following": 0.37196561982177156, "pattern_recognition": 0.3}}, {"period": "25063", "predicted_state": ["3:2", 0.47413250351941116], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.47413250351941116], "frequency_analysis": ["3:2", 0.47413250351941116], "trend_following": ["3:2", 0.47413250351941116], "pattern_recognition": ["3:2", 0.47413250351941116]}, "weights_used": {"markov_chain": 0.5898409748516138, "frequency_analysis": 0.31185219600645053, "trend_following": 0.04915341457096782, "pattern_recognition": 0.04915341457096782}, "confidences_used": {"markov_chain": 0.5679690578395947, "frequency_analysis": 0.46796905783959464, "trend_following": 0.36796905783959444, "pattern_recognition": 0.3}}, {"period": "25062", "predicted_state": ["3:2", 0.499576214545371], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.499576214545371], "frequency_analysis": ["3:2", 0.499576214545371], "trend_following": ["3:2", 0.499576214545371], "pattern_recognition": ["3:2", 0.499576214545371]}, "weights_used": {"markov_chain": 0.5898393530844653, "frequency_analysis": 0.3118540880681238, "trend_following": 0.049153279423705454, "pattern_recognition": 0.049153279423705454}, "confidences_used": {"markov_chain": 0.5639721520556353, "frequency_analysis": 0.46397215205563525, "trend_following": 0.36397215205563505, "pattern_recognition": 0.3}}, {"period": "25061", "predicted_state": ["3:2", 0.5295819170810003], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5295819170810003], "frequency_analysis": ["3:2", 0.5295819170810003], "trend_following": ["3:2", 0.5295819170810003], "pattern_recognition": ["3:2", 0.5295819170810003]}, "weights_used": {"markov_chain": 0.5898378984528121, "frequency_analysis": 0.31185578513838585, "trend_following": 0.04915315820440101, "pattern_recognition": 0.04915315820440101}, "confidences_used": {"markov_chain": 0.5599749368500718, "frequency_analysis": 0.4599749368500718, "trend_following": 0.3599749368500716, "pattern_recognition": 0.3}}, {"period": "25060", "predicted_state": ["3:2", 0.5565369333793396], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5565369333793396], "frequency_analysis": ["3:2", 0.5565369333793396], "trend_following": ["3:2", 0.5565369333793396], "pattern_recognition": ["3:2", 0.5565369333793396]}, "weights_used": {"markov_chain": 0.5898365929197756, "frequency_analysis": 0.31185730826026187, "trend_following": 0.0491530494099813, "pattern_recognition": 0.0491530494099813}, "confidences_used": {"markov_chain": 0.5559774431650647, "frequency_analysis": 0.45597744316506467, "trend_following": 0.35597744316506447, "pattern_recognition": 0.3}}, {"period": "25059", "predicted_state": ["3:2", 0.5189080923233678], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5189080923233678], "frequency_analysis": ["3:2", 0.5189080923233678], "trend_following": ["3:2", 0.5189080923233678], "pattern_recognition": ["3:2", 0.5189080923233678]}, "weights_used": {"markov_chain": 0.5898354206045157, "frequency_analysis": 0.31185867596139843, "trend_following": 0.04915295171704298, "pattern_recognition": 0.04915295171704298}, "confidences_used": {"markov_chain": 0.5519796988485582, "frequency_analysis": 0.45197969884855826, "trend_following": 0.35197969884855806, "pattern_recognition": 0.3}}], "weight_momentum": {"markov_chain": -0.01998984942427888, "frequency_analysis": -0.01998984942427888, "trend_following": -0.01998984942427888, "pattern_recognition": -0.01998984942427888}, "confidence_momentum": {"markov_chain": -0.003997969884855776, "frequency_analysis": -0.003997969884855776, "trend_following": -0.003997969884855776, "pattern_recognition": -0.003997969884855776}}