{"current_weights": {"markov_chain": 0.5898524299611022, "frequency_analysis": 0.3118388317120475, "trend_following": 0.049154369163425186, "pattern_recognition": 0.049154369163425186}, "current_confidences": {"markov_chain": 0.5879475991796551, "frequency_analysis": 0.4879475991796549, "trend_following": 0.3879475991796547, "pattern_recognition": 0.3}, "evidence_performance": {"markov_chain": {"hits": 0, "total": 62, "accuracy": 0.0}, "frequency_analysis": {"hits": 0, "total": 62, "accuracy": 0.0}, "trend_following": {"hits": 0, "total": 62, "accuracy": 0.0}, "pattern_recognition": {"hits": 0, "total": 62, "accuracy": 0.0}}, "prediction_history": [{"period": "25066", "predicted_state": ["2:3", 0.41125801393155614], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.41125801393155614], "frequency_analysis": ["2:3", 0.41125801393155614], "trend_following": ["2:3", 0.41125801393155614], "pattern_recognition": ["2:3", 0.41125801393155614]}, "weights_used": {"markov_chain": 0.5903004976978576, "frequency_analysis": 0.311316086019166, "trend_following": 0.04919170814148814, "pattern_recognition": 0.04919170814148814}, "confidences_used": {"markov_chain": 0.6675689894534282, "frequency_analysis": 0.5675689894534282, "trend_following": 0.4675689894534281, "pattern_recognition": 0.3675689894534278}}, {"period": "25065", "predicted_state": ["2:3", 0.4434619207395983], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4434619207395983], "frequency_analysis": ["2:3", 0.4434619207395983], "trend_following": ["2:3", 0.4434619207395983], "pattern_recognition": ["2:3", 0.4434619207395983]}, "weights_used": {"markov_chain": 0.5902110369557733, "frequency_analysis": 0.3114204568849311, "trend_following": 0.04918425307964778, "pattern_recognition": 0.04918425307964778}, "confidences_used": {"markov_chain": 0.6636120905080855, "frequency_analysis": 0.5636120905080854, "trend_following": 0.46361209050808533, "pattern_recognition": 0.363612090508085}}, {"period": "25064", "predicted_state": ["2:3", 0.4528253167982506], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4528253167982506], "frequency_analysis": ["2:3", 0.4528253167982506], "trend_following": ["2:3", 0.4528253167982506], "pattern_recognition": ["2:3", 0.4528253167982506]}, "weights_used": {"markov_chain": 0.5901416164372849, "frequency_analysis": 0.3115014474898342, "trend_following": 0.049178468036440416, "pattern_recognition": 0.049178468036440416}, "confidences_used": {"markov_chain": 0.6596508814572769, "frequency_analysis": 0.5596508814572768, "trend_following": 0.45965088145727684, "pattern_recognition": 0.35965088145727653}}, {"period": "25068", "predicted_state": ["2:3", 0.4362131560694935], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4362131560694935], "frequency_analysis": ["2:3", 0.4362131560694935], "trend_following": ["2:3", 0.4362131560694935], "pattern_recognition": ["2:3", 0.4362131560694935]}, "weights_used": {"markov_chain": 0.5900872991040192, "frequency_analysis": 0.3115648177119775, "trend_following": 0.04917394159200161, "pattern_recognition": 0.04917394159200161}, "confidences_used": {"markov_chain": 0.6556857933115493, "frequency_analysis": 0.5556857933115492, "trend_following": 0.4556857933115492, "pattern_recognition": 0.35568579331154887}}, {"period": "25067", "predicted_state": ["2:3", 0.4465434952520471], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4465434952520471], "frequency_analysis": ["2:3", 0.4465434952520471], "trend_following": ["2:3", 0.4465434952520471], "pattern_recognition": ["2:3", 0.4465434952520471]}, "weights_used": {"markov_chain": 0.5900444152686619, "frequency_analysis": 0.31161484885322766, "trend_following": 0.049170367939055167, "pattern_recognition": 0.049170367939055167}, "confidences_used": {"markov_chain": 0.6517172139803944, "frequency_analysis": 0.5517172139803943, "trend_following": 0.45171721398039427, "pattern_recognition": 0.35171721398039396}}, {"period": "25066", "predicted_state": ["2:3", 0.4111505336653884], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.4111505336653884], "frequency_analysis": ["2:3", 0.4111505336653884], "trend_following": ["2:3", 0.4111505336653884], "pattern_recognition": ["2:3", 0.4111505336653884]}, "weights_used": {"markov_chain": 0.5900102326018515, "frequency_analysis": 0.31165472863117316, "trend_following": 0.04916751938348763, "pattern_recognition": 0.04916751938348763}, "confidences_used": {"markov_chain": 0.647745492582355, "frequency_analysis": 0.5477454925823549, "trend_following": 0.44774549258235485, "pattern_recognition": 0.34774549258235454}}, {"period": "25065", "predicted_state": ["2:3", 0.44344650094483534], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.44344650094483534], "frequency_analysis": ["2:3", 0.44344650094483534], "trend_following": ["2:3", 0.44344650094483534], "pattern_recognition": ["2:3", 0.44344650094483534]}, "weights_used": {"markov_chain": 0.5899827121628033, "frequency_analysis": 0.3116868358100628, "trend_following": 0.049165226013566946, "pattern_recognition": 0.049165226013566946}, "confidences_used": {"markov_chain": 0.6437709433241195, "frequency_analysis": 0.5437709433241195, "trend_following": 0.44377094332411937, "pattern_recognition": 0.34377094332411906}}, {"period": "25064", "predicted_state": ["2:3", 0.45278295447464884], "actual_state": "1:4", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["2:3", 0.45278295447464884], "frequency_analysis": ["2:3", 0.45278295447464884], "trend_following": ["2:3", 0.45278295447464884], "pattern_recognition": ["2:3", 0.45278295447464884]}, "weights_used": {"markov_chain": 0.5899603281956658, "frequency_analysis": 0.3117129504383899, "trend_following": 0.049163360682972154, "pattern_recognition": 0.049163360682972154}, "confidences_used": {"markov_chain": 0.6397938489917077, "frequency_analysis": 0.5397938489917076, "trend_following": 0.4397938489917075, "pattern_recognition": 0.33979384899170717}}, {"period": "25063", "predicted_state": ["3:2", 0.4745881960216694], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.4745881960216694], "frequency_analysis": ["3:2", 0.4745881960216694], "trend_following": ["3:2", 0.4745881960216694], "pattern_recognition": ["3:2", 0.4745881960216694]}, "weights_used": {"markov_chain": 0.5899419350926959, "frequency_analysis": 0.3117344090585214, "trend_following": 0.049161827924391326, "pattern_recognition": 0.049161827924391326}, "confidences_used": {"markov_chain": 0.635814464092537, "frequency_analysis": 0.5358144640925369, "trend_following": 0.43581446409253677, "pattern_recognition": 0.33581446409253646}}, {"period": "25062", "predicted_state": ["3:2", 0.5001254421282686], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5001254421282686], "frequency_analysis": ["3:2", 0.5001254421282686], "trend_following": ["3:2", 0.5001254421282686], "pattern_recognition": ["3:2", 0.5001254421282686]}, "weights_used": {"markov_chain": 0.5899266691950957, "frequency_analysis": 0.31175221927238833, "trend_following": 0.04916055576625798, "pattern_recognition": 0.04916055576625798}, "confidences_used": {"markov_chain": 0.6318330176832833, "frequency_analysis": 0.5318330176832832, "trend_following": 0.43183301768328314, "pattern_recognition": 0.3318330176832828}}, {"period": "25061", "predicted_state": ["3:2", 0.5303994204771677], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5303994204771677], "frequency_analysis": ["3:2", 0.5303994204771677], "trend_following": ["3:2", 0.5303994204771677], "pattern_recognition": ["3:2", 0.5303994204771677]}, "weights_used": {"markov_chain": 0.5899138762998662, "frequency_analysis": 0.31176714431682256, "trend_following": 0.04915948969165553, "pattern_recognition": 0.04915948969165553}, "confidences_used": {"markov_chain": 0.627849715914955, "frequency_analysis": 0.5278497159149549, "trend_following": 0.42784971591495485, "pattern_recognition": 0.32784971591495454}}, {"period": "25060", "predicted_state": ["3:2", 0.5574525220446169], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5574525220446169], "frequency_analysis": ["3:2", 0.5574525220446169], "trend_following": ["3:2", 0.5574525220446169], "pattern_recognition": ["3:2", 0.5574525220446169]}, "weights_used": {"markov_chain": 0.5899030581229558, "frequency_analysis": 0.3117797655232181, "trend_following": 0.049158588176912994, "pattern_recognition": 0.049158588176912994}, "confidences_used": {"markov_chain": 0.6238647443234595, "frequency_analysis": 0.5238647443234594, "trend_following": 0.4238647443234594, "pattern_recognition": 0.3238647443234591}}, {"period": "25059", "predicted_state": ["3:2", 0.5194213208113839], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5194213208113839], "frequency_analysis": ["3:2", 0.5194213208113839], "trend_following": ["3:2", 0.5194213208113839], "pattern_recognition": ["3:2", 0.5194213208113839]}, "weights_used": {"markov_chain": 0.5898938327366815, "frequency_analysis": 0.3117905284738716, "trend_following": 0.04915781939472346, "pattern_recognition": 0.04915781939472346}, "confidences_used": {"markov_chain": 0.6198782698911136, "frequency_analysis": 0.5198782698911135, "trend_following": 0.4198782698911135, "pattern_recognition": 0.3198782698911132}}, {"period": "25058", "predicted_state": ["3:2", 0.576347309093719], "actual_state": "4:1", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.576347309093719], "frequency_analysis": ["3:2", 0.576347309093719], "trend_following": ["3:2", 0.576347309093719], "pattern_recognition": ["3:2", 0.576347309093719]}, "weights_used": {"markov_chain": 0.5898859053077683, "frequency_analysis": 0.31179977714093693, "trend_following": 0.049157158775647365, "pattern_recognition": 0.049157158775647365}, "confidences_used": {"markov_chain": 0.6158904429020023, "frequency_analysis": 0.5158904429020023, "trend_following": 0.41589044290200217, "pattern_recognition": 0.31589044290200186}}, {"period": "25057", "predicted_state": ["3:2", 0.5348094373873141], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5348094373873141], "frequency_analysis": ["3:2", 0.5348094373873141], "trend_following": ["3:2", 0.5348094373873141], "pattern_recognition": ["3:2", 0.5348094373873141]}, "weights_used": {"markov_chain": 0.5898790464288798, "frequency_analysis": 0.3118077791663069, "trend_following": 0.04915658720240665, "pattern_recognition": 0.04915658720240665}, "confidences_used": {"markov_chain": 0.6119013986118021, "frequency_analysis": 0.5119013986118021, "trend_following": 0.41190139861180197, "pattern_recognition": 0.31190139861180166}}, {"period": "25056", "predicted_state": ["3:2", 0.5551264086169904], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5551264086169904], "frequency_analysis": ["3:2", 0.5551264086169904], "trend_following": ["3:2", 0.5551264086169904], "pattern_recognition": ["3:2", 0.5551264086169904]}, "weights_used": {"markov_chain": 0.5898730760495583, "frequency_analysis": 0.31181474460884856, "trend_following": 0.04915608967079653, "pattern_recognition": 0.04915608967079653}, "confidences_used": {"markov_chain": 0.607911258750622, "frequency_analysis": 0.5079112587506219, "trend_following": 0.4079112587506218, "pattern_recognition": 0.3079112587506215}}, {"period": "25055", "predicted_state": ["3:2", 0.5558340797819225], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5558340797819225], "frequency_analysis": ["3:2", 0.5558340797819225], "trend_following": ["3:2", 0.5558340797819225], "pattern_recognition": ["3:2", 0.5558340797819225]}, "weights_used": {"markov_chain": 0.5898678515380369, "frequency_analysis": 0.31182083987229037, "trend_following": 0.049155654294836405, "pattern_recognition": 0.049155654294836405}, "confidences_used": {"markov_chain": 0.6039201328755599, "frequency_analysis": 0.5039201328755598, "trend_following": 0.4039201328755596, "pattern_recognition": 0.3039201328755593}}, {"period": "25054", "predicted_state": ["3:2", 0.5206665438723141], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5206665438723141], "frequency_analysis": ["3:2", 0.5206665438723141], "trend_following": ["3:2", 0.5206665438723141], "pattern_recognition": ["3:2", 0.5206665438723141]}, "weights_used": {"markov_chain": 0.5898632587924666, "frequency_analysis": 0.3118261980754557, "trend_following": 0.04915527156603888, "pattern_recognition": 0.04915527156603888}, "confidences_used": {"markov_chain": 0.599928119588004, "frequency_analysis": 0.49992811958800387, "trend_following": 0.39992811958800367, "pattern_recognition": 0.3}}, {"period": "25053", "predicted_state": ["3:2", 0.485163258820469], "actual_state": "3:2", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.485163258820469], "frequency_analysis": ["3:2", 0.485163258820469], "trend_following": ["3:2", 0.485163258820469], "pattern_recognition": ["3:2", 0.485163258820469]}, "weights_used": {"markov_chain": 0.5898592056051009, "frequency_analysis": 0.311830926794049, "trend_following": 0.04915493380042507, "pattern_recognition": 0.04915493380042507}, "confidences_used": {"markov_chain": 0.5959353076292037, "frequency_analysis": 0.4959353076292035, "trend_following": 0.3959353076292033, "pattern_recognition": 0.3}}, {"period": "25052", "predicted_state": ["3:2", 0.5140394746860119], "actual_state": "2:3", "is_hit": false, "bayes_probability": 0.5, "evidence_predictions": {"markov_chain": ["3:2", 0.5140394746860119], "frequency_analysis": ["3:2", 0.5140394746860119], "trend_following": ["3:2", 0.5140394746860119], "pattern_recognition": ["3:2", 0.5140394746860119]}, "weights_used": {"markov_chain": 0.5898556166927377, "frequency_analysis": 0.31183511385847273, "trend_following": 0.04915463472439481, "pattern_recognition": 0.04915463472439481}, "confidences_used": {"markov_chain": 0.5919417768662834, "frequency_analysis": 0.4919417768662832, "trend_following": 0.391941776866283, "pattern_recognition": 0.3}}], "weight_momentum": {"markov_chain": -0.019970888433141397, "frequency_analysis": -0.019970888433141397, "trend_following": -0.019970888433141397, "pattern_recognition": -0.019970888433141397}, "confidence_momentum": {"markov_chain": -0.0039941776866282775, "frequency_analysis": -0.0039941776866282775, "trend_following": -0.0039941776866282775, "pattern_recognition": -0.0039941776866282775}}