"""
数据增强器
生成更多高质量的训练数据
"""

import numpy as np
import pandas as pd
from typing import List, Tu<PERSON>, Dict, Optional
from collections import Counter
from utils import parse_numbers, load_data


class DataAugmenter:
    """数据增强器"""
    
    def __init__(self):
        """初始化数据增强器"""
        self.data = load_data()
        self.augmentation_strategies = {
            'noise_injection': self._noise_injection,
            'pattern_variation': self._pattern_variation,
            'statistical_sampling': self._statistical_sampling,
            'sequence_permutation': self._sequence_permutation,
            'interpolation': self._interpolation
        }
    
    def augment_dataset(self, X: np.ndarray, y: np.ndarray, 
                       augmentation_factor: int = 3,
                       strategies: List[str] = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        增强数据集
        
        Args:
            X: 原始特征矩阵
            y: 原始标签
            augmentation_factor: 增强倍数
            strategies: 使用的增强策略
            
        Returns:
            Tuple[np.ndar<PERSON>, np.ndarray]: 增强后的特征和标签
        """
        if strategies is None:
            strategies = ['noise_injection', 'pattern_variation', 'statistical_sampling']
        
        print(f"开始数据增强，原始数据: {X.shape[0]} 样本")
        
        augmented_X = [X]
        augmented_y = [y]
        
        for strategy in strategies:
            if strategy in self.augmentation_strategies:
                print(f"  应用 {strategy} 策略...")
                
                aug_X, aug_y = self.augmentation_strategies[strategy](
                    X, y, augmentation_factor // len(strategies)
                )
                
                augmented_X.append(aug_X)
                augmented_y.append(aug_y)
        
        # 合并所有增强数据
        valid_X = [x for x in augmented_X if len(x) > 0]
        valid_y = [y for y in augmented_y if len(y) > 0]

        if valid_X and valid_y:
            final_X = np.vstack(valid_X)
            final_y = np.vstack(valid_y)
        else:
            final_X = X
            final_y = y
        
        print(f"数据增强完成，最终数据: {final_X.shape[0]} 样本")
        
        return final_X, final_y
    
    def _noise_injection(self, X: np.ndarray, y: np.ndarray, 
                        factor: int) -> Tuple[np.ndarray, np.ndarray]:
        """噪声注入增强"""
        n_samples = X.shape[0]
        n_augmented = n_samples * factor
        
        # 随机选择样本进行增强
        indices = np.random.choice(n_samples, n_augmented, replace=True)
        augmented_X = X[indices].copy()
        augmented_y = y[indices].copy()
        
        # 添加高斯噪声
        noise_std = np.std(X, axis=0) * 0.1  # 噪声标准差为原数据的10%
        noise = np.random.normal(0, noise_std, augmented_X.shape)
        augmented_X += noise
        
        return augmented_X, augmented_y
    
    def _pattern_variation(self, X: np.ndarray, y: np.ndarray, 
                          factor: int) -> Tuple[np.ndarray, np.ndarray]:
        """模式变化增强"""
        n_samples = X.shape[0]
        n_augmented = n_samples * factor
        
        augmented_X = []
        augmented_y = []
        
        for _ in range(n_augmented):
            # 随机选择一个样本
            idx = np.random.randint(n_samples)
            sample_X = X[idx].copy()
            sample_y = y[idx].copy()
            
            # 应用模式变化
            # 1. 特征缩放
            scale_factor = np.random.uniform(0.9, 1.1, sample_X.shape)
            sample_X *= scale_factor
            
            # 2. 特征旋转（对于某些特征）
            if len(sample_X) > 10:
                rotation_indices = np.random.choice(len(sample_X), 5, replace=False)
                # 使用简单的旋转变换
                angle = np.random.uniform(0, 2 * np.pi)
                rotation_factor = np.cos(angle)
                sample_X[rotation_indices] *= rotation_factor
            
            # 3. 特征重排（保持某些约束）
            if len(sample_X) > 20:
                perm_indices = np.random.choice(len(sample_X), 10, replace=False)
                np.random.shuffle(sample_X[perm_indices])
            
            augmented_X.append(sample_X)
            augmented_y.append(sample_y)
        
        return np.array(augmented_X), np.array(augmented_y)
    
    def _statistical_sampling(self, X: np.ndarray, y: np.ndarray, 
                            factor: int) -> Tuple[np.ndarray, np.ndarray]:
        """统计采样增强"""
        n_samples = X.shape[0]
        n_augmented = n_samples * factor
        
        # 分析每个类别的统计特征
        class_stats = {}
        unique_classes = np.unique(np.argmax(y, axis=1))
        
        for class_idx in unique_classes:
            class_mask = np.argmax(y, axis=1) == class_idx
            class_data = X[class_mask]
            
            class_stats[class_idx] = {
                'mean': np.mean(class_data, axis=0),
                'std': np.std(class_data, axis=0),
                'min': np.min(class_data, axis=0),
                'max': np.max(class_data, axis=0)
            }
        
        augmented_X = []
        augmented_y = []
        
        for _ in range(n_augmented):
            # 随机选择一个类别
            class_idx = np.random.choice(unique_classes)
            stats = class_stats[class_idx]
            
            # 从该类别的统计分布中采样
            sample_X = np.random.normal(stats['mean'], stats['std'] + 1e-8)
            
            # 确保在合理范围内
            sample_X = np.clip(sample_X, stats['min'], stats['max'])
            
            # 创建对应的标签
            sample_y = np.zeros(y.shape[1])
            sample_y[class_idx] = 1
            
            augmented_X.append(sample_X)
            augmented_y.append(sample_y)
        
        return np.array(augmented_X), np.array(augmented_y)
    
    def _sequence_permutation(self, X: np.ndarray, y: np.ndarray, 
                            factor: int) -> Tuple[np.ndarray, np.ndarray]:
        """序列排列增强"""
        n_samples = X.shape[0]
        n_augmented = n_samples * factor
        
        indices = np.random.choice(n_samples, n_augmented, replace=True)
        augmented_X = X[indices].copy()
        augmented_y = y[indices].copy()
        
        # 对每个样本应用序列排列
        for i in range(n_augmented):
            sample = augmented_X[i]
            
            # 将特征分组并在组内排列
            n_features = len(sample)
            group_size = max(5, n_features // 10)
            
            for start in range(0, n_features, group_size):
                end = min(start + group_size, n_features)
                group = sample[start:end].copy()
                np.random.shuffle(group)
                sample[start:end] = group
        
        return augmented_X, augmented_y
    
    def _interpolation(self, X: np.ndarray, y: np.ndarray, 
                      factor: int) -> Tuple[np.ndarray, np.ndarray]:
        """插值增强"""
        n_samples = X.shape[0]
        n_augmented = n_samples * factor
        
        augmented_X = []
        augmented_y = []
        
        for _ in range(n_augmented):
            # 随机选择两个同类样本
            class_labels = np.argmax(y, axis=1)
            unique_classes = np.unique(class_labels)
            
            selected_class = np.random.choice(unique_classes)
            class_indices = np.where(class_labels == selected_class)[0]
            
            if len(class_indices) >= 2:
                idx1, idx2 = np.random.choice(class_indices, 2, replace=False)
                
                # 线性插值
                alpha = np.random.beta(2, 2)  # Beta分布，偏向中间值
                interpolated_X = alpha * X[idx1] + (1 - alpha) * X[idx2]
                interpolated_y = y[idx1]  # 使用相同的标签
                
                augmented_X.append(interpolated_X)
                augmented_y.append(interpolated_y)
        
        return np.array(augmented_X), np.array(augmented_y)
    
    def generate_synthetic_lottery_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """
        生成合成彩票数据
        
        Args:
            n_samples: 生成样本数
            
        Returns:
            pd.DataFrame: 合成数据
        """
        print(f"生成 {n_samples} 个合成彩票样本...")
        
        if len(self.data) == 0:
            print("没有历史数据，无法生成合成数据")
            return pd.DataFrame()
        
        # 分析历史数据的统计特征
        red_stats, blue_stats = self._analyze_lottery_statistics()
        
        synthetic_data = []
        
        for i in range(n_samples):
            # 生成红球
            red_balls = self._generate_synthetic_red_balls(red_stats)
            
            # 生成蓝球
            blue_balls = self._generate_synthetic_blue_balls(blue_stats)
            
            # 创建数据行
            synthetic_row = {
                '期号': 30000 + i,  # 使用不同的期号范围
                '红球1': red_balls[0],
                '红球2': red_balls[1],
                '红球3': red_balls[2],
                '红球4': red_balls[3],
                '红球5': red_balls[4],
                '蓝球1': blue_balls[0],
                '蓝球2': blue_balls[1]
            }
            
            synthetic_data.append(synthetic_row)
        
        synthetic_df = pd.DataFrame(synthetic_data)
        print(f"合成数据生成完成: {len(synthetic_df)} 期")
        
        return synthetic_df
    
    def _analyze_lottery_statistics(self) -> Tuple[Dict, Dict]:
        """分析彩票统计特征"""
        red_numbers = []
        blue_numbers = []
        red_sums = []
        blue_sums = []
        red_spans = []
        blue_gaps = []
        
        for _, row in self.data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            
            red_numbers.extend(red_balls)
            blue_numbers.extend(blue_balls)
            
            red_sums.append(sum(red_balls))
            blue_sums.append(sum(blue_balls))
            
            red_spans.append(max(red_balls) - min(red_balls))
            if len(blue_balls) == 2:
                blue_gaps.append(abs(blue_balls[1] - blue_balls[0]))
        
        # 红球统计
        red_freq = Counter(red_numbers)
        red_stats = {
            'frequency': red_freq,
            'sum_mean': np.mean(red_sums),
            'sum_std': np.std(red_sums),
            'span_mean': np.mean(red_spans),
            'span_std': np.std(red_spans),
            'prob_dist': np.array([red_freq.get(i, 0) for i in range(1, 36)])
        }
        red_stats['prob_dist'] = red_stats['prob_dist'] / red_stats['prob_dist'].sum()
        
        # 蓝球统计
        blue_freq = Counter(blue_numbers)
        blue_stats = {
            'frequency': blue_freq,
            'sum_mean': np.mean(blue_sums),
            'sum_std': np.std(blue_sums),
            'gap_mean': np.mean(blue_gaps) if blue_gaps else 3,
            'gap_std': np.std(blue_gaps) if blue_gaps else 2,
            'prob_dist': np.array([blue_freq.get(i, 0) for i in range(1, 13)])
        }
        blue_stats['prob_dist'] = blue_stats['prob_dist'] / blue_stats['prob_dist'].sum()
        
        return red_stats, blue_stats
    
    def _generate_synthetic_red_balls(self, red_stats: Dict) -> List[int]:
        """生成合成红球"""
        # 方法1: 基于概率分布采样
        if np.random.random() < 0.7:
            red_balls = list(np.random.choice(
                range(1, 36), 5, replace=False, p=red_stats['prob_dist']
            ))
        else:
            # 方法2: 基于统计约束生成
            target_sum = np.random.normal(red_stats['sum_mean'], red_stats['sum_std'])
            target_span = np.random.normal(red_stats['span_mean'], red_stats['span_std'])
            
            red_balls = self._generate_constrained_red_balls(target_sum, target_span)
        
        return sorted(red_balls)
    
    def _generate_synthetic_blue_balls(self, blue_stats: Dict) -> List[int]:
        """生成合成蓝球"""
        # 方法1: 基于概率分布采样
        if np.random.random() < 0.8:
            blue_balls = list(np.random.choice(
                range(1, 13), 2, replace=False, p=blue_stats['prob_dist']
            ))
        else:
            # 方法2: 基于统计约束生成
            target_sum = np.random.normal(blue_stats['sum_mean'], blue_stats['sum_std'])
            target_gap = np.random.normal(blue_stats['gap_mean'], blue_stats['gap_std'])
            
            blue_balls = self._generate_constrained_blue_balls(target_sum, target_gap)
        
        return sorted(blue_balls)
    
    def _generate_constrained_red_balls(self, target_sum: float, target_span: float) -> List[int]:
        """生成满足约束的红球"""
        for _ in range(100):  # 最多尝试100次
            red_balls = list(np.random.choice(range(1, 36), 5, replace=False))
            
            actual_sum = sum(red_balls)
            actual_span = max(red_balls) - min(red_balls)
            
            if (abs(actual_sum - target_sum) < 20 and 
                abs(actual_span - target_span) < 10):
                return red_balls
        
        # 如果无法满足约束，返回随机组合
        return list(np.random.choice(range(1, 36), 5, replace=False))
    
    def _generate_constrained_blue_balls(self, target_sum: float, target_gap: float) -> List[int]:
        """生成满足约束的蓝球"""
        for _ in range(50):  # 最多尝试50次
            blue_balls = list(np.random.choice(range(1, 13), 2, replace=False))
            
            actual_sum = sum(blue_balls)
            actual_gap = abs(blue_balls[1] - blue_balls[0])
            
            if (abs(actual_sum - target_sum) < 5 and 
                abs(actual_gap - target_gap) < 3):
                return blue_balls
        
        # 如果无法满足约束，返回随机组合
        return list(np.random.choice(range(1, 13), 2, replace=False))
    
    def create_balanced_dataset(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """创建平衡数据集"""
        print("创建平衡数据集...")
        
        # 统计各类别样本数
        class_counts = np.sum(y, axis=0)
        max_count = int(np.max(class_counts))
        
        balanced_X = []
        balanced_y = []
        
        for class_idx in range(y.shape[1]):
            class_mask = y[:, class_idx] == 1
            class_X = X[class_mask]
            class_y = y[class_mask]
            
            current_count = len(class_X)
            needed_count = max_count - current_count
            
            if needed_count > 0:
                # 使用插值增强生成缺失样本
                augmented_X, augmented_y = self._interpolation(
                    class_X, class_y, needed_count // current_count + 1
                )
                
                # 选择需要的样本数
                augmented_X = augmented_X[:needed_count]
                augmented_y = augmented_y[:needed_count]
                
                balanced_X.extend([class_X, augmented_X])
                balanced_y.extend([class_y, augmented_y])
            else:
                balanced_X.append(class_X)
                balanced_y.append(class_y)
        
        final_X = np.vstack(balanced_X)
        final_y = np.vstack(balanced_y)
        
        print(f"平衡数据集创建完成: {final_X.shape[0]} 样本")
        
        return final_X, final_y


def test_data_augmentation():
    """测试数据增强"""
    # 创建模拟数据
    np.random.seed(42)
    n_samples, n_features, n_classes = 100, 20, 6
    
    X = np.random.randn(n_samples, n_features)
    y = np.random.randint(0, n_classes, n_samples)
    y_onehot = np.eye(n_classes)[y]
    
    # 测试数据增强
    augmenter = DataAugmenter()
    
    print("原始数据形状:", X.shape, y_onehot.shape)
    
    # 数据增强
    aug_X, aug_y = augmenter.augment_dataset(X, y_onehot, augmentation_factor=2)
    print("增强后数据形状:", aug_X.shape, aug_y.shape)
    
    # 创建平衡数据集
    balanced_X, balanced_y = augmenter.create_balanced_dataset(X, y_onehot)
    print("平衡数据集形状:", balanced_X.shape, balanced_y.shape)
    
    # 生成合成彩票数据
    synthetic_lottery = augmenter.generate_synthetic_lottery_data(100)
    print("合成彩票数据形状:", synthetic_lottery.shape)


if __name__ == "__main__":
    test_data_augmentation()
