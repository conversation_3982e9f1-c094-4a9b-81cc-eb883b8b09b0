#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
寻找平均杀号6个、全中率大于92%的组合
"""

import pandas as pd
import numpy as np
from typing import List, Dict, <PERSON><PERSON>
from itertools import combinations
import random

# 导入现有的算法系统
from single_output_algorithm_system import SingleOutputAlgorithmSystem

class SixKillHighRateComboFinder:
    def __init__(self):
        self.system = SingleOutputAlgorithmSystem()
        self.target_kill_count = 6
        self.target_success_rate = 0.92
        
    def load_data(self) -> bool:
        """加载数据"""
        return self.system.load_data()

    def find_target_combinations(self, test_periods: int = 50) -> List[Tuple]:
        """寻找符合条件的组合"""
        print(f"\n🎯 寻找平均杀号{self.target_kill_count}个、全中率>{self.target_success_rate:.0%}的组合")
        print("=" * 80)
        
        # 首先测试所有算法
        all_results = self.system.test_all_algorithms(test_periods)
        
        # 筛选出高质量算法（成功率>85%）
        quality_algorithms = []
        for algo_name, result in all_results.items():
            if result['success_rate'] >= 0.85 and result['total_kills'] >= test_periods * 0.5:
                quality_algorithms.append(algo_name)
        
        print(f"✅ 筛选出{len(quality_algorithms)}个高质量算法")
        
        # 测试不同规模的组合来达到目标杀号数
        target_combinations = []
        
        # 测试6-8算法组合（预期能产生6个左右杀号）
        for combo_size in range(6, 9):
            print(f"\n🔍 测试{combo_size}算法组合...")

            # 限制搜索范围以避免内存问题
            max_samples = 200  # 减少采样数量
            combo_count = 0

            # 使用生成器避免一次性生成所有组合
            for combo in combinations(quality_algorithms, combo_size):
                if combo_count >= max_samples:
                    break

                combo_stats = self._test_combination_detailed(combo, test_periods)

                # 检查是否符合条件
                avg_kills = combo_stats['total_kills'] / combo_stats['total_periods'] if combo_stats['total_periods'] > 0 else 0

                if (combo_stats['perfect_rate'] >= self.target_success_rate and
                    5.0 <= avg_kills <= 7.0):  # 允许5-7个杀号的范围
                    target_combinations.append((combo, combo_stats))
                    print(f"  ✅ 找到符合条件的组合: 全中率{combo_stats['perfect_rate']:.1%}, 平均杀号{avg_kills:.1f}")

                combo_count += 1
        
        # 按全中率排序
        target_combinations.sort(key=lambda x: (x[1]['perfect_rate'], -x[1]['total_kills']/x[1]['total_periods']), reverse=True)
        
        return target_combinations

    def _test_combination_detailed(self, algorithms: Tuple[str], test_periods: int = 50) -> Dict:
        """详细测试组合"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0,
            'period_details': []
        }
        
        for i in range(test_periods):
            if i + 5 >= len(self.system.data):
                break
                
            # 获取历史数据
            current_period = self.system.data.iloc[i]
            period_data = {
                'current': current_period,
                'last': self.system.data.iloc[i + 1],
                'prev2': self.system.data.iloc[i + 2],
                'prev3': self.system.data.iloc[i + 3],
                'prev4': self.system.data.iloc[i + 4],
                'prev5': self.system.data.iloc[i + 5]
            }
            
            # 解析号码
            from test_kill_algorithm import parse_numbers
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period_data['last'])
            period2_red, _ = parse_numbers(period_data['prev2'])
            
            # 收集本期所有杀号
            period_kills = set()  # 使用set去重
            
            for algo_name in algorithms:
                try:
                    kill_number = self.system.derived_algorithms[algo_name](period_data)
                    if 1 <= kill_number <= 35 and kill_number not in (period1_red + period2_red):
                        period_kills.add(kill_number)
                except Exception:
                    continue
            
            period_kills = list(period_kills)  # 转回list
            
            if period_kills:
                stats['total_periods'] += 1
                stats['total_kills'] += len(period_kills)
                
                # 检查杀号成功情况
                successful_kills = sum(1 for kill in period_kills if kill not in current_red)
                stats['successful_kills'] += successful_kills
                
                # 检查是否全中
                is_perfect = successful_kills == len(period_kills)
                if is_perfect:
                    stats['perfect_periods'] += 1
                
                # 记录期详情
                stats['period_details'].append({
                    'period': current_period['期号'],
                    'kills': period_kills,
                    'successful_kills': successful_kills,
                    'total_kills': len(period_kills),
                    'perfect': is_perfect,
                    'actual_red': current_red
                })
        
        # 计算最终统计
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
        
        if stats['total_kills'] > 0:
            stats['kill_success_rate'] = stats['successful_kills'] / stats['total_kills']
        
        return stats

    def print_target_results(self, combinations: List[Tuple]):
        """打印符合条件的组合结果"""
        print(f"\n📊 符合条件的组合结果 (平均杀号≈{self.target_kill_count}个, 全中率>{self.target_success_rate:.0%})")
        print("=" * 80)
        
        if not combinations:
            print("❌ 未找到符合条件的组合")
            return
        
        print(f"✅ 找到{len(combinations)}个符合条件的组合:")
        
        for i, (combo, stats) in enumerate(combinations[:10], 1):  # 显示前10个
            avg_kills = stats['total_kills'] / stats['total_periods']
            status = "🎯" if stats['perfect_rate'] >= 0.95 else "✅" if stats['perfect_rate'] >= 0.92 else "⚠️"
            
            print(f"\n{i}. 全中率:{stats['perfect_rate']:6.1%} "
                  f"杀号成功率:{stats['kill_success_rate']:6.1%} "
                  f"平均杀号:{avg_kills:.1f} "
                  f"({stats['perfect_periods']}/{stats['total_periods']}) {status}")
            
            print(f"   算法组合({len(combo)}个): {', '.join(combo)}")
            
            # 显示失败期数
            failed_periods = [p for p in stats['period_details'] if not p['perfect']]
            if failed_periods:
                print(f"   失败期数: {len(failed_periods)}期")
                for detail in failed_periods[:3]:  # 只显示前3期失败
                    kills_str = ','.join(map(str, detail['kills']))
                    actual_str = ','.join(map(str, detail['actual_red']))
                    print(f"     {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                          f"成功{detail['successful_kills']}/{detail['total_kills']}")
        
        # 推荐最佳组合
        if combinations:
            best_combo, best_stats = combinations[0]
            avg_kills = best_stats['total_kills'] / best_stats['total_periods']
            
            print(f"\n🏆 推荐最佳组合:")
            print(f"算法组合: {', '.join(best_combo)}")
            print(f"全中率: {best_stats['perfect_rate']:.1%}")
            print(f"杀号成功率: {best_stats['kill_success_rate']:.1%}")
            print(f"平均杀号数: {avg_kills:.1f}")
            print(f"测试期数: {best_stats['total_periods']}")
            
            # 显示最近几期表现
            print(f"\n最近5期表现:")
            for detail in best_stats['period_details'][:5]:
                kills_str = ','.join(map(str, detail['kills']))
                actual_str = ','.join(map(str, detail['actual_red']))
                status = "✅" if detail['perfect'] else "❌"
                print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                      f"成功{detail['successful_kills']}/{detail['total_kills']} {status}")

    def find_alternative_combinations(self, test_periods: int = 50) -> List[Tuple]:
        """寻找备选组合（稍微放宽条件）"""
        print(f"\n🔍 寻找备选组合 (全中率>90%, 平均杀号5-7个)")
        print("=" * 60)
        
        # 测试所有算法
        all_results = self.system.test_all_algorithms(test_periods)
        
        # 筛选算法
        quality_algorithms = []
        for algo_name, result in all_results.items():
            if result['success_rate'] >= 0.80 and result['total_kills'] >= test_periods * 0.4:
                quality_algorithms.append(algo_name)
        
        alternative_combinations = []
        
        # 测试5-7算法组合
        for combo_size in range(5, 8):
            max_samples = 150  # 减少采样数量
            combo_count = 0

            # 使用生成器避免内存问题
            for combo in combinations(quality_algorithms, combo_size):
                if combo_count >= max_samples:
                    break

                combo_stats = self._test_combination_detailed(combo, test_periods)

                avg_kills = combo_stats['total_kills'] / combo_stats['total_periods'] if combo_stats['total_periods'] > 0 else 0

                if (combo_stats['perfect_rate'] >= 0.90 and
                    4.5 <= avg_kills <= 7.5):
                    alternative_combinations.append((combo, combo_stats))

                combo_count += 1
        
        # 按全中率排序
        alternative_combinations.sort(key=lambda x: x[1]['perfect_rate'], reverse=True)
        
        return alternative_combinations[:20]  # 返回前20个

def main():
    """主函数"""
    print("🎯 寻找平均杀号6个、全中率>92%的组合")
    print("=" * 60)
    
    # 初始化搜索器
    finder = SixKillHighRateComboFinder()
    
    # 加载数据
    if not finder.load_data():
        return
    
    print(f"✅ 成功加载数据: {len(finder.system.data)} 期")
    
    # 设置随机种子以确保结果可重现
    random.seed(42)
    
    # 寻找目标组合
    target_combinations = finder.find_target_combinations(test_periods=50)
    
    # 打印结果
    finder.print_target_results(target_combinations)
    
    # 如果没找到理想组合，寻找备选组合
    if len(target_combinations) < 5:
        print(f"\n🔍 目标组合较少，寻找备选组合...")
        alternative_combinations = finder.find_alternative_combinations(test_periods=50)
        
        print(f"\n📊 备选组合 (全中率>90%, 平均杀号5-7个)")
        print("=" * 60)
        
        for i, (combo, stats) in enumerate(alternative_combinations[:5], 1):
            avg_kills = stats['total_kills'] / stats['total_periods']
            status = "🎯" if stats['perfect_rate'] >= 0.95 else "✅" if stats['perfect_rate'] >= 0.90 else "⚠️"
            
            print(f"{i}. 全中率:{stats['perfect_rate']:6.1%} "
                  f"平均杀号:{avg_kills:.1f} "
                  f"算法数:{len(combo)} {status}")
    
    print(f"\n🎉 搜索完成！")

if __name__ == "__main__":
    main()
