#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试杀号成功率计算修复效果
"""

import pandas as pd
from kill_count_analysis_system import KillCountAnalysisSystem

def test_kill_success_rate_calculation():
    """测试杀号成功率计算"""
    print("🔧 测试杀号成功率计算修复效果")
    print("=" * 80)
    
    # 初始化系统
    system = KillCountAnalysisSystem()
    
    if not system.load_data():
        print("❌ 数据加载失败")
        return
    
    system.initialize_system()
    
    # 测试少量杀号数量以便观察
    print("🧪 测试杀号成功率计算逻辑:")
    
    # 只测试1-5个杀号，便于验证
    test_counts = [1, 2, 3, 4, 5]
    
    for kill_count in test_counts:
        print(f"\n📊 测试{kill_count}个杀号:")
        
        stats = system._test_specific_kill_count(kill_count)
        
        print(f"  总期数: {stats['total_periods']}")
        print(f"  杀号成功期数: {stats['successful_periods']}")
        print(f"  全中期数: {stats['perfect_periods']}")
        print(f"  杀号成功率: {stats['kill_success_rate']:.1%}")
        print(f"  全中率: {stats['perfect_rate']:.1%}")
        
        # 验证逻辑正确性
        if stats['total_periods'] > 0:
            expected_kill_rate = stats['successful_periods'] / stats['total_periods']
            expected_perfect_rate = stats['perfect_periods'] / stats['total_periods']
            
            if abs(stats['kill_success_rate'] - expected_kill_rate) < 0.001:
                print(f"  ✅ 杀号成功率计算正确")
            else:
                print(f"  ❌ 杀号成功率计算错误")
            
            if abs(stats['perfect_rate'] - expected_perfect_rate) < 0.001:
                print(f"  ✅ 全中率计算正确")
            else:
                print(f"  ❌ 全中率计算错误")
            
            # 检查杀号成功率和全中率是否相等（应该相等）
            if abs(stats['kill_success_rate'] - stats['perfect_rate']) < 0.001:
                print(f"  ✅ 杀号成功率 = 全中率（逻辑正确）")
            else:
                print(f"  ⚠️  杀号成功率 ≠ 全中率（可能有问题）")
        
        # 显示前3期的详细情况
        if stats['period_details']:
            print(f"  前3期详情:")
            for i, detail in enumerate(stats['period_details'][:3], 1):
                period = detail['period']
                kills = detail['kills']
                successful = detail['successful']
                total = detail['total']
                kill_success = detail.get('kill_success', False)
                actual_red = detail['actual_red']
                
                print(f"    第{i}期({period}): 杀号{kills}")
                print(f"      开奖红球: {actual_red}")
                print(f"      成功杀号: {successful}/{total}")
                print(f"      杀号成功: {'✅' if kill_success else '❌'}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🔍 测试边界情况:")
    print("-" * 60)
    
    # 模拟测试数据
    test_cases = [
        {
            'name': '全部杀号成功',
            'kills': [1, 2, 3],
            'actual_red': [4, 5, 6, 7, 8],
            'expected_success': True
        },
        {
            'name': '部分杀号失败',
            'kills': [1, 2, 3],
            'actual_red': [1, 5, 6, 7, 8],  # 包含杀号1
            'expected_success': False
        },
        {
            'name': '全部杀号失败',
            'kills': [1, 2, 3],
            'actual_red': [1, 2, 3, 7, 8],  # 包含所有杀号
            'expected_success': False
        },
        {
            'name': '空杀号列表',
            'kills': [],
            'actual_red': [1, 2, 3, 4, 5],
            'expected_success': True  # 没有杀号，应该算成功
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 测试: {case['name']}")
        
        kills = case['kills']
        actual_red = case['actual_red']
        expected = case['expected_success']
        
        # 模拟计算逻辑
        successful_kills = sum(1 for k in kills if k not in actual_red)
        is_kill_success = successful_kills == len(kills)
        
        print(f"  杀号: {kills}")
        print(f"  开奖: {actual_red}")
        print(f"  成功杀号数: {successful_kills}/{len(kills)}")
        print(f"  杀号成功: {is_kill_success}")
        print(f"  预期结果: {expected}")
        
        if is_kill_success == expected:
            print(f"  ✅ 测试通过")
        else:
            print(f"  ❌ 测试失败")

def compare_old_vs_new_logic():
    """对比新旧逻辑的差异"""
    print(f"\n📊 对比新旧逻辑的差异:")
    print("-" * 60)
    
    # 模拟数据
    test_data = [
        {'kills': [1, 2, 3], 'actual': [4, 5, 6, 7, 8]},  # 全部成功
        {'kills': [1, 2, 3], 'actual': [1, 5, 6, 7, 8]},  # 1个失败
        {'kills': [1, 2, 3], 'actual': [1, 2, 6, 7, 8]},  # 2个失败
        {'kills': [1, 2, 3], 'actual': [1, 2, 3, 7, 8]},  # 全部失败
    ]
    
    print("期数 | 杀号 | 开奖 | 成功杀号 | 旧逻辑成功率 | 新逻辑成功")
    print("-" * 70)
    
    old_total_kills = 0
    old_successful_kills = 0
    new_successful_periods = 0
    total_periods = len(test_data)
    
    for i, data in enumerate(test_data, 1):
        kills = data['kills']
        actual = data['actual']
        
        # 旧逻辑：按单个杀号计算
        successful_kills = sum(1 for k in kills if k not in actual)
        old_total_kills += len(kills)
        old_successful_kills += successful_kills
        
        # 新逻辑：按期数计算
        is_period_success = successful_kills == len(kills)
        if is_period_success:
            new_successful_periods += 1
        
        print(f" {i:2d}  | {kills} | {actual} | {successful_kills:2d}/{len(kills)} | {successful_kills/len(kills):.1%} | {'✅' if is_period_success else '❌'}")
    
    # 计算最终成功率
    old_rate = old_successful_kills / old_total_kills if old_total_kills > 0 else 0
    new_rate = new_successful_periods / total_periods if total_periods > 0 else 0
    
    print("-" * 70)
    print(f"旧逻辑成功率: {old_rate:.1%} ({old_successful_kills}/{old_total_kills})")
    print(f"新逻辑成功率: {new_rate:.1%} ({new_successful_periods}/{total_periods})")
    print()
    print("📝 差异说明:")
    print("  旧逻辑: 按单个杀号的成功率计算，部分成功也算贡献")
    print("  新逻辑: 按期数计算，全部杀号正确才算该期成功")
    print("  新逻辑更严格，更符合实际杀号的成功定义")

def main():
    """主函数"""
    try:
        test_kill_success_rate_calculation()
        test_edge_cases()
        compare_old_vs_new_logic()
        
        print(f"\n🎯 修复总结:")
        print("=" * 80)
        print("✅ 修复了杀号成功率计算逻辑")
        print("✅ 现在按期数计算成功率，而不是按单个杀号")
        print("✅ 全部杀号正确才算该期成功")
        print("✅ 杀号成功率 = 杀号成功的期数 / 总期数")
        print("✅ 更符合实际杀号的成功定义")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
