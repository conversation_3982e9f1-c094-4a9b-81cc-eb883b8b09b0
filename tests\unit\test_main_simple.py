"""
简化的主程序测试
"""

from utils import load_data, parse_numbers
from analyzer import LotteryAnalyzer
from markov_model import MarkovModel
from bayes_selector import BayesSelector

def test_prediction():
    """测试预测功能"""
    print("开始测试预测功能...")
    
    try:
        # 加载数据
        data = load_data()
        print(f"数据加载成功，共{len(data)}行")
        
        # 模拟预测第0期（使用第1期之后的数据训练）
        train_data = data.iloc[1:].copy()  # 使用第1期之后的数据训练
        print(f"训练数据: {len(train_data)}行")
        
        # 创建分析器
        train_analyzer = LotteryAnalyzer(train_data)
        
        # 训练马尔科夫模型
        red_markov = MarkovModel('red')
        red_markov.train(train_analyzer.get_feature_sequence('red_odd_even'))
        
        # 创建贝叶斯选择器
        red_bayes = BayesSelector('red')
        red_bayes.set_prior_probabilities(
            train_analyzer.calculate_state_frequencies('red_odd_even')
        )
        
        # 获取当前期（第0期）的实际状态
        current_row = data.iloc[0]
        current_red, current_blue = parse_numbers(current_row)
        print(f"当前期号: {current_row['期号']}")
        print(f"当前红球: {current_red}")
        
        # 计算当前状态
        from utils import calculate_odd_even_ratio, ratio_to_state
        red_odd, red_even = calculate_odd_even_ratio(current_red)
        current_red_odd_even = ratio_to_state((red_odd, red_even))
        print(f"当前红球奇偶比状态: {current_red_odd_even}")
        
        # 马尔科夫预测
        predicted_state, probability = red_markov.predict_next_state(current_red_odd_even)
        print(f"马尔科夫预测下一状态: {predicted_state}, 概率: {probability:.3f}")
        
        # 贝叶斯选择
        markov_probs = red_markov.get_state_probabilities(current_red_odd_even)
        bayes_state, bayes_prob = red_bayes.select_best_state([markov_probs])
        print(f"贝叶斯选择状态: {bayes_state}, 概率: {bayes_prob:.3f}")
        
        print("\n预测功能测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_prediction()
