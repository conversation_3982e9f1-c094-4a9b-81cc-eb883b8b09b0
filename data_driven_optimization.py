#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于数据驱动的真正优化算法
基于回测结果，使用表现最好的简单频率算法作为基础进行改进
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict

class DataDrivenOptimizedKiller:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        
    def calculate_optimized_red_kills(self, recent_periods: List[List[int]], target_count: int = 13) -> List[int]:
        """
        基于回测结果的真正优化红球杀号
        
        核心策略：
        1. 以简单频率算法为主（回测表现最好）
        2. 加入少量模式分析（回测表现第二）
        3. 避免过度复杂化
        """
        if len(recent_periods) < 2:
            return list(range(1, target_count + 1))
        
        # 策略1: 频率分析 (权重70% - 因为回测表现最好)
        frequency_kills = self._get_frequency_based_kills(target_count)
        
        # 策略2: 简单模式分析 (权重30% - 回测表现第二)
        pattern_kills = self._get_simple_pattern_kills(recent_periods, target_count)
        
        # 简单加权融合
        kill_scores = defaultdict(float)
        
        # 频率杀号评分
        for i, num in enumerate(frequency_kills):
            kill_scores[num] += 0.7 * (target_count - i) / target_count
        
        # 模式杀号评分
        for i, num in enumerate(pattern_kills):
            kill_scores[num] += 0.3 * (target_count - i) / target_count
        
        # 应用过滤规则
        filtered_scores = self._apply_simple_filters(kill_scores, recent_periods)
        
        # 选择得分最高的杀号
        sorted_kills = sorted(filtered_scores.items(), key=lambda x: x[1], reverse=True)
        final_kills = [num for num, score in sorted_kills[:target_count]]
        
        return final_kills
    
    def _get_frequency_based_kills(self, target_count: int) -> List[int]:
        """
        基于频率的杀号 (回测表现最好的算法)
        """
        try:
            # 统计最近100期的频率
            all_numbers = []
            for _, row in self.data.head(100).iterrows():
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(row)
                all_numbers.extend(red_balls)
            
            frequencies = Counter(all_numbers)
            
            # 选择频率最低的号码作为杀号
            sorted_nums = sorted(frequencies.items(), key=lambda x: x[1])
            return [num for num, freq in sorted_nums[:target_count]]
        except:
            return list(range(1, target_count + 1))
    
    def _get_simple_pattern_kills(self, recent_periods: List[List[int]], target_count: int) -> List[int]:
        """
        简单模式分析 (回测表现第二的算法)
        """
        try:
            if not recent_periods:
                return list(range(1, target_count + 1))
            
            last_period = recent_periods[0]
            
            # 分析上期特征
            last_odd_count = sum(1 for num in last_period if num % 2 == 1)
            last_large_count = sum(1 for num in last_period if num > 17)
            
            kill_candidates = []
            
            # 基于奇偶模式杀号
            if last_odd_count >= 3:  # 上期奇数多，杀奇数
                kill_candidates.extend([1, 3, 5, 7, 9, 11, 13, 15])
            else:  # 上期偶数多，杀偶数
                kill_candidates.extend([2, 4, 6, 8, 10, 12, 14, 16])
            
            # 基于大小模式杀号
            if last_large_count >= 3:  # 上期大数多，杀大数
                kill_candidates.extend([19, 21, 23, 25, 27, 29, 31, 33, 35])
            else:  # 上期小数多，杀小数
                kill_candidates.extend([1, 2, 3, 4, 5, 6, 7, 8, 9])
            
            # 去重并限制数量
            unique_candidates = list(set(kill_candidates))
            return unique_candidates[:target_count]
        except:
            return list(range(1, target_count + 1))
    
    def _apply_simple_filters(self, kill_scores: Dict[int, float], recent_periods: List[List[int]]) -> Dict[int, float]:
        """
        应用简单过滤规则
        """
        filtered_scores = {}
        
        # 获取前两期号码，避免杀掉刚出现的号码
        recent_numbers = set()
        if len(recent_periods) >= 2:
            recent_numbers.update(recent_periods[0])
            recent_numbers.update(recent_periods[1])
        
        for num, score in kill_scores.items():
            # 基本范围检查
            if not (1 <= num <= 35):
                continue
            
            # 避免杀掉前两期出现的号码（但不完全排除）
            if num in recent_numbers:
                score *= 0.5  # 降权但不完全排除
            
            filtered_scores[num] = score
        
        return filtered_scores

class ImprovedKillSystem:
    """改进的杀号系统 - 集成到主系统"""
    
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.data_driven_killer = DataDrivenOptimizedKiller(data)
    
    def calculate_red_kills(self, recent_periods: List[List[int]], target_count: int = 13) -> List[int]:
        """
        计算红球杀号 - 使用数据驱动的优化算法
        """
        return self.data_driven_killer.calculate_optimized_red_kills(recent_periods, target_count)

def test_improved_algorithm():
    """测试改进的算法"""
    print("🧪 测试基于数据驱动的改进算法")
    print("=" * 60)
    
    # 加载数据
    try:
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 简单测试
        improved_killer = DataDrivenOptimizedKiller(data)
        
        # 获取最近几期数据
        recent_periods = []
        for i in range(6):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(data.iloc[i])
                recent_periods.append(red_balls)
        
        print(f"\n📊 使用最近{len(recent_periods)}期数据进行测试:")
        for i, period in enumerate(recent_periods):
            print(f"  第{i+1}期: {sorted(period)}")
        
        # 测试改进算法
        improved_kills = improved_killer.calculate_optimized_red_kills(recent_periods, 13)
        print(f"\n🎯 改进算法杀号: {sorted(improved_kills)} (共{len(improved_kills)}个)")
        
        # 分析特征
        if improved_kills:
            odd_count = sum(1 for num in improved_kills if num % 2 == 1)
            small_count = sum(1 for num in improved_kills if num <= 17)
            print(f"  特征分析: 奇数{odd_count}个, 偶数{13-odd_count}个, 小数{small_count}个, 大数{13-small_count}个")
            print(f"  号码范围: {min(improved_kills)}-{max(improved_kills)}")
        
        print(f"\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_algorithm()
