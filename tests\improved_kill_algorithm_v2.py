#!/usr/bin/env python3
"""
基于失败模式分析的改进杀号算法 v2.0
主要改进：
1. 避免假冷号误杀
2. 增加蓝球杀号多样性
3. 保护高命中率号码
4. 引入和值特征保护
"""

import sys
import os
from collections import defaultdict, Counter
from typing import Dict, List
import random

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from advanced_probabilistic_system import AdvancedProbabilisticSystem

class ImprovedKillAlgorithmV2(AdvancedProbabilisticSystem):
    """改进的杀号算法 v2.0"""
    
    def __init__(self):
        super().__init__()
        
        # 基于失败模式分析的黑名单
        self.red_fake_cold_blacklist = [4]  # 假冷号黑名单
        self.blue_fake_cold_blacklist = [1]  # 蓝球假冷号
        
        # 高命中率号码保护名单
        self.red_high_hit_protection = [10, 12, 14, 16, 27]
        
        # 蓝球轮换策略
        self.blue_rotation_pool = [2, 3, 5, 6, 8, 9, 11, 12]  # 排除假冷号1
        self.blue_last_killed = []  # 记录最近杀过的蓝球
        
    def predict_kills_by_period(self, period_number: str, red_target_count: int = 5, blue_target_count: int = 1) -> Dict:
        """预测杀号 - 改进版本"""
        try:
            print("🎯 使用改进杀号算法 v2.0")

            # 获取基础杀号
            base_result = super().predict_kills_by_period(period_number, red_target_count, blue_target_count)

            if not base_result:
                print("❌ 基础杀号获取失败")
                return {'red_universal': [], 'blue_universal': []}

            # 改进红球杀号
            improved_red_kills = self._improve_red_kills(base_result.get('red_universal', []))

            # 改进蓝球杀号
            improved_blue_kills = self._improve_blue_kills(base_result.get('blue_universal', []))

            result = {
                'red_universal': improved_red_kills,
                'blue_universal': improved_blue_kills
            }

            print(f"✅ 改进杀号完成: 红球{improved_red_kills}, 蓝球{improved_blue_kills}")
            return result

        except Exception as e:
            print(f"❌ 改进杀号算法出错: {e}")
            return {'red_universal': [], 'blue_universal': []}
    
    def _improve_red_kills(self, base_red_kills):
        """改进红球杀号"""
        print("🔴 改进红球杀号...")
        
        # 1. 移除假冷号
        filtered_kills = [k for k in base_red_kills if k not in self.red_fake_cold_blacklist]
        print(f"   移除假冷号{self.red_fake_cold_blacklist}: {base_red_kills} -> {filtered_kills}")
        
        # 2. 检查高命中率号码保护
        protected_kills = []
        for kill in filtered_kills:
            if kill in self.red_high_hit_protection:
                # 高命中率号码需要额外验证
                if self._verify_high_risk_kill(kill):
                    protected_kills.append(kill)
                else:
                    print(f"   保护高命中率号码: {kill}")
            else:
                protected_kills.append(kill)
        
        # 3. 如果杀号不足，补充安全杀号
        if len(protected_kills) < 5:
            safe_supplements = self._get_safe_red_supplements(protected_kills)
            protected_kills.extend(safe_supplements[:5-len(protected_kills)])
        
        # 4. 和值特征保护
        final_kills = self._apply_sum_protection(protected_kills)
        
        return final_kills[:5]
    
    def _improve_blue_kills(self, base_blue_kills):
        """改进蓝球杀号 - 增加多样性"""
        print("🔵 改进蓝球杀号...")
        
        # 1. 移除假冷号
        filtered_kills = [k for k in base_blue_kills if k not in self.blue_fake_cold_blacklist]
        print(f"   移除蓝球假冷号{self.blue_fake_cold_blacklist}: {base_blue_kills} -> {filtered_kills}")
        
        # 2. 实现轮换策略
        if not filtered_kills:
            # 如果没有基础杀号，使用轮换策略
            rotated_kill = self._get_rotated_blue_kill()
            print(f"   使用轮换策略: {rotated_kill}")
            return [rotated_kill]
        
        # 3. 检查是否需要轮换
        current_kill = filtered_kills[0]
        if self._should_rotate_blue_kill(current_kill):
            rotated_kill = self._get_rotated_blue_kill()
            print(f"   轮换蓝球杀号: {current_kill} -> {rotated_kill}")
            return [rotated_kill]
        
        # 4. 更新轮换记录
        self._update_blue_rotation_history(current_kill)
        
        return filtered_kills[:1]  # 蓝球只杀1个
    
    def _verify_high_risk_kill(self, kill_num):
        """验证高命中率号码是否真的应该被杀"""
        if self.data is None or self.data.empty or len(self.data) < 10:
            return False

        # 检查最近10期的出现情况
        recent_data = self.data.head(10)
        appear_count = 0

        for _, row in recent_data.iterrows():
            red_numbers = self._parse_red_numbers(row)
            if kill_num in red_numbers:
                appear_count += 1

        # 如果最近10期出现超过3次，则不杀
        if appear_count >= 3:
            print(f"   号码{kill_num}最近10期出现{appear_count}次，不予杀除")
            return False

        return True
    
    def _get_safe_red_supplements(self, existing_kills):
        """获取安全的红球补充杀号"""
        # 选择历史低频且不在黑名单的号码
        safe_candidates = []

        if self.data is None or self.data.empty or len(self.data) < 50:
            return []

        # 统计最近50期的频率
        recent_data = self.data.head(50)
        freq_count = defaultdict(int)

        for _, row in recent_data.iterrows():
            red_numbers = self._parse_red_numbers(row)
            for num in red_numbers:
                freq_count[num] += 1

        # 选择低频号码作为补充
        for num in range(1, 36):
            if (num not in existing_kills and
                num not in self.red_fake_cold_blacklist and
                num not in self.red_high_hit_protection and
                freq_count[num] <= 5):  # 50期内出现≤5次
                safe_candidates.append(num)

        # 随机选择以增加多样性
        random.shuffle(safe_candidates)
        return safe_candidates[:3]
    
    def _apply_sum_protection(self, kill_list):
        """应用和值特征保护"""
        if self.data is None or self.data.empty or len(self.data) < 5:
            return kill_list

        # 计算最近5期的平均和值
        recent_sums = []
        for i in range(min(5, len(self.data))):
            row = self.data.iloc[i]
            red_numbers = self._parse_red_numbers(row)
            if red_numbers:  # 确保解析成功
                recent_sums.append(sum(red_numbers))

        if not recent_sums:
            return kill_list

        avg_sum = sum(recent_sums) / len(recent_sums)

        # 如果平均和值在85-105之间（失败期特征），加强保护
        if 85 <= avg_sum <= 105:
            print(f"   检测到失败期和值特征({avg_sum:.1f})，加强保护")
            # 移除可能影响和值的极值号码
            protected_list = [k for k in kill_list if not (k <= 5 or k >= 32)]
            if len(protected_list) < 3:
                protected_list = kill_list  # 如果过度过滤，保持原有
            return protected_list

        return kill_list
    
    def _get_rotated_blue_kill(self):
        """获取轮换的蓝球杀号"""
        # 从轮换池中选择最久没有被杀的号码
        available = [num for num in self.blue_rotation_pool 
                    if num not in self.blue_last_killed[-3:]]  # 避免最近3次
        
        if not available:
            available = self.blue_rotation_pool  # 重置轮换
            self.blue_last_killed = []
        
        # 随机选择以增加不可预测性
        selected = random.choice(available)
        return selected
    
    def _should_rotate_blue_kill(self, current_kill):
        """判断是否需要轮换蓝球杀号"""
        # 如果连续使用同一个杀号超过2次，强制轮换
        if len(self.blue_last_killed) >= 2:
            if self.blue_last_killed[-1] == self.blue_last_killed[-2] == current_kill:
                return True
        return False
    
    def _update_blue_rotation_history(self, kill_num):
        """更新蓝球轮换历史"""
        self.blue_last_killed.append(kill_num)
        if len(self.blue_last_killed) > 10:  # 只保留最近10次记录
            self.blue_last_killed = self.blue_last_killed[-10:]
    
    def _parse_red_numbers(self, row):
        """解析红球号码"""
        try:
            numbers_str = str(row['开奖号码']).strip()
            if '+' in numbers_str:
                red_part = numbers_str.split('+')[0].strip()
                return [int(x) for x in red_part.split()]
            return []
        except:
            return []

def test_improved_algorithm():
    """测试改进的算法"""
    print("🧪 测试改进的杀号算法 v2.0")
    print("=" * 60)

    # 创建改进的系统
    improved_system = ImprovedKillAlgorithmV2()

    # 模拟几次预测
    for i in range(5):
        print(f"\n第{i+1}次预测:")
        period_num = f"2506{i}"
        result = improved_system.predict_kills_by_period(period_num)
        print(f"结果: 红球{result['red_universal']}, 蓝球{result['blue_universal']}")

if __name__ == "__main__":
    test_improved_algorithm()
