#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
蓝球算法优化分析
分析当前蓝球算法的表现并提出优化方案
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict
import time

class BlueBallOptimizationAnalysis:
    def __init__(self):
        self.data = None
        self.test_periods = 30  # 回测期数
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
            
            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"✅ 成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue
            
            print(f"❌ 未找到数据文件: {data_files}")
            return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_current_blue_algorithm(self):
        """分析当前蓝球算法的表现"""
        print("🔵 分析当前蓝球算法表现")
        print("=" * 60)
        
        if not self.load_data():
            return
        
        # 测试当前蓝球算法
        current_results = self._test_current_blue_algorithm()
        
        # 分析蓝球数据特征
        blue_features = self._analyze_blue_ball_features()
        
        # 提出优化建议
        optimization_suggestions = self._generate_optimization_suggestions(current_results, blue_features)
        
        return {
            'current_results': current_results,
            'blue_features': blue_features,
            'optimization_suggestions': optimization_suggestions
        }
    
    def _test_current_blue_algorithm(self) -> Dict:
        """测试当前蓝球算法"""
        print("\n🧪 测试当前蓝球算法...")
        
        stats = {
            'total_periods': 0,
            'kill_success_periods': 0,
            'prediction_success_periods': 0,
            'kill_details': [],
            'prediction_details': []
        }
        
        for i in range(self.test_periods):
            if i + 6 >= len(self.data):
                break
            
            try:
                # 获取当前期和历史期数据
                current_period = self.data.iloc[i]
                historical_data = self.data.iloc[i+1:i+101]
                
                # 解析当前期号码
                from test_kill_algorithm import parse_numbers
                _, current_blue = parse_numbers(current_period)
                
                # 获取最近几期蓝球数据
                recent_blue_periods = []
                for j in range(1, 7):
                    if i + j < len(self.data):
                        _, blue_balls = parse_numbers(self.data.iloc[i + j])
                        recent_blue_periods.append(blue_balls)
                
                if len(recent_blue_periods) < 2:
                    continue
                
                # 测试蓝球杀号
                from bayesian_markov_killer import BayesianMarkovKiller
                killer = BayesianMarkovKiller(historical_data)
                blue_kills = killer.calculate_blue_kills(recent_blue_periods, target_count=5)
                
                # 检查杀号成功情况
                kill_success = not any(num in current_blue for num in blue_kills)
                
                stats['total_periods'] += 1
                if kill_success:
                    stats['kill_success_periods'] += 1
                
                stats['kill_details'].append({
                    'period': current_period['期号'],
                    'kills': blue_kills,
                    'actual': current_blue,
                    'success': kill_success,
                    'wrong_kills': [num for num in blue_kills if num in current_blue]
                })
                
                # 测试蓝球大小比预测
                from src.utils.utils import calculate_size_ratio_blue, ratio_to_state
                actual_small, actual_big = calculate_size_ratio_blue(current_blue)
                actual_blue_size = ratio_to_state((actual_small, actual_big))
                
                # 这里可以添加蓝球大小比预测的测试
                # 暂时跳过，专注于杀号分析
                
            except Exception as e:
                continue
        
        # 计算统计数据
        if stats['total_periods'] > 0:
            stats['kill_success_rate'] = stats['kill_success_periods'] / stats['total_periods']
        
        print(f"  测试期数: {stats['total_periods']}")
        print(f"  杀号成功率: {stats['kill_success_rate']:.1%}")
        print(f"  成功期数: {stats['kill_success_periods']}/{stats['total_periods']}")
        
        return stats
    
    def _analyze_blue_ball_features(self) -> Dict:
        """分析蓝球数据特征"""
        print("\n📊 分析蓝球数据特征...")
        
        features = {
            'frequency_analysis': {},
            'position_analysis': {},
            'pattern_analysis': {},
            'trend_analysis': {}
        }
        
        try:
            # 频率分析
            all_blues = []
            position_data = {1: [], 2: []}
            
            for i, row in self.data.head(100).iterrows():
                from test_kill_algorithm import parse_numbers
                _, blue_balls = parse_numbers(row)
                
                if isinstance(blue_balls, list) and len(blue_balls) == 2:
                    all_blues.extend(blue_balls)
                    sorted_blues = sorted(blue_balls)
                    position_data[1].append(sorted_blues[0])
                    position_data[2].append(sorted_blues[1])
                elif isinstance(blue_balls, int):
                    all_blues.append(blue_balls)
                    position_data[1].append(blue_balls)
            
            # 频率统计
            blue_counter = Counter(all_blues)
            features['frequency_analysis'] = {
                'total_count': len(all_blues),
                'frequency_distribution': dict(blue_counter),
                'hot_numbers': [num for num, count in blue_counter.most_common(6)],
                'cold_numbers': [num for num, count in blue_counter.most_common()[-6:]]
            }
            
            # 位置分析
            for pos in [1, 2]:
                if position_data[pos]:
                    pos_counter = Counter(position_data[pos])
                    features['position_analysis'][f'position_{pos}'] = {
                        'most_common': pos_counter.most_common(3),
                        'least_common': pos_counter.most_common()[-3:],
                        'avg_value': np.mean(position_data[pos]),
                        'range': (min(position_data[pos]), max(position_data[pos]))
                    }
            
            # 模式分析
            odd_count = sum(1 for num in all_blues if num % 2 == 1)
            large_count = sum(1 for num in all_blues if num > 6)
            
            features['pattern_analysis'] = {
                'odd_ratio': odd_count / len(all_blues) if all_blues else 0,
                'large_ratio': large_count / len(all_blues) if all_blues else 0,
                'avg_value': np.mean(all_blues) if all_blues else 0
            }
            
            print(f"  频率分析: 热号{features['frequency_analysis']['hot_numbers']}")
            print(f"  频率分析: 冷号{features['frequency_analysis']['cold_numbers']}")
            print(f"  模式分析: 奇数比例{features['pattern_analysis']['odd_ratio']:.1%}")
            print(f"  模式分析: 大数比例{features['pattern_analysis']['large_ratio']:.1%}")
            
        except Exception as e:
            print(f"  特征分析失败: {e}")
        
        return features
    
    def _generate_optimization_suggestions(self, current_results: Dict, blue_features: Dict) -> List[str]:
        """生成优化建议"""
        print("\n💡 生成优化建议...")
        
        suggestions = []
        
        # 基于当前表现分析
        if current_results.get('kill_success_rate', 0) < 0.8:
            suggestions.append("🔧 杀号成功率需要提升 - 当前低于80%目标")
            
            # 分析失败案例
            failed_cases = [detail for detail in current_results.get('kill_details', []) if not detail['success']]
            if failed_cases:
                wrong_numbers = []
                for case in failed_cases:
                    wrong_numbers.extend(case['wrong_kills'])
                
                wrong_counter = Counter(wrong_numbers)
                frequent_wrong = [num for num, count in wrong_counter.most_common(3)]
                suggestions.append(f"⚠️  经常被误杀的号码: {frequent_wrong} - 需要避免杀掉这些号码")
        
        # 基于特征分析的建议
        if blue_features.get('frequency_analysis'):
            hot_numbers = blue_features['frequency_analysis']['hot_numbers']
            cold_numbers = blue_features['frequency_analysis']['cold_numbers']
            suggestions.append(f"📈 优先杀掉真正的冷号: {cold_numbers}")
            suggestions.append(f"🚫 避免杀掉热号: {hot_numbers}")
        
        if blue_features.get('pattern_analysis'):
            odd_ratio = blue_features['pattern_analysis']['odd_ratio']
            if odd_ratio > 0.6:
                suggestions.append("🎯 蓝球偏向奇数 - 杀号时考虑这个趋势")
            elif odd_ratio < 0.4:
                suggestions.append("🎯 蓝球偏向偶数 - 杀号时考虑这个趋势")
        
        # 算法改进建议
        suggestions.extend([
            "🔄 考虑减少杀号数量 - 从5个减少到3个提高精准度",
            "📊 增加位置分析 - 不同位置的蓝球有不同特征",
            "⏰ 加强时间窗口分析 - 近期趋势比历史频率更重要",
            "🎲 引入随机性控制 - 避免过度拟合历史数据"
        ])
        
        for suggestion in suggestions:
            print(f"  {suggestion}")
        
        return suggestions

def main():
    """主函数"""
    analyzer = BlueBallOptimizationAnalysis()
    results = analyzer.analyze_current_blue_algorithm()
    
    print(f"\n🎯 蓝球算法优化分析完成")
    print(f"当前杀号成功率: {results['current_results'].get('kill_success_rate', 0):.1%}")
    print(f"优化建议数量: {len(results['optimization_suggestions'])}")

if __name__ == "__main__":
    main()
