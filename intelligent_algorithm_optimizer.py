#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能算法优化系统
专注于提升算法质量，解决算法实现问题，优化组合性能
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict

# 导入现有的杀号算法
from test_kill_algorithm import KillAlgorithmTester, parse_numbers
from test_algorithm_combinations import AlgorithmCombinationTester

class IntelligentAlgorithmOptimizer:
    def __init__(self):
        self.data = None
        self.base_tester = KillAlgorithmTester()
        self.combo_tester = AlgorithmCombinationTester()
        
        # 已验证的高质量算法（基于之前的测试结果）
        self.verified_high_quality_algorithms = {
            'factorial_kill': 96.7,        # 阶乘杀号法
            'pentagonal_kill': 94.7,       # 五边形数杀号法
            'last_period_plus': 94.7,      # 上期+1杀号法
            'catalan_kill': 94.4,          # 卡塔兰数杀号法
            'prev2_period_pattern_kill': 93.2,  # 上上期模式杀号法
            'direction_kill': 92.3,        # 方位维度杀号法
            'psychology_kill': 92.3,       # 心理学维度杀号法
            'span': 91.7,                  # 跨度计算法
            'energy_kill': 89.7,           # 能量维度杀号法
            'abundant_kill': 78.3          # 过剩数杀号法（需要改进）
        }
        
        # 备选高质量算法
        self.backup_algorithms = {
            'last_period_reverse': 90.2,   # 上期反向杀号法
            'symmetry_kill': 90.0,         # 对称杀号法
            'modular_kill': 90.0,          # 模运算杀号法
            'odd_even': 90.9,              # 奇偶比例法
            'interval_kill': 90.5,         # 区间杀号法
            'prime_kill': 89.5,            # 质数杀号法
            'prev2_period_half': 89.1,     # 上上期÷2杀号法
            'month_kill': 89.1,            # 月份维度杀号法
            'zodiac_kill': 89.1,           # 生肖维度杀号法
            'weather_kill': 88.7,          # 天气维度杀号法
            'season_kill': 88.2,           # 季节维度杀号法
            'prev2_period_reverse': 88.2,  # 上上期反向杀号法
            'prev2_period_diff_kill': 88.2, # 上上期差值杀号法
            'lunar_kill': 88.0,            # 农历维度杀号法
            'weekday_kill': 87.9,          # 星期维度杀号法
            'element_kill': 86.5           # 五行维度杀号法
        }

    def load_data(self) -> bool:
        """加载数据"""
        if self.base_tester.load_data():
            self.data = self.base_tester.data
            self.combo_tester.data = self.data
            return True
        return False

    def test_algorithm_reliability(self, algorithms: List[str], test_periods: int = 50) -> Dict:
        """测试算法的可靠性（是否能产生有效杀号）"""
        print(f"\n🔍 测试算法可靠性 (最近{test_periods}期)")
        print("=" * 80)
        
        reliability_stats = {}
        
        for algo in algorithms:
            stats = {
                'total_attempts': 0,
                'valid_kills': 0,
                'successful_kills': 0,
                'reliability_rate': 0.0,
                'success_rate': 0.0,
                'is_reliable': False
            }
            
            # 逐期测试
            for i in range(test_periods):
                if i + 2 >= len(self.data):
                    break
                    
                # 获取当前期和前两期数据
                current_period = self.data.iloc[i]
                period1_data = self.data.iloc[i + 1]
                period2_data = self.data.iloc[i + 2]
                
                # 解析号码
                current_red, _ = parse_numbers(current_period)
                period1_red, _ = parse_numbers(period1_data)
                period2_red, _ = parse_numbers(period2_data)
                
                stats['total_attempts'] += 1
                
                # 获取算法杀号
                try:
                    kills = self.base_tester._get_single_kill_number(algo, period1_red, period2_red, current_period)
                    
                    if kills and len(kills) > 0:
                        single_kill = kills[0]
                        if 1 <= single_kill <= 35 and single_kill not in (period1_red + period2_red):
                            stats['valid_kills'] += 1
                            is_successful = single_kill not in current_red
                            if is_successful:
                                stats['successful_kills'] += 1
                except Exception as e:
                    # 算法执行出错
                    continue
            
            # 计算可靠性和成功率
            if stats['total_attempts'] > 0:
                stats['reliability_rate'] = stats['valid_kills'] / stats['total_attempts']
                stats['is_reliable'] = stats['reliability_rate'] >= 0.8  # 80%以上的期数能产生有效杀号
            
            if stats['valid_kills'] > 0:
                stats['success_rate'] = stats['successful_kills'] / stats['valid_kills']
            
            reliability_stats[algo] = stats
        
        return reliability_stats

    def create_optimized_combinations(self) -> Dict[str, List[str]]:
        """创建多种优化组合方案"""
        combinations = {}
        
        # 方案1: 超高质量组合（只用95%+成功率的算法）
        combinations['ultra_high_quality'] = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill'
        ]
        
        # 方案2: 高质量平衡组合（90%+成功率算法）
        combinations['high_quality_balanced'] = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill'
        ]
        
        # 方案3: 数学专注组合
        combinations['math_focused'] = [
            'factorial_kill', 'pentagonal_kill', 'catalan_kill', 'prime_kill',
            'modular_kill', 'symmetry_kill', 'interval_kill'
        ]
        
        # 方案4: 时间维度组合
        combinations['time_dimension'] = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'prev2_period_pattern_kill', 'last_period_reverse', 'prev2_period_half'
        ]
        
        # 方案5: 多维度平衡组合
        combinations['multi_dimension'] = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'direction_kill', 'psychology_kill', 'month_kill', 'zodiac_kill'
        ]
        
        # 方案6: 保守高效组合（只用最可靠的算法）
        combinations['conservative_efficient'] = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'span', 'odd_even'
        ]
        
        # 方案7: 激进优化组合（尝试新的高成功率组合）
        combinations['aggressive_optimized'] = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'odd_even', 'interval_kill', 'last_period_reverse', 'modular_kill'
        ]
        
        return combinations

    def comprehensive_test(self, test_periods: int = 50) -> Dict:
        """全面测试各种组合方案"""
        print(f"\n🚀 全面测试优化组合方案")
        print("=" * 80)
        
        # 创建优化组合
        combinations = self.create_optimized_combinations()
        
        # 测试每个组合
        results = {}
        
        for combo_name, algorithms in combinations.items():
            print(f"\n正在测试: {combo_name}")
            print(f"算法数量: {len(algorithms)}")
            print(f"算法列表: {', '.join(algorithms)}")
            
            # 首先测试算法可靠性
            reliability_stats = self.test_algorithm_reliability(algorithms, test_periods)
            
            # 检查是否有不可靠的算法
            unreliable_algos = [algo for algo, stats in reliability_stats.items() if not stats['is_reliable']]
            if unreliable_algos:
                print(f"⚠️ 发现不可靠算法: {', '.join(unreliable_algos)}")
            
            # 测试组合性能
            combo_result = self.combo_tester.test_combination(algorithms, test_periods)
            
            results[combo_name] = {
                'combo_result': combo_result,
                'reliability_stats': reliability_stats,
                'unreliable_algos': unreliable_algos,
                'algorithm_count': len(algorithms),
                'algorithms': algorithms
            }
            
            print(f"结果: 期成功率 {combo_result['success_rate']:.1%}, "
                  f"杀号成功率 {combo_result['kill_success_rate']:.1%}, "
                  f"平均杀号 {combo_result['total_kills']/combo_result['total_periods']:.1f}")
        
        return results

    def analyze_and_recommend(self, results: Dict) -> Dict:
        """分析结果并推荐最佳方案"""
        print(f"\n📊 分析结果并推荐最佳方案")
        print("=" * 80)
        
        # 按期成功率排序
        sorted_results = sorted(
            results.items(),
            key=lambda x: (x[1]['combo_result']['success_rate'], x[1]['combo_result']['kill_success_rate']),
            reverse=True
        )
        
        print("🏆 组合方案排行榜 (按期成功率排序):")
        for i, (combo_name, result) in enumerate(sorted_results, 1):
            combo_result = result['combo_result']
            status = "🎯" if combo_result['success_rate'] >= 0.8 else "⚠️" if combo_result['success_rate'] >= 0.7 else "❌"
            reliability_issues = "⚠️" if result['unreliable_algos'] else "✅"
            
            print(f"  {i}. {combo_name:25} "
                  f"期成功率:{combo_result['success_rate']:6.1%} "
                  f"杀号成功率:{combo_result['kill_success_rate']:6.1%} "
                  f"算法数:{result['algorithm_count']} "
                  f"可靠性:{reliability_issues} {status}")
        
        # 推荐最佳方案
        best_combo_name, best_result = sorted_results[0]
        
        print(f"\n🎯 推荐最佳方案: {best_combo_name}")
        print("=" * 60)
        
        best_combo_result = best_result['combo_result']
        print(f"期成功率: {best_combo_result['success_rate']:.1%} ({best_combo_result['successful_periods']}/{best_combo_result['total_periods']})")
        print(f"杀号成功率: {best_combo_result['kill_success_rate']:.1%}")
        print(f"平均杀号数: {best_combo_result['total_kills']/best_combo_result['total_periods']:.1f}")
        print(f"算法列表: {', '.join(best_result['algorithms'])}")
        
        if best_result['unreliable_algos']:
            print(f"⚠️ 注意: 包含不可靠算法 {', '.join(best_result['unreliable_algos'])}")
        else:
            print(f"✅ 所有算法都可靠")
        
        # 显示最近几期详情
        print(f"\n最近5期详情:")
        for detail in best_combo_result['details'][:5]:
            kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
            actual_str = ','.join(map(str, detail['actual_red']))
            status = "✅" if detail['period_success'] else "❌"
            print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                  f"成功{detail['successful_kills']}/{detail['total_kills']} {status}")
        
        return {
            'best_combo_name': best_combo_name,
            'best_result': best_result,
            'all_results': sorted_results
        }

def main():
    """主函数"""
    print("🎯 智能算法优化系统")
    print("=" * 60)
    
    # 初始化优化器
    optimizer = IntelligentAlgorithmOptimizer()
    
    # 加载数据
    if not optimizer.load_data():
        return
    
    # 全面测试
    results = optimizer.comprehensive_test(test_periods=50)
    
    # 分析并推荐
    recommendation = optimizer.analyze_and_recommend(results)
    
    print(f"\n🎉 智能优化完成!")
    print(f"推荐使用: {recommendation['best_combo_name']}")
    print(f"期成功率: {recommendation['best_result']['combo_result']['success_rate']:.1%}")

if __name__ == "__main__":
    main()
