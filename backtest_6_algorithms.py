#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
6算法集成系统回测脚本
验证新增算法对杀号成功率的提升效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_probabilistic_system import AdvancedProbabilisticSystem
from src.utils.utils import load_data, parse_numbers
import pandas as pd

def comprehensive_backtest():
    """全面回测6算法系统"""
    print("🚀 启动6算法集成系统全面回测...")
    print("   目标: 验证新增算法对杀号成功率的提升效果")
    
    # 加载数据
    try:
        data = load_data('dlt_data.csv')
        print(f"✅ 数据加载成功: {len(data)} 期")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 回测参数
    backtest_periods = 15  # 回测期数
    start_index = 200      # 从第200期开始回测
    
    print(f"📊 回测配置:")
    print(f"   回测期数: {backtest_periods}")
    print(f"   开始位置: 第{start_index}期")
    print(f"   结束位置: 第{start_index + backtest_periods}期")
    
    # 初始化系统
    system = AdvancedProbabilisticSystem()
    system.data = data
    
    # 回测结果
    results = []
    red_kill_success = 0
    blue_kill_success = 0
    total_tests = 0
    
    print(f"\n🎯 开始回测...")
    
    for i in range(backtest_periods):
        test_index = start_index + i
        if test_index >= len(data):
            break
            
        # 获取测试期号和实际结果
        test_row = data.iloc[test_index]
        test_period = str(test_row['期号'])
        actual_red, actual_blue = parse_numbers(test_row)
        
        print(f"\n{'='*60}")
        print(f"🔍 回测 {i+1}/{backtest_periods}: 期号 {test_period}")
        print(f"   实际红球: {sorted(actual_red)}")
        print(f"   实际蓝球: {actual_blue}")
        
        try:
            # 预测杀号
            result = system.predict_kills_by_period(test_period, red_target_count=5, blue_target_count=1)
            
            if result['success']:
                pred_red_kills = result['red_kills']
                pred_blue_kills = result['blue_kills']
                
                # 验证红球杀号
                red_kill_hit = all(k not in actual_red for k in pred_red_kills)
                blue_kill_hit = all(k not in actual_blue for k in pred_blue_kills)
                
                print(f"   预测红球杀号: {sorted(pred_red_kills)}")
                print(f"   预测蓝球杀号: {pred_blue_kills}")
                print(f"   红球杀号结果: {'✅ 全中' if red_kill_hit else '❌ 失败'}")
                print(f"   蓝球杀号结果: {'✅ 全中' if blue_kill_hit else '❌ 失败'}")
                
                # 统计成功率
                if red_kill_hit:
                    red_kill_success += 1
                if blue_kill_hit:
                    blue_kill_success += 1
                total_tests += 1
                
                # 记录详细结果
                results.append({
                    'period': test_period,
                    'actual_red': actual_red,
                    'actual_blue': actual_blue,
                    'pred_red_kills': pred_red_kills,
                    'pred_blue_kills': pred_blue_kills,
                    'red_kill_success': red_kill_hit,
                    'blue_kill_success': blue_kill_hit,
                    'weights': system.ensemble_system.weights.copy()
                })
                
            else:
                print(f"   ❌ 预测失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"   ❌ 回测异常: {e}")
    
    # 计算最终统计
    red_success_rate = red_kill_success / total_tests * 100 if total_tests > 0 else 0
    blue_success_rate = blue_kill_success / total_tests * 100 if total_tests > 0 else 0
    overall_success_rate = (red_kill_success + blue_kill_success) / (total_tests * 2) * 100 if total_tests > 0 else 0
    
    print(f"\n{'='*60}")
    print(f"📈 回测结果统计:")
    print(f"   总回测期数: {total_tests}")
    print(f"   红球杀号成功: {red_kill_success}/{total_tests} ({red_success_rate:.1f}%)")
    print(f"   蓝球杀号成功: {blue_kill_success}/{total_tests} ({blue_success_rate:.1f}%)")
    print(f"   综合成功率: {overall_success_rate:.1f}%")
    
    # 权重演化分析
    if results:
        print(f"\n🔧 权重演化分析:")
        initial_weights = results[0]['weights']
        final_weights = results[-1]['weights']
        
        print(f"   初始权重: {initial_weights}")
        print(f"   最终权重: {final_weights}")
        
        print(f"   权重变化:")
        for algo in initial_weights:
            change = final_weights[algo] - initial_weights[algo]
            direction = "↑" if change > 0 else "↓" if change < 0 else "→"
            print(f"     {algo}: {initial_weights[algo]:.3f} → {final_weights[algo]:.3f} ({direction}{abs(change):.3f})")
    
    # 算法贡献分析
    print(f"\n🔍 算法贡献分析:")
    if results:
        # 分析各算法的权重趋势
        algo_performance = {}
        for algo in ['bayesian', 'markov1', 'markov2', 'frequency', 'trend', 'correlation']:
            weights = [r['weights'][algo] for r in results]
            avg_weight = sum(weights) / len(weights)
            max_weight = max(weights)
            min_weight = min(weights)
            
            algo_performance[algo] = {
                'avg': avg_weight,
                'max': max_weight,
                'min': min_weight,
                'stability': max_weight - min_weight
            }
            
            print(f"   {algo:12}: 平均{avg_weight:.3f}, 最高{max_weight:.3f}, 最低{min_weight:.3f}, 波动{max_weight-min_weight:.3f}")
    
    # 性能评估
    print(f"\n🎯 性能评估:")
    if red_success_rate >= 70:
        print(f"🏆 红球杀号表现优秀！成功率 {red_success_rate:.1f}% 超过70%")
    elif red_success_rate >= 50:
        print(f"✅ 红球杀号表现良好！成功率 {red_success_rate:.1f}% 超过50%")
    else:
        print(f"⚠️ 红球杀号需要改进，成功率仅 {red_success_rate:.1f}%")
    
    if blue_success_rate >= 70:
        print(f"🏆 蓝球杀号表现优秀！成功率 {blue_success_rate:.1f}% 超过70%")
    elif blue_success_rate >= 50:
        print(f"✅ 蓝球杀号表现良好！成功率 {blue_success_rate:.1f}% 超过50%")
    else:
        print(f"⚠️ 蓝球杀号需要改进，成功率仅 {blue_success_rate:.1f}%")
    
    # 改进建议
    if overall_success_rate < 60:
        print(f"\n💡 改进建议:")
        print(f"   1. 调整算法权重配置")
        print(f"   2. 增加更多历史数据训练")
        print(f"   3. 优化频率分析算法参数")
        print(f"   4. 改进趋势分析窗口大小")
        print(f"   5. 增强相关性分析精度")
    
    return results

def compare_with_3_algorithms():
    """与3算法系统对比"""
    print(f"\n🔄 与3算法系统性能对比...")
    print("   注意: 这里只是展示6算法系统的优势，实际对比需要相同条件下测试")
    
    print(f"📊 理论优势分析:")
    print(f"   3算法系统: 贝叶斯 + 马尔科夫1阶 + 马尔科夫2阶")
    print(f"   6算法系统: 上述3个 + 频率分析 + 趋势分析 + 相关性分析")
    
    print(f"\n🎯 新增算法特点:")
    print(f"   频率分析: 基于历史出现频率，识别冷热号码")
    print(f"   趋势分析: 基于短期/长期趋势，预测号码走势")
    print(f"   相关性分析: 基于号码共现关系，识别关联模式")
    
    print(f"\n✨ 预期改进:")
    print(f"   1. 算法多样性提升: 从3个增加到6个")
    print(f"   2. 预测角度更全面: 概率+状态+频率+趋势+关联")
    print(f"   3. 权重优化更精细: 6维权重空间")
    print(f"   4. 容错能力更强: 单一算法失效时有更多备选")

if __name__ == "__main__":
    # 运行全面回测
    results = comprehensive_backtest()
    
    # 性能对比分析
    compare_with_3_algorithms()
    
    print(f"\n🎉 6算法集成系统回测完成！")
