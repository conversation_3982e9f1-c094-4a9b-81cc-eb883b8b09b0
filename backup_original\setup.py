"""
大乐透预测系统安装配置
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = "大乐透预测系统 - 基于数据分析和机器学习的彩票预测"

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, "r", encoding="utf-8") as f:
        requirements = [
            line.strip() 
            for line in f.readlines() 
            if line.strip() and not line.startswith("#")
        ]
else:
    requirements = [
        "pandas>=1.5.0",
        "numpy>=1.21.0", 
        "scikit-learn>=1.1.0"
    ]

setup(
    name="lottery-predictor",
    version="2.0.0",
    author="Lottery Predictor Team",
    author_email="<EMAIL>",
    description="大乐透预测系统 - 基于数据分析和机器学习的彩票预测",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/lottery-predictor",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Games/Entertainment",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=3.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=0.971",
        ],
        "docs": [
            "sphinx>=5.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
        "ml": [
            "tensorflow>=2.10.0",
            "torch>=1.12.0",
        ],
        "viz": [
            "matplotlib>=3.5.0",
            "seaborn>=0.11.0",
            "plotly>=5.10.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "lottery-predictor=scripts.main:main",
            "lottery-migrate=scripts.migrate_legacy_code:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.csv", "*.json", "*.yaml", "*.yml"],
        "data": ["raw/*.csv"],
        "config": ["*.py"],
    },
    zip_safe=False,
    keywords="lottery prediction machine-learning data-analysis",
    project_urls={
        "Bug Reports": "https://github.com/your-username/lottery-predictor/issues",
        "Source": "https://github.com/your-username/lottery-predictor",
        "Documentation": "https://lottery-predictor.readthedocs.io/",
    },
)
