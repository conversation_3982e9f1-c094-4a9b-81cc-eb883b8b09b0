"""
贝叶斯号码组合选择器
使用贝叶斯方法对多组预测号码进行评估和排序
"""

import numpy as np
from typing import List, Tuple, Dict
from collections import Counter


class BayesCombinationSelector:
    """贝叶斯号码组合选择器"""
    
    def __init__(self):
        self.is_initialized = False
        self.historical_data = None
        self.feature_weights = {
            'frequency_score': 0.25,      # 频率得分
            'pattern_score': 0.20,       # 模式得分
            'balance_score': 0.20,       # 平衡性得分
            'trend_score': 0.15,         # 趋势得分
            'kill_avoidance_score': 0.20  # 杀号规避得分
        }
        
    def initialize(self, historical_data: List[Tuple[List[int], List[int]]], 
                   kill_numbers: Dict[str, List[List[int]]] = None):
        """
        初始化选择器
        
        Args:
            historical_data: 历史开奖数据 [(红球, 蓝球), ...]
            kill_numbers: 杀号数据
        """
        self.historical_data = historical_data
        self.kill_numbers = kill_numbers or {'red': [[], [], [], [], []], 'blue': [[], []]}
        self.is_initialized = True
        
        # 计算历史统计信息
        self._calculate_historical_stats()
        
    def _calculate_historical_stats(self):
        """计算历史统计信息"""
        if not self.historical_data:
            return
            
        # 红球频率统计
        self.red_frequency = Counter()
        self.blue_frequency = Counter()
        
        # 奇偶比、大小比统计
        self.red_odd_even_patterns = []
        self.red_size_patterns = []
        self.blue_size_patterns = []
        
        for red_balls, blue_balls in self.historical_data:
            # 频率统计
            self.red_frequency.update(red_balls)
            self.blue_frequency.update(blue_balls)
            
            # 模式统计
            red_odd = sum(1 for x in red_balls if x % 2 == 1)
            red_even = len(red_balls) - red_odd
            self.red_odd_even_patterns.append((red_odd, red_even))
            
            red_small = sum(1 for x in red_balls if x <= 18)
            red_big = len(red_balls) - red_small
            self.red_size_patterns.append((red_small, red_big))
            
            blue_small = sum(1 for x in blue_balls if x <= 6)
            blue_big = len(blue_balls) - blue_small
            self.blue_size_patterns.append((blue_small, blue_big))
    
    def evaluate_combinations(self, combinations: List[Tuple[List[int], List[int]]]) -> List[Tuple[int, float, Dict[str, float]]]:
        """
        使用贝叶斯方法评估号码组合
        
        Args:
            combinations: 号码组合列表 [(红球, 蓝球), ...]
            
        Returns:
            List[Tuple[int, float, Dict[str, float]]]: [(组合索引, 总分, 各项得分), ...]
        """
        if not self.is_initialized:
            raise ValueError("选择器尚未初始化")
            
        results = []
        
        for i, (red_balls, blue_balls) in enumerate(combinations):
            # 计算各项得分
            scores = self._calculate_combination_scores(red_balls, blue_balls)
            
            # 贝叶斯加权融合
            total_score = self._bayesian_fusion(scores)
            
            results.append((i, total_score, scores))
        
        # 按总分排序
        results.sort(key=lambda x: x[1], reverse=True)
        
        return results
    
    def _calculate_combination_scores(self, red_balls: List[int], blue_balls: List[int]) -> Dict[str, float]:
        """计算单个组合的各项得分"""
        scores = {}
        
        # 1. 频率得分 - 基于历史出现频率
        scores['frequency_score'] = self._calculate_frequency_score(red_balls, blue_balls)
        
        # 2. 模式得分 - 基于奇偶比、大小比的历史分布
        scores['pattern_score'] = self._calculate_pattern_score(red_balls, blue_balls)
        
        # 3. 平衡性得分 - 号码分布的均匀性
        scores['balance_score'] = self._calculate_balance_score(red_balls, blue_balls)
        
        # 4. 趋势得分 - 与最近期号码的关联性
        scores['trend_score'] = self._calculate_trend_score(red_balls, blue_balls)
        
        # 5. 杀号规避得分 - 避开杀号的程度
        scores['kill_avoidance_score'] = self._calculate_kill_avoidance_score(red_balls, blue_balls)
        
        return scores
    
    def _calculate_frequency_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算频率得分"""
        if not self.historical_data:
            return 0.5
            
        total_periods = len(self.historical_data)
        
        # 红球频率得分
        red_score = 0
        for num in red_balls:
            frequency = self.red_frequency.get(num, 0)
            # 使用对数平滑，避免极端值
            normalized_freq = (frequency + 1) / (total_periods + 35)
            red_score += np.log(normalized_freq + 0.001)
        
        # 蓝球频率得分
        blue_score = 0
        for num in blue_balls:
            frequency = self.blue_frequency.get(num, 0)
            normalized_freq = (frequency + 1) / (total_periods + 12)
            blue_score += np.log(normalized_freq + 0.001)
        
        # 归一化到0-1范围
        combined_score = (red_score + blue_score) / (len(red_balls) + len(blue_balls))
        return max(0, min(1, (combined_score + 6) / 6))  # 调整范围
    
    def _calculate_pattern_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算模式得分"""
        if not self.red_odd_even_patterns:
            return 0.5
            
        # 当前组合的模式
        red_odd = sum(1 for x in red_balls if x % 2 == 1)
        red_even = len(red_balls) - red_odd
        
        red_small = sum(1 for x in red_balls if x <= 18)
        red_big = len(red_balls) - red_small
        
        blue_small = sum(1 for x in blue_balls if x <= 6)
        blue_big = len(blue_balls) - blue_small
        
        # 计算与历史模式的匹配度
        odd_even_matches = sum(1 for pattern in self.red_odd_even_patterns 
                              if pattern == (red_odd, red_even))
        size_matches = sum(1 for pattern in self.red_size_patterns 
                          if pattern == (red_small, red_big))
        blue_size_matches = sum(1 for pattern in self.blue_size_patterns 
                               if pattern == (blue_small, blue_big))
        
        total_periods = len(self.historical_data)
        
        # 计算概率
        odd_even_prob = (odd_even_matches + 1) / (total_periods + 6)  # 平滑
        size_prob = (size_matches + 1) / (total_periods + 6)
        blue_size_prob = (blue_size_matches + 1) / (total_periods + 3)
        
        # 综合得分
        return (odd_even_prob + size_prob + blue_size_prob) / 3
    
    def _calculate_balance_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算平衡性得分"""
        # 红球分布平衡性
        red_ranges = [0, 0, 0, 0, 0]  # 1-7, 8-14, 15-21, 22-28, 29-35
        for num in red_balls:
            range_idx = min(4, (num - 1) // 7)
            red_ranges[range_idx] += 1
        
        # 计算红球分布的标准差（越小越平衡）
        red_std = np.std(red_ranges)
        red_balance = max(0, 1 - red_std / 2.5)  # 归一化
        
        # 蓝球分布平衡性
        blue_ranges = [0, 0]  # 1-6, 7-12
        for num in blue_balls:
            range_idx = 0 if num <= 6 else 1
            blue_ranges[range_idx] += 1
        
        blue_balance = 1 - abs(blue_ranges[0] - blue_ranges[1]) / len(blue_balls)
        
        return (red_balance + blue_balance) / 2
    
    def _calculate_trend_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算趋势得分"""
        if len(self.historical_data) < 5:
            return 0.5
            
        # 最近5期的号码
        recent_red = set()
        recent_blue = set()
        
        for red, blue in self.historical_data[-5:]:
            recent_red.update(red)
            recent_blue.update(blue)
        
        # 计算与最近号码的重叠度
        red_overlap = len(set(red_balls) & recent_red) / len(red_balls)
        blue_overlap = len(set(blue_balls) & recent_blue) / len(blue_balls)
        
        # 适度重叠是好的，完全重叠或完全不重叠都不好
        red_trend = 1 - abs(red_overlap - 0.4)  # 期望40%重叠
        blue_trend = 1 - abs(blue_overlap - 0.5)  # 期望50%重叠
        
        return (red_trend + blue_trend) / 2
    
    def _calculate_kill_avoidance_score(self, red_balls: List[int], blue_balls: List[int]) -> float:
        """计算杀号规避得分"""
        if not self.kill_numbers:
            return 1.0
            
        total_penalty = 0
        total_kills = 0
        
        # 检查红球杀号
        for i, red_kill_list in enumerate(self.kill_numbers.get('red', [])):
            if red_kill_list and i < len(red_balls):
                if red_balls[i] in red_kill_list:
                    total_penalty += 1
                total_kills += len(red_kill_list)
        
        # 检查蓝球杀号
        for i, blue_kill_list in enumerate(self.kill_numbers.get('blue', [])):
            if blue_kill_list and i < len(blue_balls):
                if blue_balls[i] in blue_kill_list:
                    total_penalty += 1
                total_kills += len(blue_kill_list)
        
        if total_kills == 0:
            return 1.0
            
        return max(0, 1 - total_penalty / max(1, total_kills * 0.1))
    
    def _bayesian_fusion(self, scores: Dict[str, float]) -> float:
        """贝叶斯融合各项得分"""
        # 使用对数概率进行贝叶斯融合
        log_likelihood = 0
        
        for feature, score in scores.items():
            weight = self.feature_weights.get(feature, 0.2)
            # 将得分转换为对数似然
            likelihood = max(0.001, min(0.999, score))  # 避免极端值
            log_likelihood += weight * np.log(likelihood / (1 - likelihood))
        
        # 转换回概率
        exp_log = np.exp(log_likelihood)
        probability = exp_log / (1 + exp_log)
        
        return probability
    
    def select_top_combinations(self, combinations: List[Tuple[List[int], List[int]]], 
                               top_k: int = 5) -> List[Dict]:
        """
        选择前K个最优组合
        
        Args:
            combinations: 号码组合列表
            top_k: 返回前K个组合
            
        Returns:
            List[Dict]: 排序后的组合信息
        """
        evaluation_results = self.evaluate_combinations(combinations)
        
        top_combinations = []
        for i, (combo_idx, total_score, scores) in enumerate(evaluation_results[:top_k]):
            red_balls, blue_balls = combinations[combo_idx]
            
            combination_info = {
                'rank': i + 1,
                'original_index': combo_idx + 1,
                'red_balls': red_balls,
                'blue_balls': blue_balls,
                'total_score': total_score,
                'confidence': min(100, total_score * 100),
                'scores': scores,
                'recommendation': self._get_recommendation_level(total_score)
            }
            
            top_combinations.append(combination_info)
        
        return top_combinations
    
    def _get_recommendation_level(self, score: float) -> str:
        """根据得分获取推荐等级"""
        if score >= 0.8:
            return "🌟强烈推荐"
        elif score >= 0.7:
            return "⭐推荐"
        elif score >= 0.6:
            return "✅可选"
        elif score >= 0.5:
            return "⚠️谨慎"
        else:
            return "❌不推荐"
