#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
蓝球算法回测对比
对比原始算法和优化算法的实际效果
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter, defaultdict

class BlueBallBacktestComparison:
    def __init__(self):
        self.data = None
        self.test_periods = 30
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
            
            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"✅ 成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue
            
            print(f"❌ 未找到数据文件")
            return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def run_comparison(self):
        """运行对比测试"""
        print("🔵 蓝球算法回测对比")
        print("=" * 60)
        
        if not self.load_data():
            return
        
        # 测试原始算法
        original_results = self._test_original_algorithm()
        
        # 测试优化算法
        optimized_results = self._test_optimized_algorithm()
        
        # 对比分析
        self._compare_results(original_results, optimized_results)
    
    def _test_original_algorithm(self) -> Dict:
        """测试原始蓝球算法"""
        print(f"\n🧪 测试原始蓝球算法 (杀5个)...")
        
        stats = {
            'total_periods': 0,
            'success_periods': 0,
            'success_rate': 0.0,
            'details': []
        }
        
        for i in range(self.test_periods):
            if i + 6 >= len(self.data):
                break
            
            try:
                # 获取当前期和历史期数据
                current_period = self.data.iloc[i]
                historical_data = self.data.iloc[i+1:i+101]
                
                # 解析当前期号码
                from test_kill_algorithm import parse_numbers
                _, current_blue = parse_numbers(current_period)
                
                # 获取最近几期蓝球数据
                recent_blue_periods = []
                for j in range(1, 7):
                    if i + j < len(self.data):
                        _, blue_balls = parse_numbers(self.data.iloc[i + j])
                        recent_blue_periods.append(blue_balls)
                
                if len(recent_blue_periods) < 2:
                    continue
                
                # 使用原始算法
                from bayesian_markov_killer import BayesianMarkovKiller
                killer = BayesianMarkovKiller(historical_data)
                blue_kills = killer.calculate_blue_kills(recent_blue_periods, target_count=5)
                
                # 检查杀号成功情况
                success = not any(num in current_blue for num in blue_kills)
                wrong_kills = [num for num in blue_kills if num in current_blue]
                
                stats['total_periods'] += 1
                if success:
                    stats['success_periods'] += 1
                
                stats['details'].append({
                    'period': current_period['期号'],
                    'kills': blue_kills,
                    'actual': current_blue,
                    'success': success,
                    'wrong_kills': wrong_kills
                })
                
            except Exception as e:
                continue
        
        if stats['total_periods'] > 0:
            stats['success_rate'] = stats['success_periods'] / stats['total_periods']
        
        print(f"  测试期数: {stats['total_periods']}")
        print(f"  成功期数: {stats['success_periods']}")
        print(f"  成功率: {stats['success_rate']:.1%}")
        
        return stats
    
    def _test_optimized_algorithm(self) -> Dict:
        """测试优化蓝球算法"""
        print(f"\n🚀 测试优化蓝球算法 (杀3个)...")
        
        stats = {
            'total_periods': 0,
            'success_periods': 0,
            'success_rate': 0.0,
            'details': []
        }
        
        for i in range(self.test_periods):
            if i + 6 >= len(self.data):
                break
            
            try:
                # 获取当前期和历史期数据
                current_period = self.data.iloc[i]
                historical_data = self.data.iloc[i+1:i+101]
                
                # 解析当前期号码
                from test_kill_algorithm import parse_numbers
                _, current_blue = parse_numbers(current_period)
                
                # 获取最近几期蓝球数据
                recent_blue_periods = []
                for j in range(1, 7):
                    if i + j < len(self.data):
                        _, blue_balls = parse_numbers(self.data.iloc[i + j])
                        recent_blue_periods.append(blue_balls)
                
                if len(recent_blue_periods) < 2:
                    continue
                
                # 使用优化算法
                from optimized_blue_ball_killer import OptimizedBlueBallKiller
                optimized_killer = OptimizedBlueBallKiller(historical_data)
                blue_kills = optimized_killer.calculate_optimized_blue_kills(recent_blue_periods, target_count=3)
                
                # 检查杀号成功情况
                success = not any(num in current_blue for num in blue_kills)
                wrong_kills = [num for num in blue_kills if num in current_blue]
                
                stats['total_periods'] += 1
                if success:
                    stats['success_periods'] += 1
                
                stats['details'].append({
                    'period': current_period['期号'],
                    'kills': blue_kills,
                    'actual': current_blue,
                    'success': success,
                    'wrong_kills': wrong_kills
                })
                
            except Exception as e:
                continue
        
        if stats['total_periods'] > 0:
            stats['success_rate'] = stats['success_periods'] / stats['total_periods']
        
        print(f"  测试期数: {stats['total_periods']}")
        print(f"  成功期数: {stats['success_periods']}")
        print(f"  成功率: {stats['success_rate']:.1%}")
        
        return stats
    
    def _compare_results(self, original_results: Dict, optimized_results: Dict):
        """对比分析结果"""
        print(f"\n📊 对比分析结果")
        print("=" * 60)
        
        # 基本对比
        print(f"🔍 基本性能对比:")
        print(f"  原始算法 (杀5个): {original_results['success_rate']:.1%} 成功率")
        print(f"  优化算法 (杀3个): {optimized_results['success_rate']:.1%} 成功率")
        
        improvement = optimized_results['success_rate'] - original_results['success_rate']
        print(f"  性能改进: {improvement:+.1%}")
        
        if improvement > 0:
            print(f"  ✅ 优化算法表现更好")
        elif improvement == 0:
            print(f"  ⚖️  两种算法表现相当")
        else:
            print(f"  ⚠️  优化算法需要进一步改进")
        
        # 详细分析
        print(f"\n🔬 详细分析:")
        
        # 分析失败案例
        original_failures = [d for d in original_results['details'] if not d['success']]
        optimized_failures = [d for d in optimized_results['details'] if not d['success']]
        
        print(f"  原始算法失败期数: {len(original_failures)}")
        print(f"  优化算法失败期数: {len(optimized_failures)}")
        
        # 分析误杀号码
        if original_failures:
            original_wrong = []
            for failure in original_failures:
                original_wrong.extend(failure['wrong_kills'])
            original_wrong_counter = Counter(original_wrong)
            print(f"  原始算法常误杀: {dict(original_wrong_counter.most_common(3))}")
        
        if optimized_failures:
            optimized_wrong = []
            for failure in optimized_failures:
                optimized_wrong.extend(failure['wrong_kills'])
            optimized_wrong_counter = Counter(optimized_wrong)
            print(f"  优化算法常误杀: {dict(optimized_wrong_counter.most_common(3))}")
        
        # 杀号效率分析
        print(f"\n📈 杀号效率分析:")
        original_kill_count = 5 * original_results['total_periods']
        optimized_kill_count = 3 * optimized_results['total_periods']
        
        original_wrong_count = sum(len(d['wrong_kills']) for d in original_results['details'])
        optimized_wrong_count = sum(len(d['wrong_kills']) for d in optimized_results['details'])
        
        original_efficiency = (original_kill_count - original_wrong_count) / original_kill_count if original_kill_count > 0 else 0
        optimized_efficiency = (optimized_kill_count - optimized_wrong_count) / optimized_kill_count if optimized_kill_count > 0 else 0
        
        print(f"  原始算法杀号效率: {original_efficiency:.1%} ({original_kill_count - original_wrong_count}/{original_kill_count})")
        print(f"  优化算法杀号效率: {optimized_efficiency:.1%} ({optimized_kill_count - optimized_wrong_count}/{optimized_kill_count})")
        
        # 推荐结论
        print(f"\n💡 推荐结论:")
        if optimized_results['success_rate'] >= 0.8:
            print(f"  🎯 优化算法达到80%目标，推荐使用")
        elif optimized_results['success_rate'] > original_results['success_rate']:
            print(f"  ✅ 优化算法表现更好，推荐使用")
        else:
            print(f"  ⚠️  需要进一步优化算法")
        
        # 具体改进建议
        if optimized_results['success_rate'] < 0.8:
            print(f"\n🔧 进一步改进建议:")
            if optimized_failures:
                # 分析失败模式
                failure_patterns = []
                for failure in optimized_failures:
                    if any(num in [1, 2, 3, 4] for num in failure['wrong_kills']):
                        failure_patterns.append("误杀小号")
                    if any(num in [9, 10, 11, 12] for num in failure['wrong_kills']):
                        failure_patterns.append("误杀大号")
                
                pattern_counter = Counter(failure_patterns)
                for pattern, count in pattern_counter.most_common(2):
                    print(f"  • 减少{pattern} (出现{count}次)")
            
            print(f"  • 考虑进一步减少杀号数量到2个")
            print(f"  • 加强近期趋势分析权重")
            print(f"  • 优化位置分析算法")

def main():
    """主函数"""
    comparison = BlueBallBacktestComparison()
    comparison.run_comparison()

if __name__ == "__main__":
    main()
