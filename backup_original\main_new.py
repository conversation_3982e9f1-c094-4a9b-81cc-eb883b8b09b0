#!/usr/bin/env python3
"""
兼容的主程序入口
保持与原有main.py的兼容性
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / 'src'
if src_path.exists():
    sys.path.insert(0, str(src_path))

try:
    # 尝试使用新的系统
    from systems.basic_system import LotteryPredictor
    print("✅ 使用新架构运行")
except ImportError:
    # 回退到原有的main.py
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "src/systems/basic_system.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        LotteryPredictor = main_module.LotteryPredictor
        print("✅ 使用迁移后的系统运行")
    except:
        print("❌ 无法加载预测系统")
        sys.exit(1)

def main():
    """主函数"""
    try:
        predictor = LotteryPredictor()
        predictor.run_backtest(num_periods=50, display_periods=10)
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
