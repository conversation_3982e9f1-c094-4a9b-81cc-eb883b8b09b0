#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化红球杀号参数，找到高胜率的马尔可夫+贝叶斯组合参数
"""

import sys
import os
from pathlib import Path
import itertools

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.utils import load_data, parse_numbers
from advanced_red_kill_algorithm import AdvancedRedKillAlgorithm


class RedKillParamOptimizer:
    """红球杀号参数优化器"""
    
    def __init__(self, data):
        self.data = data
        self.test_periods = 50  # 增加测试期数以获得更可靠的结果
        
    def test_params(self, bayesian_weight, markov_weight, bayesian_history, markov_history, 
                   similarity_threshold, target_count):
        """测试特定参数组合的效果"""
        try:
            # 创建算法实例
            killer = AdvancedRedKillAlgorithm(self.data)
            
            # 设置参数
            killer.strategies = {
                'bayesian': bayesian_weight,
                'markov': markov_weight
            }
            killer.bayesian_history_periods = bayesian_history
            killer.markov_history_periods = markov_history
            killer.similarity_threshold = similarity_threshold
            
            # 回测
            success_count = 0
            total_count = 0
            
            for i in range(self.test_periods):
                try:
                    # 构建period_data
                    train_data = self.data.iloc[i + 1:i + 201]
                    if len(train_data) < 10:
                        continue
                    
                    period_data = {
                        'current': train_data.iloc[0],
                        'last': train_data.iloc[1],
                        'prev2': train_data.iloc[2],
                        'prev3': train_data.iloc[3],
                        'prev4': train_data.iloc[4]
                    }
                    
                    # 获取实际结果
                    actual_row = self.data.iloc[i]
                    actual_red, _ = parse_numbers(actual_row)
                    
                    # 更新算法数据
                    killer.data = train_data
                    
                    # 预测杀号（静默模式）
                    kills = killer.predict_red_kills_silent(period_data, target_count=target_count)
                    success = not any(k in actual_red for k in kills)
                    
                    if success:
                        success_count += 1
                    total_count += 1
                    
                except Exception as e:
                    continue
            
            success_rate = success_count / total_count if total_count > 0 else 0
            return success_rate, success_count, total_count
            
        except Exception as e:
            return 0, 0, 0
    
    def optimize_parameters(self):
        """优化参数"""
        print("🔍 开始红球杀号参数优化...")
        print("=" * 60)
        
        # 扩大参数搜索空间
        bayesian_weights = [0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
        bayesian_histories = [60, 80, 100, 120, 150]
        markov_histories = [40, 60, 80, 100, 120]
        similarity_thresholds = [1, 2, 3, 4, 5]
        target_counts = [4, 5, 6, 7, 8]
        
        best_params = None
        best_score = 0
        best_details = None
        
        total_combinations = (len(bayesian_weights) * len(bayesian_histories) * 
                            len(markov_histories) * len(similarity_thresholds) * len(target_counts))
        current_combination = 0
        
        print(f"📊 总共需要测试 {total_combinations} 种参数组合...")
        print()
        
        for bayesian_weight in bayesian_weights:
            markov_weight = 1.0 - bayesian_weight
            
            for bayesian_history in bayesian_histories:
                for markov_history in markov_histories:
                    for similarity_threshold in similarity_thresholds:
                        for target_count in target_counts:
                            current_combination += 1
                            
                            print(f"🔧 测试组合 {current_combination}/{total_combinations}:")
                            print(f"  贝叶斯权重: {bayesian_weight:.1f}, 马尔可夫权重: {markov_weight:.1f}")
                            print(f"  贝叶斯历史: {bayesian_history}期, 马尔可夫历史: {markov_history}期")
                            print(f"  相似度阈值: {similarity_threshold}, 杀号数量: {target_count}")
                            
                            success_rate, success_count, total_count = self.test_params(
                                bayesian_weight, markov_weight, bayesian_history, 
                                markov_history, similarity_threshold, target_count
                            )
                            
                            print(f"  结果: {success_rate:.1%} ({success_count}/{total_count})")
                            
                            if success_rate > best_score:
                                best_score = success_rate
                                best_params = {
                                    'bayesian_weight': bayesian_weight,
                                    'markov_weight': markov_weight,
                                    'bayesian_history': bayesian_history,
                                    'markov_history': markov_history,
                                    'similarity_threshold': similarity_threshold,
                                    'target_count': target_count
                                }
                                best_details = (success_count, total_count)
                                print(f"  🎉 新的最佳参数！成功率: {success_rate:.1%}")
                            
                            print()
        
        # 输出最佳结果
        print("=" * 60)
        print("🏆 红球杀号参数优化完成！")
        print("=" * 60)
        
        if best_params:
            print(f"🎯 最佳参数组合:")
            print(f"  贝叶斯权重: {best_params['bayesian_weight']:.1f}")
            print(f"  马尔可夫权重: {best_params['markov_weight']:.1f}")
            print(f"  贝叶斯历史期数: {best_params['bayesian_history']}")
            print(f"  马尔可夫历史期数: {best_params['markov_history']}")
            print(f"  相似度阈值: {best_params['similarity_threshold']}")
            print(f"  杀号数量: {best_params['target_count']}")
            print()
            print(f"📊 最佳成功率: {best_score:.1%} ({best_details[0]}/{best_details[1]})")
            
            # 验证最佳参数
            print()
            print("🔍 验证最佳参数...")
            self.validate_best_params(best_params)
        else:
            print("❌ 未找到有效的参数组合")
        
        return best_params
    
    def validate_best_params(self, best_params):
        """验证最佳参数"""
        print("🧪 使用最佳参数进行详细验证...")
        
        # 创建优化后的算法
        killer = AdvancedRedKillAlgorithm(self.data)
        killer.strategies = {
            'bayesian': best_params['bayesian_weight'],
            'markov': best_params['markov_weight']
        }
        killer.bayesian_history_periods = best_params['bayesian_history']
        killer.markov_history_periods = best_params['markov_history']
        killer.similarity_threshold = best_params['similarity_threshold']
        
        # 详细测试
        results = []
        for i in range(10):  # 测试10期
            try:
                train_data = self.data.iloc[i + 1:i + 201]
                if len(train_data) < 10:
                    continue
                
                period_data = {
                    'current': train_data.iloc[0],
                    'last': train_data.iloc[1],
                    'prev2': train_data.iloc[2],
                    'prev3': train_data.iloc[3],
                    'prev4': train_data.iloc[4]
                }
                
                actual_row = self.data.iloc[i]
                actual_red, _ = parse_numbers(actual_row)
                
                killer.data = train_data
                kills = killer.predict_red_kills(period_data, target_count=best_params['target_count'])
                success = not any(k in actual_red for k in kills)
                
                results.append(success)
                period_num = actual_row['期号']
                print(f"  期号 {period_num}: 实际{sorted(actual_red)}, 杀号{sorted(kills)} {'✅' if success else '❌'}")
                
            except Exception as e:
                continue
        
        if results:
            final_rate = sum(results) / len(results)
            print(f"📊 验证结果: {final_rate:.1%} ({sum(results)}/{len(results)})")
        
        return best_params


# 为AdvancedRedKillAlgorithm添加静默预测方法
def predict_red_kills_silent(self, period_data, target_count=6):
    """静默版本的红球杀号预测（不打印调试信息）"""
    try:
        # 获取贝叶斯概率分布
        bayesian_probs = self._get_bayesian_probabilities(period_data)
        
        # 获取马尔可夫预测
        markov_predictions = self._get_markov_predictions(period_data)
        
        # 组合策略
        combined_scores = self._combine_strategies(bayesian_probs, markov_predictions)
        
        # 安全过滤
        safe_scores = self._apply_safety_filter(combined_scores, period_data)
        
        # 选择最低得分的号码
        if safe_scores:
            sorted_candidates = sorted(safe_scores.items(), key=lambda x: x[1])
            return [num for num, score in sorted_candidates[:target_count]]
        else:
            return [32, 33, 34, 35, 31, 30][:target_count]
    except:
        return [32, 33, 34, 35, 31, 30][:target_count]

# 动态添加方法
AdvancedRedKillAlgorithm.predict_red_kills_silent = predict_red_kills_silent


def quick_test():
    """快速测试更多关键参数组合"""
    print("🚀 红球杀号快速参数测试")
    print("=" * 60)

    # 加载数据
    data = load_data('dlt_data.csv')
    print(f"✅ 加载数据: {len(data)} 期")

    # 创建优化器
    optimizer = RedKillParamOptimizer(data)

    # 测试更多关键参数组合
    test_configs = [
        # 原始配置
        {'bayesian_weight': 0.5, 'bayesian_history': 80, 'markov_history': 60, 'similarity_threshold': 2, 'target_count': 5},

        # 增加贝叶斯权重
        {'bayesian_weight': 0.7, 'bayesian_history': 100, 'markov_history': 80, 'similarity_threshold': 3, 'target_count': 5},
        {'bayesian_weight': 0.8, 'bayesian_history': 120, 'markov_history': 60, 'similarity_threshold': 2, 'target_count': 5},

        # 增加马尔可夫权重
        {'bayesian_weight': 0.3, 'bayesian_history': 80, 'markov_history': 100, 'similarity_threshold': 3, 'target_count': 5},
        {'bayesian_weight': 0.4, 'bayesian_history': 60, 'markov_history': 120, 'similarity_threshold': 4, 'target_count': 5},

        # 调整历史期数
        {'bayesian_weight': 0.6, 'bayesian_history': 150, 'markov_history': 100, 'similarity_threshold': 3, 'target_count': 5},
        {'bayesian_weight': 0.6, 'bayesian_history': 60, 'markov_history': 40, 'similarity_threshold': 1, 'target_count': 5},

        # 调整杀号数量
        {'bayesian_weight': 0.6, 'bayesian_history': 100, 'markov_history': 80, 'similarity_threshold': 3, 'target_count': 4},
        {'bayesian_weight': 0.6, 'bayesian_history': 100, 'markov_history': 80, 'similarity_threshold': 3, 'target_count': 6},
        {'bayesian_weight': 0.6, 'bayesian_history': 100, 'markov_history': 80, 'similarity_threshold': 3, 'target_count': 8},
    ]
    
    best_config = None
    best_rate = 0
    
    for i, config in enumerate(test_configs):
        print(f"\n🔧 测试配置 {i+1}:")
        print(f"  参数: {config}")
        
        success_rate, success_count, total_count = optimizer.test_params(
            config['bayesian_weight'], 1.0 - config['bayesian_weight'],
            config['bayesian_history'], config['markov_history'],
            config['similarity_threshold'], config['target_count']
        )
        
        print(f"  结果: {success_rate:.1%} ({success_count}/{total_count})")
        
        if success_rate > best_rate:
            best_rate = success_rate
            best_config = config
    
    print(f"\n🏆 快速测试最佳配置:")
    print(f"  参数: {best_config}")
    print(f"  成功率: {best_rate:.1%}")
    
    return best_config


def main():
    """主函数"""
    print("🔴 红球杀号参数优化器")
    print("=" * 60)
    
    # 先进行快速测试
    quick_config = quick_test()
    
    print("\n" + "=" * 60)
    choice = input("是否进行完整参数优化？(y/n): ")
    
    if choice.lower() == 'y':
        # 加载数据
        data = load_data('dlt_data.csv')
        
        # 创建优化器
        optimizer = RedKillParamOptimizer(data)
        
        # 开始优化
        optimizer.optimize_parameters()
    else:
        print("使用快速测试结果作为推荐配置。")


if __name__ == "__main__":
    main()
