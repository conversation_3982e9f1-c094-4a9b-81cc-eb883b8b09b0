#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期号杀号预测器 - 直接通过期号调用advanced_probabilistic_system.py
"""

import sys
import os
sys.path.append('src/systems')

from src.systems.main import LotteryPredictor

class PeriodKillPredictor:
    """期号杀号预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.system = LotteryPredictor()
        print("✅ 期号杀号预测器初始化完成")
    
    def predict_by_period(self, period_number: str):
        """
        根据期号预测杀号
        
        Args:
            period_number: 期号（如"25069"）
            
        Returns:
            Dict: 杀号结果
        """
        print(f"\n🎯 开始预测期号 {period_number} 的杀号...")
        print("=" * 50)
        
        try:
            result = self.system.predict_kill_numbers_by_period(period_number)
            
            print(f"\n📊 预测结果:")
            print(f"  红球杀号: {result['red_universal']} (共{len(result['red_universal'])}个)")
            print(f"  蓝球杀号: {result['blue_universal']} (共{len(result['blue_universal'])}个)")
            
            return result
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return None

def main():
    """主函数 - 演示使用方法"""
    print("🎯 期号杀号预测器")
    print("=" * 60)
    
    # 初始化预测器
    predictor = PeriodKillPredictor()
    
    # 示例：预测指定期号的杀号
    test_period = "25068"  # 可以修改为任意期号
    
    result = predictor.predict_by_period(test_period)
    
    if result:
        print(f"\n🎉 期号 {test_period} 杀号预测完成！")
        print("\n💡 使用说明:")
        print("  1. 修改 test_period 变量为你想预测的期号")
        print("  2. 运行脚本即可获得该期号的杀号预测")
        print("  3. 红球杀号：避免选择这些号码")
        print("  4. 蓝球杀号：避免选择这些号码")
    else:
        print(f"\n❌ 期号 {test_period} 杀号预测失败")

if __name__ == "__main__":
    main()
