#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
算法组合测试系统
测试不同10算法组合的成功率
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
import itertools
from collections import defaultdict

# 导入现有的杀号算法
from test_kill_algorithm import KillAlgorithmTester, parse_numbers

class AlgorithmCombinationTester:
    def __init__(self):
        self.data = None
        self.base_tester = KillAlgorithmTester()
        
        # 定义所有可用算法，按类别分组
        self.algorithm_categories = {
            'traditional': [
                'pentagonal_kill',      # 97.6% - 五边形数杀号法
                'abundant_kill',        # 95.8% - 过剩数杀号法
                'factorial_kill',       # 95.0% - 阶乘杀号法
                'catalan_kill',         # 91.3% - 卡塔兰数杀号法
                'span',                 # 91.1% - 跨度计算法
                'odd_even',             # 90.9% - 奇偶比例法
                'interval_kill',        # 90.5% - 区间杀号法
                'symmetry_kill',        # 90.0% - 对称杀号法
                'modular_kill',         # 90.0% - 模运算杀号法
                'prime_kill'            # 89.5% - 质数杀号法
            ],
            'last_period': [
                'last_period_plus',     # 91.0% - 上期+1杀号法
                'last_period_reverse',  # 90.2% - 上期反向杀号法
                'last_period_diff_kill', # 85.8% - 上期差值杀号法
                'last_period_minus',    # 85.6% - 上期-1杀号法
                'last_period_pattern_kill', # 86.4% - 上期模式杀号法
                'last_period_avg_kill', # 85.0% - 上期平均值杀号法
                'last_period_median_kill', # 86.1% - 上期中位数杀号法
                'last_period_sum_kill', # 84.8% - 上期和值杀号法
                'last_period_double',   # 83.3% - 上期×2杀号法
                'last_period_half'      # 82.6% - 上期÷2杀号法
            ],
            'prev2_period': [
                'prev2_period_pattern_kill', # 91.7% - 上上期模式杀号法
                'prev2_period_half',    # 89.1% - 上上期÷2杀号法
                'prev2_period_reverse', # 88.2% - 上上期反向杀号法
                'prev2_period_diff_kill', # 88.2% - 上上期差值杀号法
                'prev2_period_plus',    # 87.9% - 上上期+1杀号法
                'prev2_period_median_kill', # 86.9% - 上上期中位数杀号法
                'prev2_period_minus',   # 86.5% - 上上期-1杀号法
                'prev2_period_avg_kill', # 86.5% - 上上期平均值杀号法
                'prev2_period_sum_kill', # 86.5% - 上上期和值杀号法
                'prev2_period_double'   # 82.0% - 上上期×2杀号法
            ],
            'other_dimensions': [
                'energy_kill',          # 92.5% - 能量维度杀号法
                'direction_kill',       # 90.5% - 方位维度杀号法
                'psychology_kill',      # 90.5% - 心理学维度杀号法
                'month_kill',           # 89.1% - 月份维度杀号法
                'zodiac_kill',          # 89.1% - 生肖维度杀号法
                'weather_kill',         # 88.7% - 天气维度杀号法
                'season_kill',          # 88.2% - 季节维度杀号法
                'lunar_kill',           # 88.0% - 农历维度杀号法
                'weekday_kill',         # 87.9% - 星期维度杀号法
                'element_kill'          # 86.5% - 五行维度杀号法
            ]
        }
        
        # 预定义的组合策略
        self.predefined_combinations = {
            'top_traditional': [
                'pentagonal_kill', 'abundant_kill', 'factorial_kill', 'catalan_kill', 
                'span', 'odd_even', 'interval_kill', 'symmetry_kill', 'modular_kill', 'prime_kill'
            ],
            'top_last_period': [
                'last_period_plus', 'last_period_reverse', 'last_period_pattern_kill',
                'last_period_diff_kill', 'last_period_minus', 'last_period_median_kill',
                'last_period_avg_kill', 'last_period_sum_kill', 'last_period_double', 'last_period_half'
            ],
            'top_prev2_period': [
                'prev2_period_pattern_kill', 'prev2_period_half', 'prev2_period_reverse',
                'prev2_period_diff_kill', 'prev2_period_plus', 'prev2_period_median_kill',
                'prev2_period_minus', 'prev2_period_avg_kill', 'prev2_period_sum_kill', 'prev2_period_double'
            ],
            'top_other_dimensions': [
                'energy_kill', 'direction_kill', 'psychology_kill', 'month_kill', 'zodiac_kill',
                'weather_kill', 'season_kill', 'lunar_kill', 'weekday_kill', 'element_kill'
            ],
            'mixed_best': [
                'pentagonal_kill', 'factorial_kill', 'energy_kill', 'last_period_plus',
                'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'catalan_kill',
                'span', 'abundant_kill'
            ],
            'mathematical_focus': [
                'pentagonal_kill', 'abundant_kill', 'factorial_kill', 'catalan_kill',
                'prime_kill', 'modular_kill', 'symmetry_kill', 'interval_kill',
                'span', 'odd_even'
            ],
            'time_dimension_focus': [
                'last_period_plus', 'last_period_reverse', 'last_period_pattern_kill',
                'prev2_period_pattern_kill', 'prev2_period_half', 'prev2_period_reverse',
                'last_period_diff_kill', 'prev2_period_diff_kill', 'last_period_minus', 'prev2_period_plus'
            ],
            'cultural_dimension_focus': [
                'zodiac_kill', 'element_kill', 'lunar_kill', 'season_kill', 'month_kill',
                'weekday_kill', 'direction_kill', 'weather_kill', 'psychology_kill', 'energy_kill'
            ],
            'balanced_mix': [
                'pentagonal_kill', 'last_period_plus', 'prev2_period_pattern_kill', 'energy_kill',
                'factorial_kill', 'direction_kill', 'catalan_kill', 'psychology_kill',
                'span', 'zodiac_kill'
            ],
            'precision_focused': [
                'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
                'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
                'energy_kill', 'abundant_kill'
            ]
        }

    def load_data(self) -> bool:
        """加载数据"""
        if self.base_tester.load_data():
            self.data = self.base_tester.data
            return True
        return False

    def test_combination(self, algorithms: List[str], test_periods: int = 50) -> Dict:
        """测试单个算法组合"""
        results = {
            'algorithms': algorithms,
            'total_periods': 0,
            'successful_periods': 0,
            'success_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0,
            'details': []
        }
        
        # 逐期测试
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期
            
            # 解析号码
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)
            
            # 收集所有算法的杀号
            all_kills = set()
            algorithm_kills = {}
            
            for algo in algorithms:
                kills = self._get_algorithm_kills(algo, period1_red, period2_red, current_period)
                if kills:
                    # 只取第一个杀号
                    single_kill = kills[0]
                    if 1 <= single_kill <= 35 and single_kill not in (period1_red + period2_red):
                        all_kills.add(single_kill)
                        algorithm_kills[algo] = single_kill
            
            # 统计本期结果
            period_successful_kills = 0
            period_total_kills = len(all_kills)
            
            for kill in all_kills:
                if kill not in current_red:
                    period_successful_kills += 1
            
            # 判断本期是否成功（要求所有杀号都成功）
            period_success = (period_total_kills > 0 and period_successful_kills == period_total_kills)
            
            period_detail = {
                'period': current_period['期号'],
                'actual_red': current_red,
                'kills': list(all_kills),
                'successful_kills': period_successful_kills,
                'total_kills': period_total_kills,
                'period_success': period_success,
                'algorithm_kills': algorithm_kills
            }
            
            results['details'].append(period_detail)
            results['total_periods'] += 1
            results['total_kills'] += period_total_kills
            results['successful_kills'] += period_successful_kills
            
            if period_success:
                results['successful_periods'] += 1
        
        # 计算成功率
        if results['total_periods'] > 0:
            results['success_rate'] = results['successful_periods'] / results['total_periods']
        
        if results['total_kills'] > 0:
            results['kill_success_rate'] = results['successful_kills'] / results['total_kills']
        
        return results

    def _get_algorithm_kills(self, algo: str, period1_red: List[int], period2_red: List[int], current_period) -> List[int]:
        """获取指定算法的杀号"""
        return self.base_tester._get_single_kill_number(algo, period1_red, period2_red, current_period)

    def test_all_combinations(self, test_periods: int = 50) -> Dict:
        """测试所有预定义组合"""
        print(f"\n🎯 测试所有算法组合 (最近{test_periods}期)")
        print("要求：每个组合包含10个算法，每个算法每期只杀1个号码")
        print("=" * 80)
        
        combination_results = {}
        
        for combo_name, algorithms in self.predefined_combinations.items():
            print(f"\n正在测试组合: {combo_name}")
            result = self.test_combination(algorithms, test_periods)
            combination_results[combo_name] = result
        
        return combination_results

    def print_combination_results(self, results: Dict):
        """打印组合测试结果"""
        print(f"\n📊 算法组合测试结果")
        print("=" * 80)
        
        # 按成功率排序
        sorted_results = sorted(results.items(), key=lambda x: x[1]['success_rate'], reverse=True)
        
        print("🏆 组合成功率排行 (按期成功率排序):")
        for i, (combo_name, result) in enumerate(sorted_results, 1):
            status = "🎯" if result['success_rate'] >= 0.9 else "⚠️" if result['success_rate'] >= 0.8 else "❌"
            print(f"  {i}. {combo_name:25} 期成功率:{result['success_rate']:6.1%} "
                  f"杀号成功率:{result['kill_success_rate']:6.1%} "
                  f"({result['successful_periods']}/{result['total_periods']}) {status}")
        
        # 详细分析前5名
        print(f"\n📋 前5名详细分析:")
        for i, (combo_name, result) in enumerate(sorted_results[:5], 1):
            print(f"\n{i}. {combo_name} 组合:")
            print(f"   算法列表: {', '.join(result['algorithms'])}")
            print(f"   期成功率: {result['success_rate']:.1%} ({result['successful_periods']}/{result['total_periods']})")
            print(f"   杀号成功率: {result['kill_success_rate']:.1%} ({result['successful_kills']}/{result['total_kills']})")
            print(f"   平均每期杀号数: {result['total_kills']/result['total_periods']:.1f}")
            
            # 显示最近3期详情
            print("   最近3期详情:")
            for detail in result['details'][:3]:
                kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
                actual_str = ','.join(map(str, detail['actual_red']))
                status = "✅" if detail['period_success'] else "❌"
                print(f"     {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                      f"成功{detail['successful_kills']}/{detail['total_kills']} {status}")

def main():
    """主函数"""
    print("🎯 算法组合测试系统")
    print("=" * 60)
    
    # 初始化测试器
    tester = AlgorithmCombinationTester()
    
    # 加载数据
    if not tester.load_data():
        return
    
    # 测试所有组合
    results = tester.test_all_combinations(test_periods=50)
    tester.print_combination_results(results)
    
    print(f"\n🎉 算法组合测试完成！")

if __name__ == "__main__":
    main()
