#!/usr/bin/env python3
"""
大乐透预测系统主程序
使用新的项目结构
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    try:
        # 导入基础系统
        from systems.main import LotteryPredictor
        
        print("🎯 大乐透预测系统")
        print("=" * 50)
        
        # 创建预测器并运行
        predictor = LotteryPredictor()
        print("预测器创建成功，开始回测...")
        predictor.run_backtest(num_periods=5, display_periods=5)  # 先测试5期
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 提示: 请确保已安装必要的依赖包")
        print("   pip install pandas numpy scikit-learn")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
