"""
自适应学习系统
根据最新开奖结果动态调整模型
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from collections import deque, defaultdict, Counter
import json
import os
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import parse_numbers, load_data


class AdaptiveLearningSystem:
    """自适应学习系统"""
    
    def __init__(self, window_size: int = 50, adaptation_rate: float = 0.1):
        """
        初始化自适应学习系统
        
        Args:
            window_size: 滑动窗口大小
            adaptation_rate: 适应率
        """
        self.window_size = window_size
        self.adaptation_rate = adaptation_rate
        
        # 滑动窗口数据
        self.recent_data = deque(maxlen=window_size)
        
        # 性能监控
        self.performance_history = {
            'red_odd_even': deque(maxlen=100),
            'red_size': deque(maxlen=100),
            'blue_size': deque(maxlen=100),
            'number_accuracy': deque(maxlen=100)
        }
        
        # 动态权重
        self.feature_weights = {
            'frequency': 0.3,
            'trend': 0.25,
            'pattern': 0.25,
            'neural': 0.2
        }
        
        # 模式检测
        self.pattern_detector = PatternDetector()
        
        # 趋势分析器
        self.trend_analyzer = TrendAnalyzer()
        
        # 学习历史
        self.learning_log = []
        
    def update_with_new_result(self, period_num: int, red_balls: List[int], 
                             blue_balls: List[int], predictions: Dict = None):
        """
        使用新的开奖结果更新系统
        
        Args:
            period_num: 期号
            red_balls: 红球号码
            blue_balls: 蓝球号码
            predictions: 之前的预测结果
        """
        # 添加新数据到滑动窗口
        new_data = {
            'period': period_num,
            'red_balls': red_balls,
            'blue_balls': blue_balls,
            'timestamp': datetime.now()
        }
        self.recent_data.append(new_data)
        
        # 如果有预测结果，评估性能
        if predictions:
            self._evaluate_predictions(red_balls, blue_balls, predictions)
        
        # 检测新模式
        self.pattern_detector.update(red_balls, blue_balls)
        
        # 更新趋势分析
        self.trend_analyzer.update(red_balls, blue_balls)
        
        # 动态调整权重
        self._adapt_weights()
        
        # 记录学习过程
        self._log_learning_event(period_num, red_balls, blue_balls, predictions)
        
        print(f"系统已更新，当前窗口大小: {len(self.recent_data)}")
    
    def _evaluate_predictions(self, actual_red: List[int], actual_blue: List[int], 
                            predictions: Dict):
        """评估预测性能"""
        # 评估状态预测
        if 'red_odd_even' in predictions:
            pred_state = predictions['red_odd_even']
            actual_odd = sum(1 for x in actual_red if x % 2 == 1)
            actual_even = 5 - actual_odd
            actual_state = f"{actual_odd}:{actual_even}"
            
            accuracy = 1.0 if pred_state == actual_state else 0.0
            self.performance_history['red_odd_even'].append(accuracy)
        
        if 'red_size' in predictions:
            pred_state = predictions['red_size']
            actual_small = sum(1 for x in actual_red if x <= 18)
            actual_big = 5 - actual_small
            actual_state = f"{actual_small}:{actual_big}"
            
            accuracy = 1.0 if pred_state == actual_state else 0.0
            self.performance_history['red_size'].append(accuracy)
        
        if 'blue_size' in predictions:
            pred_state = predictions['blue_size']
            actual_small = sum(1 for x in actual_blue if x <= 6)
            actual_big = 2 - actual_small
            actual_state = f"{actual_small}:{actual_big}"
            
            accuracy = 1.0 if pred_state == actual_state else 0.0
            self.performance_history['blue_size'].append(accuracy)
        
        # 评估号码预测
        if 'predicted_numbers' in predictions:
            pred_red, pred_blue = predictions['predicted_numbers']
            red_hits = len(set(pred_red) & set(actual_red))
            blue_hits = len(set(pred_blue) & set(actual_blue))
            
            # 计算命中率
            total_accuracy = (red_hits + blue_hits) / 7.0
            self.performance_history['number_accuracy'].append(total_accuracy)
    
    def _adapt_weights(self):
        """动态调整特征权重"""
        # 基于最近性能调整权重
        recent_performance = {}
        
        for metric, history in self.performance_history.items():
            if len(history) > 0:
                recent_performance[metric] = np.mean(list(history)[-10:])  # 最近10次的平均性能
        
        # 根据性能调整权重
        if len(recent_performance) > 0:
            avg_performance = np.mean(list(recent_performance.values()))
            
            # 如果整体性能下降，增加神经网络权重
            if avg_performance < 0.3:
                self.feature_weights['neural'] = min(0.4, self.feature_weights['neural'] + 0.05)
                self.feature_weights['frequency'] = max(0.2, self.feature_weights['frequency'] - 0.02)
            
            # 如果整体性能提升，保持当前策略
            elif avg_performance > 0.5:
                # 微调以保持性能
                pass
            
            # 归一化权重
            total_weight = sum(self.feature_weights.values())
            for key in self.feature_weights:
                self.feature_weights[key] /= total_weight
    
    def get_adaptive_predictions(self, base_predictions: Dict) -> Dict:
        """
        获取自适应预测结果
        
        Args:
            base_predictions: 基础预测结果
            
        Returns:
            Dict: 调整后的预测结果
        """
        if len(self.recent_data) < 5:
            return base_predictions
        
        adaptive_predictions = base_predictions.copy()
        
        # 基于最近趋势调整
        trend_adjustments = self.trend_analyzer.get_trend_adjustments()
        
        # 基于模式检测调整
        pattern_adjustments = self.pattern_detector.get_pattern_adjustments()
        
        # 应用调整
        for key, adjustment in trend_adjustments.items():
            if key in adaptive_predictions:
                adaptive_predictions[key] = self._apply_adjustment(
                    adaptive_predictions[key], adjustment, 'trend'
                )
        
        for key, adjustment in pattern_adjustments.items():
            if key in adaptive_predictions:
                adaptive_predictions[key] = self._apply_adjustment(
                    adaptive_predictions[key], adjustment, 'pattern'
                )
        
        return adaptive_predictions
    
    def _apply_adjustment(self, original_pred, adjustment, adjustment_type: str):
        """应用调整"""
        if adjustment_type == 'trend':
            # 趋势调整：如果检测到强趋势，增加置信度
            if isinstance(original_pred, tuple) and len(original_pred) == 2:
                pred_value, confidence = original_pred
                adjusted_confidence = confidence * (1 + adjustment * 0.1)
                return (pred_value, min(1.0, adjusted_confidence))
        
        elif adjustment_type == 'pattern':
            # 模式调整：如果检测到已知模式，调整预测
            if isinstance(adjustment, dict) and 'suggested_state' in adjustment:
                return adjustment['suggested_state']
        
        return original_pred
    
    def get_current_weights(self) -> Dict[str, float]:
        """获取当前特征权重"""
        return self.feature_weights.copy()
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        summary = {}
        
        for metric, history in self.performance_history.items():
            if len(history) > 0:
                summary[metric] = {
                    'recent_avg': np.mean(list(history)[-10:]) if len(history) >= 10 else np.mean(list(history)),
                    'overall_avg': np.mean(list(history)),
                    'trend': 'improving' if len(history) >= 5 and 
                            np.mean(list(history)[-5:]) > np.mean(list(history)[:-5]) else 'stable'
                }
        
        return summary
    
    def _log_learning_event(self, period_num: int, red_balls: List[int], 
                          blue_balls: List[int], predictions: Dict = None):
        """记录学习事件"""
        event = {
            'timestamp': datetime.now().isoformat(),
            'period': period_num,
            'red_balls': red_balls,
            'blue_balls': blue_balls,
            'predictions': predictions,
            'weights': self.feature_weights.copy(),
            'window_size': len(self.recent_data)
        }
        
        self.learning_log.append(event)
        
        # 保持日志大小
        if len(self.learning_log) > 1000:
            self.learning_log = self.learning_log[-500:]
    
    def save_learning_state(self, filepath: str):
        """保存学习状态"""
        state = {
            'recent_data': list(self.recent_data),
            'performance_history': {k: list(v) for k, v in self.performance_history.items()},
            'feature_weights': self.feature_weights,
            'learning_log': self.learning_log[-100:],  # 只保存最近100条
            'pattern_detector_state': self.pattern_detector.get_state(),
            'trend_analyzer_state': self.trend_analyzer.get_state()
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(state, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"学习状态已保存到 {filepath}")
    
    def load_learning_state(self, filepath: str) -> bool:
        """加载学习状态"""
        if not os.path.exists(filepath):
            print(f"状态文件 {filepath} 不存在")
            return False
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # 恢复状态
            self.recent_data = deque(state['recent_data'], maxlen=self.window_size)
            
            for k, v in state['performance_history'].items():
                self.performance_history[k] = deque(v, maxlen=100)
            
            self.feature_weights = state['feature_weights']
            self.learning_log = state['learning_log']
            
            # 恢复子组件状态
            self.pattern_detector.load_state(state['pattern_detector_state'])
            self.trend_analyzer.load_state(state['trend_analyzer_state'])
            
            print(f"学习状态已从 {filepath} 加载")
            return True
        except Exception as e:
            print(f"加载学习状态失败: {e}")
            return False


class PatternDetector:
    """模式检测器"""
    
    def __init__(self):
        self.patterns = defaultdict(int)
        self.recent_patterns = deque(maxlen=20)
    
    def update(self, red_balls: List[int], blue_balls: List[int]):
        """更新模式"""
        # 检测红球模式
        red_pattern = self._extract_red_pattern(red_balls)
        self.patterns[red_pattern] += 1
        self.recent_patterns.append(red_pattern)
    
    def _extract_red_pattern(self, red_balls: List[int]) -> str:
        """提取红球模式"""
        sorted_balls = sorted(red_balls)
        
        # 计算间距模式
        gaps = [sorted_balls[i+1] - sorted_balls[i] for i in range(4)]
        gap_pattern = tuple(gaps)
        
        return f"gaps_{gap_pattern}"
    
    def get_pattern_adjustments(self) -> Dict:
        """获取基于模式的调整建议"""
        if len(self.recent_patterns) < 5:
            return {}
        
        # 分析最近模式
        recent_pattern_counts = Counter(list(self.recent_patterns)[-5:])
        most_common = recent_pattern_counts.most_common(1)
        
        if most_common and most_common[0][1] >= 2:
            # 如果某个模式在最近5期中出现2次以上，给出调整建议
            return {'pattern_strength': 0.3}
        
        return {}
    
    def get_state(self) -> Dict:
        """获取状态"""
        return {
            'patterns': dict(self.patterns),
            'recent_patterns': list(self.recent_patterns)
        }
    
    def load_state(self, state: Dict):
        """加载状态"""
        self.patterns = defaultdict(int, state.get('patterns', {}))
        self.recent_patterns = deque(state.get('recent_patterns', []), maxlen=20)


class TrendAnalyzer:
    """趋势分析器"""
    
    def __init__(self):
        self.red_trends = deque(maxlen=30)
        self.blue_trends = deque(maxlen=30)
    
    def update(self, red_balls: List[int], blue_balls: List[int]):
        """更新趋势"""
        # 记录红球趋势特征
        red_sum = sum(red_balls)
        red_span = max(red_balls) - min(red_balls)
        
        self.red_trends.append({
            'sum': red_sum,
            'span': red_span,
            'max_num': max(red_balls),
            'min_num': min(red_balls)
        })
        
        # 记录蓝球趋势特征
        blue_sum = sum(blue_balls)
        blue_gap = abs(blue_balls[1] - blue_balls[0]) if len(blue_balls) == 2 else 0
        
        self.blue_trends.append({
            'sum': blue_sum,
            'gap': blue_gap,
            'max_num': max(blue_balls),
            'min_num': min(blue_balls)
        })
    
    def get_trend_adjustments(self) -> Dict:
        """获取基于趋势的调整建议"""
        if len(self.red_trends) < 10:
            return {}
        
        adjustments = {}
        
        # 分析红球和值趋势
        recent_sums = [t['sum'] for t in list(self.red_trends)[-5:]]
        earlier_sums = [t['sum'] for t in list(self.red_trends)[-10:-5]]
        
        if len(recent_sums) == 5 and len(earlier_sums) == 5:
            recent_avg = np.mean(recent_sums)
            earlier_avg = np.mean(earlier_sums)
            
            if recent_avg > earlier_avg + 10:
                adjustments['red_sum_trend'] = 0.2  # 上升趋势
            elif recent_avg < earlier_avg - 10:
                adjustments['red_sum_trend'] = -0.2  # 下降趋势
        
        return adjustments
    
    def get_state(self) -> Dict:
        """获取状态"""
        return {
            'red_trends': list(self.red_trends),
            'blue_trends': list(self.blue_trends)
        }
    
    def load_state(self, state: Dict):
        """加载状态"""
        self.red_trends = deque(state.get('red_trends', []), maxlen=30)
        self.blue_trends = deque(state.get('blue_trends', []), maxlen=30)


def test_adaptive_learning_system():
    """测试自适应学习系统"""
    system = AdaptiveLearningSystem()
    
    print("测试自适应学习系统...")
    
    # 模拟一些开奖结果
    test_results = [
        (25001, [1, 5, 12, 20, 33], [2, 8]),
        (25002, [3, 8, 15, 22, 35], [1, 10]),
        (25003, [2, 9, 18, 25, 31], [4, 11]),
    ]
    
    for period, red, blue in test_results:
        # 模拟预测结果
        predictions = {
            'red_odd_even': '3:2',
            'red_size': '2:3',
            'blue_size': '1:1',
            'predicted_numbers': ([1, 8, 15, 22, 30], [2, 9])
        }
        
        system.update_with_new_result(period, red, blue, predictions)
    
    # 获取性能摘要
    summary = system.get_performance_summary()
    print(f"\n性能摘要: {summary}")
    
    # 获取当前权重
    weights = system.get_current_weights()
    print(f"当前权重: {weights}")


if __name__ == "__main__":
    test_adaptive_learning_system()
