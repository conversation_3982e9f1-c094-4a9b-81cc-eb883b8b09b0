# 大乐透预测系统依赖包

# 核心数据处理
pandas>=1.5.0
numpy>=1.21.0

# 机器学习
scikit-learn>=1.1.0
tensorflow>=2.10.0  # 可选，用于神经网络
torch>=1.12.0       # 可选，用于深度学习

# 数据可视化
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0      # 可选，用于交互式图表

# 统计分析
scipy>=1.9.0
statsmodels>=0.13.0

# 配置管理
python-dotenv>=0.19.0
pydantic>=1.10.0    # 可选，用于配置验证

# 测试框架
pytest>=7.0.0
pytest-cov>=3.0.0
pytest-mock>=3.8.0

# 代码质量
black>=22.0.0       # 代码格式化
flake8>=5.0.0       # 代码检查
mypy>=0.971         # 类型检查

# 文档生成
sphinx>=5.0.0       # 可选，用于生成文档
sphinx-rtd-theme>=1.0.0  # 可选，文档主题

# 性能分析
memory-profiler>=0.60.0  # 可选，内存分析
line-profiler>=3.5.0     # 可选，性能分析

# 日志和监控
structlog>=22.1.0   # 可选，结构化日志
rich>=12.5.0        # 可选，美化输出

# 数据库支持（可选）
sqlalchemy>=1.4.0   # 可选，数据库ORM
sqlite3             # 内置，轻量级数据库

# 并行处理（可选）
joblib>=1.1.0       # 并行计算
multiprocessing     # 内置，多进程

# 时间处理
python-dateutil>=2.8.0

# 网络请求（可选，用于外部数据）
requests>=2.28.0
aiohttp>=3.8.0      # 可选，异步HTTP

# 缓存（可选）
redis>=4.3.0        # 可选，Redis缓存
diskcache>=5.4.0    # 可选，磁盘缓存

# 开发工具
jupyter>=1.0.0      # 可选，Jupyter笔记本
ipython>=8.0.0      # 可选，增强的Python shell

# 部署相关（可选）
gunicorn>=20.1.0    # 可选，WSGI服务器
docker>=6.0.0       # 可选，容器化
