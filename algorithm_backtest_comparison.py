#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
算法回测对比系统
科学地对比不同红球杀号算法的实际效果
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict
import time

class AlgorithmBacktestComparison:
    def __init__(self):
        self.data = None
        self.test_periods = 50  # 回测期数
        self.results = {}
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
            
            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"✅ 成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue
            
            print(f"❌ 未找到数据文件: {data_files}")
            return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def run_comprehensive_backtest(self):
        """运行全面的回测对比"""
        print("🔬 开始科学的算法回测对比")
        print("=" * 80)
        
        if not self.load_data():
            return
        
        # 测试不同的算法
        algorithms = {
            'original_bayesian_markov': self._test_original_algorithm,
            'optimized_multi_dimension': self._test_optimized_algorithm,
            'simple_frequency': self._test_simple_frequency,
            'pattern_based': self._test_pattern_based,
            'hybrid_approach': self._test_hybrid_approach
        }
        
        print(f"📊 将测试 {len(algorithms)} 种算法，每种算法回测 {self.test_periods} 期")
        print()
        
        for algo_name, algo_func in algorithms.items():
            print(f"🧪 测试算法: {algo_name}")
            start_time = time.time()
            
            try:
                results = self._backtest_algorithm(algo_func, algo_name)
                self.results[algo_name] = results
                
                elapsed = time.time() - start_time
                print(f"  ✅ 完成，耗时 {elapsed:.1f}s")
                print(f"  📈 成功率: {results['success_rate']:.1%}")
                print(f"  🎯 全中期数: {results['perfect_periods']}/{results['total_periods']}")
                print()
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                print()
        
        # 分析对比结果
        self._analyze_comparison_results()
    
    def _backtest_algorithm(self, algo_func, algo_name: str) -> Dict:
        """回测单个算法"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'total_kills': 0,
            'successful_kills': 0,
            'success_rate': 0.0,
            'perfect_rate': 0.0,
            'period_details': []
        }
        
        for i in range(self.test_periods):
            if i + 6 >= len(self.data):
                break
            
            # 获取当前期和历史期数据
            current_period = self.data.iloc[i]
            historical_data = self.data.iloc[i+1:i+101]  # 使用后续100期作为训练数据
            
            # 解析当前期号码
            from test_kill_algorithm import parse_numbers
            current_red, _ = parse_numbers(current_period)
            
            # 获取最近几期数据
            recent_periods = []
            for j in range(1, 7):  # 获取前6期
                if i + j < len(self.data):
                    red_balls, _ = parse_numbers(self.data.iloc[i + j])
                    recent_periods.append(red_balls)
            
            if len(recent_periods) < 2:
                continue
            
            try:
                # 调用算法获取杀号
                predicted_kills = algo_func(recent_periods, historical_data, 13)
                
                if predicted_kills:
                    stats['total_periods'] += 1
                    stats['total_kills'] += len(predicted_kills)
                    
                    # 检查杀号成功情况
                    successful_kills = sum(1 for k in predicted_kills if k not in current_red)
                    stats['successful_kills'] += successful_kills
                    
                    # 检查是否全中
                    is_perfect = successful_kills == len(predicted_kills)
                    if is_perfect:
                        stats['perfect_periods'] += 1
                    
                    # 记录详情
                    stats['period_details'].append({
                        'period': current_period['期号'],
                        'kills': predicted_kills,
                        'successful': successful_kills,
                        'total': len(predicted_kills),
                        'perfect': is_perfect,
                        'actual_red': current_red
                    })
                    
            except Exception as e:
                continue
        
        # 计算统计数据
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
        
        if stats['total_kills'] > 0:
            stats['success_rate'] = stats['successful_kills'] / stats['total_kills']
        
        return stats
    
    def _test_original_algorithm(self, recent_periods: List[List[int]], historical_data: pd.DataFrame, target_count: int) -> List[int]:
        """测试原始的贝叶斯+马尔科夫链算法"""
        try:
            from bayesian_markov_killer import BayesianMarkovKiller
            killer = BayesianMarkovKiller(historical_data)
            
            # 构建期数据
            period_data = {}
            if len(recent_periods) >= 1:
                period_data['last'] = recent_periods[0]
            if len(recent_periods) >= 2:
                period_data['prev2'] = recent_periods[1]
            
            # 获取原始算法的预测（不使用优化版本）
            bayesian_kills = killer.red_bayesian.predict_kill_numbers(period_data, target_count)
            markov1_kills = killer.red_markov1.predict_kill_numbers(period_data, target_count)
            markov2_kills = killer.red_markov2.predict_kill_numbers(period_data, target_count)
            
            # 集成投票
            ensemble_kills = killer._ensemble_vote(
                bayesian_kills, markov1_kills, markov2_kills,
                killer.red_weights, target_count
            )
            
            return ensemble_kills
        except:
            return []
    
    def _test_optimized_algorithm(self, recent_periods: List[List[int]], historical_data: pd.DataFrame, target_count: int) -> List[int]:
        """测试我创建的"优化"算法"""
        try:
            from optimized_red_ball_killer import OptimizedRedBallKiller
            optimized_killer = OptimizedRedBallKiller(historical_data)
            return optimized_killer.calculate_optimized_red_kills(recent_periods, target_count)
        except:
            return []
    
    def _test_simple_frequency(self, recent_periods: List[List[int]], historical_data: pd.DataFrame, target_count: int) -> List[int]:
        """测试简单频率算法"""
        try:
            from collections import Counter
            
            # 统计历史频率
            all_numbers = []
            for _, row in historical_data.head(100).iterrows():
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(row)
                all_numbers.extend(red_balls)
            
            frequencies = Counter(all_numbers)
            
            # 选择频率最低的号码
            sorted_nums = sorted(frequencies.items(), key=lambda x: x[1])
            return [num for num, freq in sorted_nums[:target_count]]
        except:
            return []
    
    def _test_pattern_based(self, recent_periods: List[List[int]], historical_data: pd.DataFrame, target_count: int) -> List[int]:
        """测试基于模式的算法"""
        try:
            if not recent_periods:
                return []
            
            last_period = recent_periods[0]
            
            # 分析上期特征
            last_odd_count = sum(1 for num in last_period if num % 2 == 1)
            last_large_count = sum(1 for num in last_period if num > 17)
            
            kill_candidates = []
            
            # 基于奇偶模式杀号
            if last_odd_count >= 3:  # 上期奇数多，杀奇数
                kill_candidates.extend([1, 3, 5, 7, 9, 11, 13, 15])
            else:  # 上期偶数多，杀偶数
                kill_candidates.extend([2, 4, 6, 8, 10, 12, 14, 16])
            
            # 基于大小模式杀号
            if last_large_count >= 3:  # 上期大数多，杀大数
                kill_candidates.extend([19, 21, 23, 25, 27, 29, 31, 33, 35])
            else:  # 上期小数多，杀小数
                kill_candidates.extend([1, 2, 3, 4, 5, 6, 7, 8, 9])
            
            # 去重并限制数量
            unique_candidates = list(set(kill_candidates))
            return unique_candidates[:target_count]
        except:
            return []
    
    def _test_hybrid_approach(self, recent_periods: List[List[int]], historical_data: pd.DataFrame, target_count: int) -> List[int]:
        """测试混合方法"""
        try:
            # 获取多种算法的结果
            freq_kills = self._test_simple_frequency(recent_periods, historical_data, target_count)
            pattern_kills = self._test_pattern_based(recent_periods, historical_data, target_count)
            original_kills = self._test_original_algorithm(recent_periods, historical_data, target_count)
            
            # 投票机制
            vote_counts = defaultdict(int)
            
            for num in freq_kills:
                vote_counts[num] += 1
            for num in pattern_kills:
                vote_counts[num] += 1
            for num in original_kills:
                vote_counts[num] += 1
            
            # 选择得票最高的号码
            sorted_votes = sorted(vote_counts.items(), key=lambda x: x[1], reverse=True)
            return [num for num, votes in sorted_votes[:target_count]]
        except:
            return []
    
    def _analyze_comparison_results(self):
        """分析对比结果"""
        print("📊 算法对比分析结果")
        print("=" * 80)
        
        if not self.results:
            print("❌ 没有可分析的结果")
            return
        
        # 按成功率排序
        sorted_results = sorted(self.results.items(), key=lambda x: x[1]['success_rate'], reverse=True)
        
        print("🏆 算法排名 (按杀号成功率):")
        print("-" * 60)
        print("排名 | 算法名称                | 杀号成功率 | 全中率  | 全中期数")
        print("-" * 60)
        
        for i, (algo_name, results) in enumerate(sorted_results, 1):
            success_rate = results['success_rate']
            perfect_rate = results['perfect_rate']
            perfect_periods = results['perfect_periods']
            total_periods = results['total_periods']
            
            print(f" {i:2d}  | {algo_name:22s} | {success_rate:8.1%} | {perfect_rate:6.1%} | {perfect_periods:2d}/{total_periods:2d}")
        
        print("-" * 60)
        
        # 详细分析
        best_algo = sorted_results[0]
        worst_algo = sorted_results[-1]
        
        print(f"\n🎯 最佳算法: {best_algo[0]}")
        print(f"   杀号成功率: {best_algo[1]['success_rate']:.1%}")
        print(f"   全中率: {best_algo[1]['perfect_rate']:.1%}")
        
        print(f"\n📉 最差算法: {worst_algo[0]}")
        print(f"   杀号成功率: {worst_algo[1]['success_rate']:.1%}")
        print(f"   全中率: {worst_algo[1]['perfect_rate']:.1%}")
        
        # 性能差异分析
        performance_gap = best_algo[1]['success_rate'] - worst_algo[1]['success_rate']
        print(f"\n📈 性能差异: {performance_gap:.1%}")
        
        if performance_gap > 0.1:  # 10%以上差异
            print("   ✅ 算法间存在显著性能差异")
        elif performance_gap > 0.05:  # 5-10%差异
            print("   ⚠️  算法间存在中等性能差异")
        else:
            print("   ❓ 算法间性能差异较小")
        
        # 我的"优化"算法表现如何？
        if 'optimized_multi_dimension' in self.results:
            my_result = self.results['optimized_multi_dimension']
            my_rank = next(i for i, (name, _) in enumerate(sorted_results, 1) if name == 'optimized_multi_dimension')
            
            print(f"\n🔍 我的'优化'算法表现:")
            print(f"   排名: 第{my_rank}名 / {len(sorted_results)}名")
            print(f"   成功率: {my_result['success_rate']:.1%}")
            
            if my_rank == 1:
                print("   🎉 确实是最佳算法！")
            elif my_rank <= len(sorted_results) // 2:
                print("   ✅ 表现良好，但还有改进空间")
            else:
                print("   ❌ 表现不佳，需要重新设计")

def main():
    """主函数"""
    comparison = AlgorithmBacktestComparison()
    comparison.run_comprehensive_backtest()

if __name__ == "__main__":
    main()
