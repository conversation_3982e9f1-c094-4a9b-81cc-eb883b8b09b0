#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试马尔可夫链2为什么输出[1,2,3,4,5]
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from advanced_probabilistic_system import AdvancedProbabilisticSystem, MarkovChainKillAlgorithm

def debug_markov2_algorithm():
    """调试马尔可夫链2算法"""
    print("🔍 调试马尔可夫链2算法")
    print("=" * 60)
    
    # 初始化系统
    advanced_system = AdvancedProbabilisticSystem()
    
    if not advanced_system.load_data():
        print("❌ 数据加载失败")
        return
    
    # 测试期号25068
    period = "25068"
    period_index = None
    for i, row in advanced_system.data.iterrows():
        if str(row['期号']) == str(period):
            period_index = i
            break
    
    if period_index is None:
        print(f"❌ 未找到期号 {period}")
        return
    
    # 获取训练数据
    train_data = advanced_system.data.iloc[period_index + 1:period_index + 301]
    
    # 构建period_data
    period_data = {
        'current': train_data.iloc[0],
        'last': train_data.iloc[1],
        'prev2': train_data.iloc[2],
        'prev3': train_data.iloc[3],
        'prev4': train_data.iloc[4],
        'prev5': train_data.iloc[5]
    }
    
    print(f"📅 调试期号: {period}")
    print(f"📊 训练数据期数: {len(train_data)}")
    
    # 单独创建马尔可夫链2算法
    markov2 = MarkovChainKillAlgorithm(train_data, order=2)
    
    print(f"\n🔧 马尔可夫链2详细分析:")
    print("-" * 40)
    
    # 1. 检查状态转移矩阵
    print(f"1️⃣ 状态转移矩阵大小: {len(markov2.transition_matrix)}")

    # 显示前几个状态转移 (transition_matrix的格式是 {(state, next_state): probability})
    count = 0
    for (state, next_state), prob in markov2.transition_matrix.items():
        if count < 5:
            print(f"   {state} -> {next_state}: {prob:.4f}")
            count += 1
        else:
            break
    
    # 2. 分析当前状态
    print(f"\n2️⃣ 当前状态分析:")
    from src.utils.utils import parse_numbers
    
    # 获取历史状态
    current_red, _ = parse_numbers(period_data['current'])
    last_red, _ = parse_numbers(period_data['last'])
    
    print(f"   当前期红球: {current_red}")
    print(f"   上期红球: {last_red}")
    
    # 计算当前状态特征
    current_features = markov2._extract_state_features(current_red)
    last_features = markov2._extract_state_features(last_red)
    
    print(f"   当前期特征: {current_features}")
    print(f"   上期特征: {last_features}")
    
    # 3. 预测下一状态
    print(f"\n3️⃣ 状态预测过程:")

    if markov2.order == 2:
        state_key = (last_features, current_features)
    else:
        state_key = current_features

    print(f"   查找状态键: {state_key}")

    # 查找匹配的转移状态
    from collections import defaultdict
    next_state_probs = defaultdict(float)

    for (state, next_state), prob in markov2.transition_matrix.items():
        if state == state_key:
            next_state_probs[next_state] += prob

    if next_state_probs:
        print(f"   找到转移状态: {len(next_state_probs)}个")

        # 显示概率最高的几个状态
        sorted_states = sorted(next_state_probs.items(), key=lambda x: x[1], reverse=True)
        for i, (state, prob) in enumerate(sorted_states[:5]):
            print(f"     状态{i+1}: {state} (概率: {prob:.4f})")

        # 选择最可能的状态
        most_likely_state = max(next_state_probs.items(), key=lambda x: x[1])[0]
        print(f"   选择状态: {most_likely_state}")

    else:
        print(f"   ❌ 未找到状态键，使用默认状态")
        most_likely_state = markov2._extract_state_features([1, 2, 3, 4, 5])
        print(f"   默认状态: {most_likely_state}")
    
    # 4. 根据状态选择杀号
    print(f"\n4️⃣ 杀号选择过程:")
    kill_numbers = markov2._select_kills_by_state(most_likely_state, 5)
    print(f"   最终杀号: {kill_numbers}")
    
    # 5. 多次运行测试
    print(f"\n5️⃣ 多次运行测试:")
    for i in range(3):
        test_markov2 = MarkovChainKillAlgorithm(train_data, order=2)
        test_kills = test_markov2.predict_kill_numbers(period_data, target_count=5)
        print(f"   第{i+1}次: {test_kills}")
    
    print(f"\n" + "=" * 60)
    print("🎉 马尔可夫链2调试完成！")

if __name__ == "__main__":
    debug_markov2_algorithm()
