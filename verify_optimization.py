#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证主系统是否使用优化的红球杀号算法
"""

from src.systems.main import LotteryPredictor
import pandas as pd

def verify_optimization():
    """验证优化算法是否正在使用"""
    print("🔍 验证主系统是否使用优化的红球杀号算法")
    print("=" * 60)
    
    # 初始化预测系统
    predictor = LotteryPredictor()
    
    # 进行一次预测
    print("📅 进行预测测试...")
    prediction = predictor.predict_next_period(0)
    
    # 显示杀号结果
    kill_numbers = prediction['kill_numbers']
    red_kills = kill_numbers['red_universal']
    blue_kills = kill_numbers['blue_universal']
    
    print(f"✅ 预测成功完成")
    print(f"  期号: {prediction['period']}")
    print(f"  红球杀号: {red_kills} (共{len(red_kills)}个)")
    print(f"  蓝球杀号: {blue_kills} (共{len(blue_kills)}个)")
    
    # 验证杀号数量
    print(f"\n🎯 杀号数量验证:")
    print(f"  红球杀号目标: 13个, 实际: {len(red_kills)}个 {'✅' if len(red_kills) == 13 else '❌'}")
    print(f"  蓝球杀号目标: 5个, 实际: {len(blue_kills)}个 {'✅' if len(blue_kills) == 5 else '❌'}")
    
    # 分析红球杀号特征
    if red_kills:
        print(f"\n📊 红球杀号特征分析:")
        
        # 奇偶分布
        odd_count = sum(1 for num in red_kills if num % 2 == 1)
        even_count = len(red_kills) - odd_count
        print(f"  奇偶分布: 奇数{odd_count}个, 偶数{even_count}个")
        
        # 大小分布
        small_count = sum(1 for num in red_kills if num <= 17)
        large_count = len(red_kills) - small_count
        print(f"  大小分布: 小数{small_count}个, 大数{large_count}个")
        
        # 号码范围
        min_num = min(red_kills)
        max_num = max(red_kills)
        range_span = max_num - min_num
        print(f"  号码范围: {min_num}-{max_num} (跨度{range_span})")
        
        # 分布均匀性
        distribution_score = calculate_distribution_score(red_kills)
        print(f"  分布均匀性: {distribution_score:.2f} (0-1, 越高越均匀)")
    
    # 测试优化算法是否被调用
    print(f"\n🔧 测试优化算法调用:")
    try:
        from optimized_red_ball_killer import OptimizedRedBallKiller
        
        # 加载数据
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                break
            except FileNotFoundError:
                continue
        
        if data is not None:
            # 获取最近几期数据
            recent_periods = []
            for i in range(6):
                if i < len(data):
                    from test_kill_algorithm import parse_numbers
                    red_balls, _ = parse_numbers(data.iloc[i])
                    recent_periods.append(red_balls)
            
            # 直接调用优化算法
            optimized_killer = OptimizedRedBallKiller(data)
            direct_kills = optimized_killer.calculate_optimized_red_kills(recent_periods, 13)
            
            print(f"  ✅ 优化算法可正常调用")
            print(f"  直接调用结果: {sorted(direct_kills)} (共{len(direct_kills)}个)")
            
            # 比较主系统结果和直接调用结果
            if set(red_kills) == set(direct_kills):
                print(f"  🎯 主系统正在使用优化算法 (结果完全一致)")
            else:
                overlap = len(set(red_kills) & set(direct_kills))
                total = len(set(red_kills) | set(direct_kills))
                similarity = overlap / total if total > 0 else 0
                print(f"  ⚠️  结果相似度: {similarity:.1%} ({overlap}/{total}个号码重叠)")
                
                if similarity >= 0.8:
                    print(f"  ✅ 主系统可能使用优化算法 (高相似度)")
                elif similarity >= 0.5:
                    print(f"  ⚠️  主系统可能部分使用优化算法 (中等相似度)")
                else:
                    print(f"  ❌ 主系统可能未使用优化算法 (低相似度)")
        else:
            print(f"  ❌ 无法加载数据文件进行验证")
            
    except ImportError as e:
        print(f"  ❌ 优化算法模块导入失败: {e}")
    except Exception as e:
        print(f"  ❌ 优化算法测试失败: {e}")
    
    # 性能指标评估
    print(f"\n📈 性能指标评估:")
    success_rate = prediction.get('kill_success_rate', 0)
    print(f"  杀号成功率: {success_rate:.1%}")
    
    # 算法特征检查
    print(f"\n🔬 算法特征检查:")
    
    # 检查是否有多维度分析的特征
    features_detected = []
    
    # 特征1: 分布均匀性 (优化算法应该有更好的分布)
    if red_kills:
        if distribution_score > 0.6:
            features_detected.append("良好的号码分布")
        
        # 特征2: 奇偶平衡 (优化算法应该更平衡)
        odd_ratio = odd_count / len(red_kills)
        if 0.4 <= odd_ratio <= 0.6:
            features_detected.append("平衡的奇偶分布")
        
        # 特征3: 大小平衡 (优化算法应该更平衡)
        small_ratio = small_count / len(red_kills)
        if 0.4 <= small_ratio <= 0.7:
            features_detected.append("合理的大小分布")
        
        # 特征4: 范围覆盖 (优化算法应该有合理的范围)
        if 20 <= range_span <= 34:
            features_detected.append("合理的号码范围")
    
    if features_detected:
        print(f"  检测到优化特征: {', '.join(features_detected)}")
        print(f"  🎯 算法优化程度: {len(features_detected)}/4 项指标良好")
    else:
        print(f"  ⚠️  未检测到明显的优化特征")
    
    print(f"\n🎉 验证完成！")

def calculate_distribution_score(numbers):
    """计算号码分布的均匀性评分"""
    if not numbers or len(numbers) < 2:
        return 0
    
    # 将1-35分为7个区间，每个区间5个号码
    intervals = [0] * 7
    for num in numbers:
        interval_idx = min((num - 1) // 5, 6)
        intervals[interval_idx] += 1
    
    # 计算分布的标准差，标准差越小分布越均匀
    mean = len(numbers) / 7
    variance = sum((count - mean) ** 2 for count in intervals) / 7
    std_dev = variance ** 0.5
    
    # 转换为0-1评分，标准差越小评分越高
    max_std = (len(numbers) ** 0.5)  # 理论最大标准差
    score = max(0, 1 - (std_dev / max_std))
    
    return score

if __name__ == "__main__":
    verify_optimization()
