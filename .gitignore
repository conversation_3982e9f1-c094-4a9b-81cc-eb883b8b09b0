# 大乐透预测系统 .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# 项目特定文件
# 日志文件
logs/
*.log

# 数据文件（保留示例）
data/processed/
data/external/
!data/raw/dlt_data.csv

# 模型文件
models/saved/
*.pkl
*.joblib
*.h5
*.pt
*.pth

# 配置文件（保留示例）
.env
config/local_settings.py

# 临时文件
temp/
tmp/
*.tmp
*.bak
*.swp
*~

# IDE文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 性能分析文件
*.prof
*.lprof

# 缓存文件
.cache/
cache/

# 输出文件
output/
results/
reports/

# 备份文件
backup/
*.backup

# 测试输出
test_output/
test_results/

# 部署文件
deployment/local/
deployment/secrets/

# 文档构建
docs/build/
docs/_build/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z
