#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试杀号修复效果
"""

from src.systems.main import LotteryPredictor

def test_kill_fix():
    """测试杀号修复效果"""
    print("🔧 测试杀号修复效果")
    print("=" * 60)
    
    # 初始化预测系统
    predictor = LotteryPredictor()
    
    # 进行一次预测
    print("\n📊 进行预测测试...")
    prediction = predictor.predict_next_period(0)
    
    # 获取杀号数据
    kill_numbers = prediction['kill_numbers']
    print(f"杀号数据: {kill_numbers}")
    
    # 获取预测组合
    combinations = prediction.get('all_combinations', [])
    print(f"\n生成的组合数: {len(combinations)}")
    
    # 检查前5组是否包含被杀号码
    red_kills = kill_numbers.get('red_universal', [])
    blue_kills = kill_numbers.get('blue_universal', [])
    
    print(f"\n红球杀号: {red_kills}")
    print(f"蓝球杀号: {blue_kills}")
    
    print(f"\n🔍 检查预测组合是否包含被杀号码:")
    
    violations_found = False
    for i, (red, blue) in enumerate(combinations[:5], 1):
        red_violations = [num for num in red if num in red_kills]
        blue_violations = [num for num in blue if num in blue_kills]
        
        print(f"第{i}组: 红球{red}, 蓝球{blue}")
        
        if red_violations:
            print(f"  ❌ 红球违规: {red_violations}")
            violations_found = True
        else:
            print(f"  ✅ 红球杀号生效")
        
        if blue_violations:
            print(f"  ❌ 蓝球违规: {blue_violations}")
            violations_found = True
        else:
            print(f"  ✅ 蓝球杀号生效")
    
    if violations_found:
        print(f"\n❌ 杀号修复失败！预测组合中仍包含被杀号码")
    else:
        print(f"\n🎉 杀号修复成功！所有预测组合都避开了被杀号码")

if __name__ == "__main__":
    test_kill_fix()
