"""
杀号算法专门测试脚本
用于验证和优化红球、蓝球杀号算法的准确性
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import Counter
import sys
import os

# 添加src路径
sys.path.append('src')
from utils.utils import parse_numbers
from utils.universal_killer import UniversalKiller
from utils.blue_killer import BlueKiller

class KillAlgorithmTester:
    """杀号算法测试器"""
    
    def __init__(self):
        self.data = None
        self.red_killer = UniversalKiller()
        self.blue_killer = BlueKiller()
        
    def load_data(self, file_path: str = "dlt_data.csv"):
        """加载彩票数据"""
        try:
            self.data = pd.read_csv(file_path)
            print(f"✅ 成功加载数据: {len(self.data)} 期")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def test_red_kill_algorithms(self, test_periods: int = 20) -> Dict:
        """测试红球杀号算法"""
        print(f"\n🔍 测试红球杀号算法 (最近{test_periods}期)")
        print("=" * 60)
        
        results = {
            'total_periods': 0,
            'algorithm_results': {},
            'combined_results': {},
            'success_details': []
        }
        
        # 测试各个算法 (包含基于上期开奖号码加减分析的新算法)
        algorithms = [
            'min_two_sum',      # 最小两数相加法 - 87.5%
            'max_two_diff',     # 最大两数相减法 - 83.6%
            'sum_tail',         # 和值尾数法 - 88.3%
            'span',             # 跨度计算法 - 91.1%
            'odd_even',         # 奇偶比例法 - 90.9%
            'consecutive',      # 连号分析法 - 84.4%
            'ac_value',         # AC值杀号法 - 85.3%
            'fibonacci',        # 斐波那契杀号法 - 88.7%
            'position_sum',     # 位置和杀号法 - 80.7%
            'interval_kill',    # 区间杀号法 - 90.5%
            'remainder_kill',   # 余数杀号法 - 88.9%
            'digital_root',     # 数字根杀号法 - 88.5%
            'symmetry_kill',    # 对称杀号法 - 90.0%
            'triangle_kill',    # 三角数杀号法 - 83.6%
            'lucas_kill',       # 卢卡斯数列杀号法 - 81.4%
            'catalan_kill',     # 卡塔兰数杀号法 - 91.3%
            'harmonic_kill',    # 调和数杀号法 - 84.1%
            'pentagonal_kill',  # 五边形数杀号法 - 97.6%
            'binary_kill',      # 二进制杀号法 - 89.3%
            'factorial_kill',   # 阶乘杀号法 - 95.0%
            'modular_kill',     # 模运算杀号法 - 90.0%
            'palindrome_kill',  # 回文数杀号法 - 83.3%
            'abundant_kill',    # 过剩数杀号法 - 95.8%
            'deficient_kill',   # 亏数杀号法 - 88.9%
            # 新增：基于上期开奖号码加减分析的算法
            'last_period_plus',     # 上期号码+1杀号法
            'last_period_minus',    # 上期号码-1杀号法
            'last_period_plus2',    # 上期号码+2杀号法
            'last_period_minus2',   # 上期号码-2杀号法
            'last_period_plus3',    # 上期号码+3杀号法
            'last_period_minus3',   # 上期号码-3杀号法
            'last_period_double',   # 上期号码×2杀号法
            'last_period_half',     # 上期号码÷2杀号法
            'last_period_reverse',  # 上期号码反向杀号法
            'last_period_sum_kill', # 上期号码和值杀号法
            'last_period_diff_kill', # 上期号码差值杀号法
            'last_period_avg_kill',  # 上期号码平均值杀号法
            'last_period_median_kill', # 上期号码中位数杀号法
            'last_period_range_kill',  # 上期号码范围杀号法
            'last_period_pattern_kill', # 上期号码模式杀号法
            # 新增：基于上上期号码杀号的算法
            'prev2_period_plus',       # 上上期号码+1杀号法
            'prev2_period_minus',      # 上上期号码-1杀号法
            'prev2_period_double',     # 上上期号码×2杀号法
            'prev2_period_half',       # 上上期号码÷2杀号法
            'prev2_period_reverse',    # 上上期号码反向杀号法
            'prev2_period_sum_kill',   # 上上期号码和值杀号法
            'prev2_period_diff_kill',  # 上上期号码差值杀号法
            'prev2_period_avg_kill',   # 上上期号码平均值杀号法
            'prev2_period_median_kill', # 上上期号码中位数杀号法
            'prev2_period_pattern_kill', # 上上期号码模式杀号法
            # 新增：其他维度的杀号算法
            'weekday_kill',            # 星期维度杀号法
            'month_kill',              # 月份维度杀号法
            'season_kill',             # 季节维度杀号法
            'lunar_kill',              # 农历维度杀号法
            'zodiac_kill',             # 生肖维度杀号法
            'element_kill',            # 五行维度杀号法
            'color_kill',              # 颜色维度杀号法
            'direction_kill',          # 方位维度杀号法
            'energy_kill',             # 能量维度杀号法
            'frequency_kill',          # 频率维度杀号法
            'temperature_kill',        # 温度维度杀号法
            'weather_kill',            # 天气维度杀号法
            'emotion_kill',            # 情感维度杀号法
            'music_kill',              # 音乐维度杀号法
            'art_kill',                # 艺术维度杀号法
            'philosophy_kill',         # 哲学维度杀号法
            'psychology_kill',         # 心理学维度杀号法
            'sociology_kill',          # 社会学维度杀号法
            'economics_kill',          # 经济学维度杀号法
            'physics_kill'             # 物理学维度杀号法
        ]
        
        for algo in algorithms:
            results['algorithm_results'][algo] = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'details': []
            }
        
        # 逐期测试
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期
            
            # 解析号码
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)
            
            period_info = {
                'period': current_period['期号'],
                'actual_red': current_red,
                'prev_periods': [period1_red, period2_red],
                'current_period': current_period,  # 添加当前期完整信息
                'algorithm_kills': {}
            }
            
            # 测试各个算法
            self._test_individual_algorithms(period_info, algorithms, results)
            
            results['total_periods'] += 1
            results['success_details'].append(period_info)
        
        # 计算成功率
        for algo in algorithms:
            algo_result = results['algorithm_results'][algo]
            if algo_result['total_kills'] > 0:
                algo_result['success_rate'] = algo_result['successful_kills'] / algo_result['total_kills']
        
        return results
    
    def _test_individual_algorithms(self, period_info: Dict, algorithms: List[str], results: Dict):
        """测试单个算法"""
        period1_red = period_info['prev_periods'][0]
        period2_red = period_info['prev_periods'][1]
        actual_red = period_info['actual_red']
        current_period = period_info['current_period']  # 添加当前期信息
        
        # 测试各个算法
        for algo in algorithms:
            if algo == 'min_two_sum':
                kills = self.red_killer._calculate_min_two_sum_kill(period1_red, period2_red)
            elif algo == 'max_two_diff':
                kills = self.red_killer._calculate_max_two_diff_kill(period1_red, period2_red)
            elif algo == 'sum_tail':
                kills = self.red_killer._calculate_sum_tail_kill(period1_red, period2_red)
            elif algo == 'span':
                kills = self.red_killer._calculate_span_kill(period1_red, period2_red)
            elif algo == 'odd_even':
                kills = self.red_killer._calculate_odd_even_kill(period1_red, period2_red)
            elif algo == 'consecutive':
                kills = self.red_killer._calculate_consecutive_kill(period1_red, period2_red)
            elif algo == 'ac_value':
                kills = self._calculate_ac_value_kill(period1_red, period2_red)
            elif algo == 'fibonacci':
                kills = self._calculate_fibonacci_kill(period1_red, period2_red)
            elif algo == 'position_sum':
                kills = self._calculate_position_sum_kill(period1_red, period2_red)
            elif algo == 'interval_kill':
                kills = self._calculate_interval_kill(period1_red, period2_red)
            elif algo == 'remainder_kill':
                kills = self._calculate_remainder_kill(period1_red, period2_red)
            elif algo == 'digital_root':
                kills = self._calculate_digital_root_kill(period1_red, period2_red)
            elif algo == 'symmetry_kill':
                kills = self._calculate_symmetry_kill(period1_red, period2_red)
            elif algo == 'triangle_kill':
                kills = self._calculate_triangle_kill(period1_red, period2_red)
            elif algo == 'lucas_kill':
                kills = self._calculate_lucas_kill(period1_red, period2_red)
            elif algo == 'catalan_kill':
                kills = self._calculate_catalan_kill(period1_red, period2_red)
            elif algo == 'harmonic_kill':
                kills = self._calculate_harmonic_kill(period1_red, period2_red)
            elif algo == 'pentagonal_kill':
                kills = self._calculate_pentagonal_kill(period1_red, period2_red)
            elif algo == 'binary_kill':
                kills = self._calculate_binary_kill(period1_red, period2_red)
            elif algo == 'factorial_kill':
                kills = self._calculate_factorial_kill(period1_red, period2_red)
            elif algo == 'modular_kill':
                kills = self._calculate_modular_kill(period1_red, period2_red)
            elif algo == 'palindrome_kill':
                kills = self._calculate_palindrome_kill(period1_red, period2_red)
            elif algo == 'abundant_kill':
                kills = self._calculate_abundant_kill(period1_red, period2_red)
            elif algo == 'deficient_kill':
                kills = self._calculate_deficient_kill(period1_red, period2_red)
            elif algo == 'last_period_plus':
                kills = self._calculate_last_period_plus_kill(period1_red)
            elif algo == 'last_period_minus':
                kills = self._calculate_last_period_minus_kill(period1_red)
            elif algo == 'last_period_plus2':
                kills = self._calculate_last_period_plus2_kill(period1_red)
            elif algo == 'last_period_minus2':
                kills = self._calculate_last_period_minus2_kill(period1_red)
            elif algo == 'last_period_plus3':
                kills = self._calculate_last_period_plus3_kill(period1_red)
            elif algo == 'last_period_minus3':
                kills = self._calculate_last_period_minus3_kill(period1_red)
            elif algo == 'last_period_double':
                kills = self._calculate_last_period_double_kill(period1_red)
            elif algo == 'last_period_half':
                kills = self._calculate_last_period_half_kill(period1_red)
            elif algo == 'last_period_reverse':
                kills = self._calculate_last_period_reverse_kill(period1_red)
            elif algo == 'last_period_sum_kill':
                kills = self._calculate_last_period_sum_kill(period1_red)
            elif algo == 'last_period_diff_kill':
                kills = self._calculate_last_period_diff_kill(period1_red)
            elif algo == 'last_period_avg_kill':
                kills = self._calculate_last_period_avg_kill(period1_red)
            elif algo == 'last_period_median_kill':
                kills = self._calculate_last_period_median_kill(period1_red)
            elif algo == 'last_period_range_kill':
                kills = self._calculate_last_period_range_kill(period1_red)
            elif algo == 'last_period_pattern_kill':
                kills = self._calculate_last_period_pattern_kill(period1_red)
            elif algo == 'prev2_period_plus':
                kills = self._calculate_prev2_period_plus_kill(period2_red)
            elif algo == 'prev2_period_minus':
                kills = self._calculate_prev2_period_minus_kill(period2_red)
            elif algo == 'prev2_period_double':
                kills = self._calculate_prev2_period_double_kill(period2_red)
            elif algo == 'prev2_period_half':
                kills = self._calculate_prev2_period_half_kill(period2_red)
            elif algo == 'prev2_period_reverse':
                kills = self._calculate_prev2_period_reverse_kill(period2_red)
            elif algo == 'prev2_period_sum_kill':
                kills = self._calculate_prev2_period_sum_kill(period2_red)
            elif algo == 'prev2_period_diff_kill':
                kills = self._calculate_prev2_period_diff_kill(period2_red)
            elif algo == 'prev2_period_avg_kill':
                kills = self._calculate_prev2_period_avg_kill(period2_red)
            elif algo == 'prev2_period_median_kill':
                kills = self._calculate_prev2_period_median_kill(period2_red)
            elif algo == 'prev2_period_pattern_kill':
                kills = self._calculate_prev2_period_pattern_kill(period2_red)
            elif algo == 'weekday_kill':
                kills = self._calculate_weekday_kill(current_period)
            elif algo == 'month_kill':
                kills = self._calculate_month_kill(current_period)
            elif algo == 'season_kill':
                kills = self._calculate_season_kill(current_period)
            elif algo == 'lunar_kill':
                kills = self._calculate_lunar_kill(current_period)
            elif algo == 'zodiac_kill':
                kills = self._calculate_zodiac_kill(current_period)
            elif algo == 'element_kill':
                kills = self._calculate_element_kill(current_period)
            elif algo == 'color_kill':
                kills = self._calculate_color_kill(current_period)
            elif algo == 'direction_kill':
                kills = self._calculate_direction_kill(current_period)
            elif algo == 'energy_kill':
                kills = self._calculate_energy_kill(current_period)
            elif algo == 'frequency_kill':
                kills = self._calculate_frequency_kill(current_period, period1_red, period2_red)
            elif algo == 'temperature_kill':
                kills = self._calculate_temperature_kill(current_period)
            elif algo == 'weather_kill':
                kills = self._calculate_weather_kill(current_period)
            elif algo == 'emotion_kill':
                kills = self._calculate_emotion_kill(current_period)
            elif algo == 'music_kill':
                kills = self._calculate_music_kill(current_period)
            elif algo == 'art_kill':
                kills = self._calculate_art_kill(current_period)
            elif algo == 'philosophy_kill':
                kills = self._calculate_philosophy_kill(current_period)
            elif algo == 'psychology_kill':
                kills = self._calculate_psychology_kill(current_period)
            elif algo == 'sociology_kill':
                kills = self._calculate_sociology_kill(current_period)
            elif algo == 'economics_kill':
                kills = self._calculate_economics_kill(current_period)
            elif algo == 'physics_kill':
                kills = self._calculate_physics_kill(current_period)
            else:
                kills = []
            
            # 验证杀号效果
            valid_kills = [k for k in kills if 1 <= k <= 35 and k not in (period1_red + period2_red)]
            successful_kills = [k for k in valid_kills if k not in actual_red]
            
            period_info['algorithm_kills'][algo] = {
                'kills': valid_kills,
                'successful': successful_kills,
                'success_count': len(successful_kills),
                'total_count': len(valid_kills)
            }
            
            # 更新统计
            results['algorithm_results'][algo]['total_kills'] += len(valid_kills)
            results['algorithm_results'][algo]['successful_kills'] += len(successful_kills)
            results['algorithm_results'][algo]['details'].append({
                'period': period_info['period'],
                'kills': valid_kills,
                'successful': successful_kills,
                'actual': actual_red
            })
    
    def test_blue_kill_algorithms(self, test_periods: int = 20) -> Dict:
        """测试蓝球杀号算法"""
        print(f"\n🔍 测试蓝球杀号算法 (最近{test_periods}期)")
        print("=" * 60)
        
        results = {
            'total_periods': 0,
            'algorithm_results': {},
            'success_details': []
        }
        
        # 测试各个算法 (专注于95%+准确性的蓝球算法)
        algorithms = [
            'sum',              # 蓝球相加法 - 73.0%
            'diff',             # 蓝球相减法 - 86.0%
            'multiple',         # 蓝球倍数法 - 80.0%
            'odd_even',         # 蓝球奇偶法 - 76.0%
            'blue_fib',         # 蓝球斐波那契法 - 84.2%
            'blue_interval',    # 蓝球区间法 - 81.4%
            'blue_square',      # 蓝球平方数法 - 87.5%
            'blue_triangle',    # 蓝球三角数法 - 81.5%
            'blue_lucas',       # 蓝球卢卡斯数法 - 87.5%
            'blue_perfect',     # 蓝球完全数法 - 93.3%
            'blue_binary',      # 蓝球二进制法 - 87.9%
            'blue_modular',     # 蓝球模运算法 - 90.0%
            'blue_palindrome',  # 蓝球回文数法 - 81.7%
            'blue_power',       # 蓝球幂次法 - 82.0%
            # 新增95%+准确性算法
            'blue_ultra_conservative',  # 蓝球超保守法
            'blue_pattern_break',       # 蓝球模式破坏法
            'blue_frequency_extreme',   # 蓝球频率极值法
            'blue_distance_max',        # 蓝球最大距离法
            'blue_composite_ultra',     # 蓝球超级复合法
            'blue_mathematical_pure',   # 蓝球纯数学法
            'blue_statistical_ultra',   # 蓝球超级统计法
            'blue_harmonic_advanced',   # 蓝球高级调和法
            'blue_golden_ratio',        # 蓝球黄金比例法
            'blue_chaos_theory'         # 蓝球混沌理论法
        ]
        
        for algo in algorithms:
            results['algorithm_results'][algo] = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'details': []
            }
        
        # 逐期测试
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break
                
            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期
            
            # 解析号码
            _, current_blue = parse_numbers(current_period)
            _, period1_blue = parse_numbers(period1_data)
            _, period2_blue = parse_numbers(period2_data)
            
            period_info = {
                'period': current_period['期号'],
                'actual_blue': current_blue,
                'prev_periods': [period1_blue, period2_blue],
                'algorithm_kills': {}
            }
            
            # 测试各个算法
            self._test_blue_individual_algorithms(period_info, algorithms, results)
            
            results['total_periods'] += 1
            results['success_details'].append(period_info)
        
        # 计算成功率
        for algo in algorithms:
            algo_result = results['algorithm_results'][algo]
            if algo_result['total_kills'] > 0:
                algo_result['success_rate'] = algo_result['successful_kills'] / algo_result['total_kills']
        
        return results
    
    def _test_blue_individual_algorithms(self, period_info: Dict, algorithms: List[str], results: Dict):
        """测试蓝球单个算法"""
        period1_blue = period_info['prev_periods'][0]
        period2_blue = period_info['prev_periods'][1]
        actual_blue = period_info['actual_blue']
        historical_data = self.data  # 添加历史数据
        
        # 测试各个算法
        for algo in algorithms:
            if algo == 'sum':
                kills = self.blue_killer._calculate_blue_sum_kill(period1_blue, period2_blue)
            elif algo == 'diff':
                kills = self.blue_killer._calculate_blue_diff_kill(period1_blue, period2_blue)
            elif algo == 'multiple':
                kills = self.blue_killer._calculate_blue_multiple_kill(period1_blue, period2_blue)
            elif algo == 'odd_even':
                kills = self.blue_killer._calculate_blue_odd_even_kill(period1_blue, period2_blue)
            elif algo == 'blue_fib':
                kills = self._calculate_blue_fib_kill(period1_blue, period2_blue)
            elif algo == 'blue_interval':
                kills = self._calculate_blue_interval_kill(period1_blue, period2_blue)
            elif algo == 'blue_square':
                kills = self._calculate_blue_square_kill(period1_blue, period2_blue)
            elif algo == 'blue_triangle':
                kills = self._calculate_blue_triangle_kill(period1_blue, period2_blue)
            elif algo == 'blue_lucas':
                kills = self._calculate_blue_lucas_kill(period1_blue, period2_blue)
            elif algo == 'blue_perfect':
                kills = self._calculate_blue_perfect_kill(period1_blue, period2_blue)
            elif algo == 'blue_binary':
                kills = self._calculate_blue_binary_kill(period1_blue, period2_blue)
            elif algo == 'blue_modular':
                kills = self._calculate_blue_modular_kill(period1_blue, period2_blue)
            elif algo == 'blue_palindrome':
                kills = self._calculate_blue_palindrome_kill(period1_blue, period2_blue)
            elif algo == 'blue_power':
                kills = self._calculate_blue_power_kill(period1_blue, period2_blue)
            elif algo == 'blue_ultra_conservative':
                kills = self._calculate_blue_ultra_conservative_kill(period1_blue, period2_blue)
            elif algo == 'blue_pattern_break':
                kills = self._calculate_blue_pattern_break_kill(period1_blue, period2_blue)
            elif algo == 'blue_frequency_extreme':
                kills = self._calculate_blue_frequency_extreme_kill(period1_blue, period2_blue, historical_data)
            elif algo == 'blue_distance_max':
                kills = self._calculate_blue_distance_max_kill(period1_blue, period2_blue)
            elif algo == 'blue_composite_ultra':
                kills = self._calculate_blue_composite_ultra_kill(period1_blue, period2_blue, historical_data)
            elif algo == 'blue_mathematical_pure':
                kills = self._calculate_blue_mathematical_pure_kill(period1_blue, period2_blue)
            elif algo == 'blue_statistical_ultra':
                kills = self._calculate_blue_statistical_ultra_kill(period1_blue, period2_blue, historical_data)
            elif algo == 'blue_harmonic_advanced':
                kills = self._calculate_blue_harmonic_advanced_kill(period1_blue, period2_blue)
            elif algo == 'blue_golden_ratio':
                kills = self._calculate_blue_golden_ratio_kill(period1_blue, period2_blue)
            elif algo == 'blue_chaos_theory':
                kills = self._calculate_blue_chaos_theory_kill(period1_blue, period2_blue)
            else:
                kills = []
            
            # 验证杀号效果
            valid_kills = [k for k in kills if 1 <= k <= 12 and k not in (period1_blue + period2_blue)]
            successful_kills = [k for k in valid_kills if k not in actual_blue]
            
            period_info['algorithm_kills'][algo] = {
                'kills': valid_kills,
                'successful': successful_kills,
                'success_count': len(successful_kills),
                'total_count': len(valid_kills)
            }
            
            # 更新统计
            results['algorithm_results'][algo]['total_kills'] += len(valid_kills)
            results['algorithm_results'][algo]['successful_kills'] += len(successful_kills)
            results['algorithm_results'][algo]['details'].append({
                'period': period_info['period'],
                'kills': valid_kills,
                'successful': successful_kills,
                'actual': actual_blue
            })
    
    def print_red_results(self, results: Dict):
        """打印红球测试结果"""
        print(f"\n📊 红球杀号算法测试结果 (共{results['total_periods']}期)")
        print("=" * 80)
        
        # 算法成功率排序
        algo_rates = [(algo, data['success_rate']) for algo, data in results['algorithm_results'].items()]
        algo_rates.sort(key=lambda x: x[1], reverse=True)
        
        print("🏆 算法成功率排行:")
        for i, (algo, rate) in enumerate(algo_rates, 1):
            data = results['algorithm_results'][algo]
            status = "🎯" if rate >= 0.8 else "⚠️" if rate >= 0.6 else "❌"
            print(f"  {i}. {algo:15} {rate:6.1%} ({data['successful_kills']}/{data['total_kills']}) {status}")
        
        # 详细分析
        print(f"\n📋 详细分析:")
        for algo, data in results['algorithm_results'].items():
            if data['total_kills'] > 0:
                print(f"\n{algo} 算法:")
                print(f"  总杀号数: {data['total_kills']}")
                print(f"  成功杀号: {data['successful_kills']}")
                print(f"  成功率: {data['success_rate']:.1%}")
                
                # 显示最近几期的详细情况
                print("  最近5期详情:")
                for detail in data['details'][:5]:
                    kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
                    success_str = ','.join(map(str, detail['successful'])) if detail['successful'] else '无'
                    actual_str = ','.join(map(str, detail['actual']))
                    print(f"    {detail['period']}: 杀号[{kills_str}] 成功[{success_str}] 实际[{actual_str}]")

    def _calculate_ac_value_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """AC值杀号法 - 基于号码间差值的绝对值"""
        kills = []
        all_numbers = period1 + period2

        # 计算AC值（所有号码间差值的绝对值的不重复个数）
        differences = set()
        for i in range(len(all_numbers)):
            for j in range(i + 1, len(all_numbers)):
                diff = abs(all_numbers[i] - all_numbers[j])
                differences.add(diff)

        ac_value = len(differences)

        # 基于AC值计算杀号
        kill_candidates = [
            ac_value % 35 + 1,
            (ac_value * 2) % 35 + 1,
            (ac_value + 10) % 35 + 1
        ]

        for num in kill_candidates:
            if 1 <= num <= 35:
                kills.append(num)

        return kills

    def _calculate_fibonacci_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """斐波那契杀号法 - 基于斐波那契数列"""
        kills = []

        # 斐波那契数列（35以内）
        fib_numbers = [1, 1, 2, 3, 5, 8, 13, 21, 34]

        # 计算两期号码的特征值
        sum1 = sum(period1)
        sum2 = sum(period2)

        # 基于特征值选择斐波那契数作为杀号
        fib_index = (sum1 + sum2) % len(fib_numbers)
        kills.append(fib_numbers[fib_index])

        # 添加相邻的斐波那契数
        if fib_index > 0:
            kills.append(fib_numbers[fib_index - 1])
        if fib_index < len(fib_numbers) - 1:
            kills.append(fib_numbers[fib_index + 1])

        return kills

    def _calculate_prime_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """质数杀号法 - 基于质数特性"""
        kills = []

        # 35以内的质数
        primes = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31]

        # 统计两期中出现的质数
        all_numbers = period1 + period2
        appeared_primes = [p for p in primes if p in all_numbers]

        # 如果质数出现过多，杀除未出现的质数
        if len(appeared_primes) >= 4:
            for prime in primes:
                if prime not in appeared_primes:
                    kills.append(prime)
                    if len(kills) >= 3:  # 最多杀3个质数
                        break

        return kills

    def _calculate_position_sum_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """位置和杀号法 - 基于位置权重和"""
        kills = []

        # 计算位置权重和
        weight_sum1 = sum(num * (i + 1) for i, num in enumerate(period1))
        weight_sum2 = sum(num * (i + 1) for i, num in enumerate(period2))

        # 基于位置权重和计算杀号
        kill_candidates = [
            weight_sum1 % 35 + 1,
            weight_sum2 % 35 + 1,
            (weight_sum1 + weight_sum2) % 35 + 1,
            abs(weight_sum1 - weight_sum2) % 35 + 1
        ]

        for num in kill_candidates:
            if 1 <= num <= 35:
                kills.append(num)

        return kills

    def _calculate_interval_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """区间杀号法 - 基于号码区间分布"""
        kills = []
        all_numbers = period1 + period2

        # 将1-35分为7个区间
        intervals = [
            (1, 5), (6, 10), (11, 15), (16, 20),
            (21, 25), (26, 30), (31, 35)
        ]

        # 统计每个区间的号码数量
        interval_counts = []
        for start, end in intervals:
            count = sum(1 for num in all_numbers if start <= num <= end)
            interval_counts.append(count)

        # 找出号码最多的区间，杀除该区间的号码
        max_count = max(interval_counts)
        if max_count >= 4:  # 如果某个区间号码过多
            max_interval_idx = interval_counts.index(max_count)
            start, end = intervals[max_interval_idx]

            # 杀除该区间中未出现的号码
            for num in range(start, end + 1):
                if num not in all_numbers:
                    kills.append(num)
                    if len(kills) >= 3:  # 最多杀3个
                        break

        return kills

    def _calculate_remainder_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """余数杀号法 - 基于除法余数"""
        kills = []
        all_numbers = period1 + period2

        # 计算各种余数分布
        for divisor in [3, 4, 5, 7]:
            remainder_counts = [0] * divisor
            for num in all_numbers:
                remainder_counts[num % divisor] += 1

            # 找出出现最多的余数
            max_count = max(remainder_counts)
            if max_count >= 3:  # 如果某个余数出现过多
                max_remainder = remainder_counts.index(max_count)

                # 杀除该余数对应的未出现号码
                for num in range(1, 36):
                    if num % divisor == max_remainder and num not in all_numbers:
                        kills.append(num)
                        if len(kills) >= 2:  # 每个除数最多杀2个
                            break

        return kills

    def _calculate_digital_root_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """数字根杀号法 - 基于数字根（各位数字和的最终单位数）"""
        kills = []

        def digital_root(n):
            while n >= 10:
                n = sum(int(digit) for digit in str(n))
            return n

        # 计算两期所有号码的数字根分布
        all_numbers = period1 + period2
        root_counts = [0] * 10  # 0-9

        for num in all_numbers:
            root = digital_root(num)
            root_counts[root] += 1

        # 找出出现最多的数字根
        max_count = max(root_counts)
        if max_count >= 3:
            max_root = root_counts.index(max_count)

            # 杀除该数字根对应的未出现号码
            for num in range(1, 36):
                if digital_root(num) == max_root and num not in all_numbers:
                    kills.append(num)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_symmetry_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """对称杀号法 - 基于号码对称性"""
        kills = []
        all_numbers = period1 + period2

        # 定义对称对（以18为中心）
        symmetry_pairs = [
            (1, 35), (2, 34), (3, 33), (4, 32), (5, 31),
            (6, 30), (7, 29), (8, 28), (9, 27), (10, 26),
            (11, 25), (12, 24), (13, 23), (14, 22), (15, 21),
            (16, 20), (17, 19)
        ]

        # 检查对称对的出现情况
        for num1, num2 in symmetry_pairs:
            if num1 in all_numbers and num2 not in all_numbers:
                # 如果对称的一边出现了，杀除另一边
                kills.append(num2)
            elif num2 in all_numbers and num1 not in all_numbers:
                kills.append(num1)

            if len(kills) >= 4:  # 最多杀4个对称号码
                break

        return kills

    def _calculate_triangle_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """三角数杀号法 - 基于三角数序列 T(n) = n(n+1)/2"""
        kills = []

        # 35以内的三角数
        triangle_numbers = []
        n = 1
        while True:
            triangle = n * (n + 1) // 2
            if triangle > 35:
                break
            triangle_numbers.append(triangle)
            n += 1

        all_numbers = period1 + period2
        appeared_triangles = [t for t in triangle_numbers if t in all_numbers]

        # 如果三角数出现过多，杀除未出现的三角数
        if len(appeared_triangles) >= 2:
            for triangle in triangle_numbers:
                if triangle not in appeared_triangles:
                    kills.append(triangle)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_square_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """平方数杀号法 - 基于完全平方数"""
        kills = []

        # 35以内的完全平方数
        square_numbers = [1, 4, 9, 16, 25]

        all_numbers = period1 + period2
        appeared_squares = [s for s in square_numbers if s in all_numbers]

        # 如果平方数出现过多，杀除未出现的平方数
        if len(appeared_squares) >= 2:
            for square in square_numbers:
                if square not in appeared_squares:
                    kills.append(square)

        return kills

    def _calculate_lucas_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """卢卡斯数列杀号法 - L(n) = L(n-1) + L(n-2), L(0)=2, L(1)=1"""
        kills = []

        # 35以内的卢卡斯数
        lucas_numbers = [2, 1, 3, 4, 7, 11, 18, 29]

        # 计算特征值
        sum1 = sum(period1)
        sum2 = sum(period2)

        # 基于特征值选择卢卡斯数
        lucas_index = (sum1 + sum2) % len(lucas_numbers)
        kills.append(lucas_numbers[lucas_index])

        # 添加相邻的卢卡斯数
        if lucas_index > 0:
            kills.append(lucas_numbers[lucas_index - 1])

        return kills

    def _calculate_catalan_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """卡塔兰数杀号法 - C(n) = (2n)! / ((n+1)! * n!)"""
        kills = []

        # 35以内的卡塔兰数
        catalan_numbers = [1, 1, 2, 5, 14]

        all_numbers = period1 + period2

        # 计算数字特征
        digit_sum = sum(int(d) for num in all_numbers for d in str(num))

        # 基于数字特征选择卡塔兰数
        catalan_index = digit_sum % len(catalan_numbers)
        if catalan_numbers[catalan_index] not in all_numbers:
            kills.append(catalan_numbers[catalan_index])

        return kills

    def _calculate_harmonic_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """调和数杀号法 - 基于调和级数的近似值"""
        kills = []
        all_numbers = period1 + period2

        # 计算调和数的近似值
        harmonic_sum = sum(1.0 / num for num in all_numbers if num > 0)

        # 基于调和和计算杀号
        harmonic_kill = int(harmonic_sum * 10) % 35 + 1
        if harmonic_kill not in all_numbers:
            kills.append(harmonic_kill)

        # 添加调和数的倍数
        harmonic_multiple = (int(harmonic_sum * 20)) % 35 + 1
        if harmonic_multiple not in all_numbers and harmonic_multiple != harmonic_kill:
            kills.append(harmonic_multiple)

        return kills

    def _calculate_pentagonal_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """五边形数杀号法 - P(n) = n(3n-1)/2"""
        kills = []

        # 35以内的五边形数
        pentagonal_numbers = []
        n = 1
        while True:
            pent = n * (3 * n - 1) // 2
            if pent > 35:
                break
            pentagonal_numbers.append(pent)
            n += 1

        all_numbers = period1 + period2
        appeared_pent = [p for p in pentagonal_numbers if p in all_numbers]

        # 如果五边形数出现较多，杀除未出现的
        if len(appeared_pent) >= 1:
            for pent in pentagonal_numbers:
                if pent not in appeared_pent:
                    kills.append(pent)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_binary_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """二进制杀号法 - 基于二进制表示"""
        kills = []
        all_numbers = period1 + period2

        # 统计二进制位数分布
        bit_counts = {}
        for num in all_numbers:
            bit_count = bin(num).count('1')  # 计算二进制中1的个数
            bit_counts[bit_count] = bit_counts.get(bit_count, 0) + 1

        # 找出出现最多的位数
        if bit_counts:
            max_bit_count = max(bit_counts, key=bit_counts.get)

            # 杀除具有相同位数的未出现号码
            for num in range(1, 36):
                if bin(num).count('1') == max_bit_count and num not in all_numbers:
                    kills.append(num)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_factorial_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """阶乘杀号法 - 基于阶乘数"""
        kills = []

        # 35以内的阶乘数
        factorial_numbers = [1, 2, 6, 24]  # 1!, 2!, 3!, 4!

        all_numbers = period1 + period2
        appeared_factorials = [f for f in factorial_numbers if f in all_numbers]

        # 如果阶乘数出现，杀除其他阶乘数
        if appeared_factorials:
            for factorial in factorial_numbers:
                if factorial not in appeared_factorials:
                    kills.append(factorial)

        return kills

    def _calculate_power_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """幂次杀号法 - 基于2的幂次"""
        kills = []

        # 35以内的2的幂次
        power_numbers = [1, 2, 4, 8, 16, 32]  # 2^0, 2^1, 2^2, 2^3, 2^4, 2^5

        all_numbers = period1 + period2
        appeared_powers = [p for p in power_numbers if p in all_numbers]

        # 如果幂次数出现过多，杀除未出现的
        if len(appeared_powers) >= 2:
            for power in power_numbers:
                if power not in appeared_powers:
                    kills.append(power)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_modular_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """模运算杀号法 - 基于模运算规律"""
        kills = []
        all_numbers = period1 + period2

        # 对多个模数进行分析
        for mod in [6, 7, 8, 9]:
            remainder_counts = [0] * mod
            for num in all_numbers:
                remainder_counts[num % mod] += 1

            # 找出出现最少的余数
            min_count = min(remainder_counts)
            min_remainders = [i for i, count in enumerate(remainder_counts) if count == min_count]

            # 杀除对应余数的号码
            for remainder in min_remainders:
                for num in range(remainder, 36, mod):
                    if num >= 1 and num not in all_numbers:
                        kills.append(num)
                        if len(kills) >= 2:
                            break
                if len(kills) >= 2:
                    break

            if len(kills) >= 2:
                break

        return kills

    def _calculate_gcd_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """最大公约数杀号法"""
        kills = []
        all_numbers = period1 + period2

        import math

        # 计算所有号码的最大公约数
        if len(all_numbers) >= 2:
            gcd_value = all_numbers[0]
            for num in all_numbers[1:]:
                gcd_value = math.gcd(gcd_value, num)

            # 基于GCD计算杀号
            if gcd_value > 1:
                # 杀除GCD的倍数
                for multiple in range(gcd_value, 36, gcd_value):
                    if multiple not in all_numbers:
                        kills.append(multiple)
                        if len(kills) >= 3:
                            break

        return kills

    def _calculate_lcm_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """最小公倍数杀号法"""
        kills = []
        all_numbers = period1 + period2

        import math

        # 计算前几个数的最小公倍数
        if len(all_numbers) >= 2:
            lcm_value = all_numbers[0]
            for num in all_numbers[1:3]:  # 只取前3个数避免LCM过大
                lcm_value = lcm_value * num // math.gcd(lcm_value, num)
                if lcm_value > 35:
                    break

            # 基于LCM计算杀号
            if lcm_value <= 35 and lcm_value not in all_numbers:
                kills.append(lcm_value)

        return kills

    def _calculate_palindrome_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """回文数杀号法"""
        kills = []

        # 35以内的回文数
        palindrome_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 22, 33]

        all_numbers = period1 + period2
        appeared_palindromes = [p for p in palindrome_numbers if p in all_numbers]

        # 如果回文数出现过多，杀除未出现的
        if len(appeared_palindromes) >= 3:
            for palindrome in palindrome_numbers:
                if palindrome not in appeared_palindromes:
                    kills.append(palindrome)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_perfect_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """完全数杀号法 - 等于其真因数之和的数"""
        kills = []

        # 35以内的完全数
        perfect_numbers = [6, 28]  # 6 = 1+2+3, 28 = 1+2+4+7+14

        all_numbers = period1 + period2

        # 如果完全数出现，杀除其他完全数
        for perfect in perfect_numbers:
            if perfect in all_numbers:
                for other_perfect in perfect_numbers:
                    if other_perfect != perfect and other_perfect not in all_numbers:
                        kills.append(other_perfect)

        return kills

    def _calculate_abundant_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """过剩数杀号法 - 真因数之和大于自身的数"""
        kills = []

        def sum_of_proper_divisors(n):
            return sum(i for i in range(1, n) if n % i == 0)

        # 35以内的过剩数
        abundant_numbers = []
        for num in range(1, 36):
            if sum_of_proper_divisors(num) > num:
                abundant_numbers.append(num)

        all_numbers = period1 + period2
        appeared_abundant = [a for a in abundant_numbers if a in all_numbers]

        # 如果过剩数出现过多，杀除未出现的
        if len(appeared_abundant) >= 2:
            for abundant in abundant_numbers:
                if abundant not in appeared_abundant:
                    kills.append(abundant)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_deficient_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """亏数杀号法 - 真因数之和小于自身的数"""
        kills = []

        def sum_of_proper_divisors(n):
            return sum(i for i in range(1, n) if n % i == 0)

        all_numbers = period1 + period2

        # 统计亏数的出现情况
        deficient_count = 0
        for num in all_numbers:
            if sum_of_proper_divisors(num) < num:
                deficient_count += 1

        # 如果亏数出现过多，杀除一些亏数
        if deficient_count >= 4:
            for num in range(1, 36):
                if (sum_of_proper_divisors(num) < num and
                    num not in all_numbers):
                    kills.append(num)
                    if len(kills) >= 3:
                        break

        return kills

    def _calculate_last_period_plus_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码+1杀号法 - 基于上期开奖号码加1"""
        kills = []
        for num in period1_red:
            kill_num = num + 1
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_minus_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码-1杀号法 - 基于上期开奖号码减1"""
        kills = []
        for num in period1_red:
            kill_num = num - 1
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_plus2_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码+2杀号法 - 基于上期开奖号码加2"""
        kills = []
        for num in period1_red:
            kill_num = num + 2
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_minus2_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码-2杀号法 - 基于上期开奖号码减2"""
        kills = []
        for num in period1_red:
            kill_num = num - 2
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_plus3_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码+3杀号法 - 基于上期开奖号码加3"""
        kills = []
        for num in period1_red:
            kill_num = num + 3
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_minus3_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码-3杀号法 - 基于上期开奖号码减3"""
        kills = []
        for num in period1_red:
            kill_num = num - 3
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_double_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码×2杀号法 - 基于上期开奖号码乘2"""
        kills = []
        for num in period1_red:
            kill_num = num * 2
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_half_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码÷2杀号法 - 基于上期开奖号码除2"""
        kills = []
        for num in period1_red:
            if num % 2 == 0:  # 只对偶数进行除2操作
                kill_num = num // 2
                if 1 <= kill_num <= 35:
                    kills.append(kill_num)
        return kills

    def _calculate_last_period_reverse_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码反向杀号法 - 基于36-上期号码"""
        kills = []
        for num in period1_red:
            kill_num = 36 - num
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_last_period_sum_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码和值杀号法 - 基于上期号码和值的变换"""
        kills = []
        total_sum = sum(period1_red)

        # 基于和值的多种变换
        kill_candidates = [
            total_sum % 35 + 1,           # 和值模35
            (total_sum // 10) % 35 + 1,   # 和值十位数
            (total_sum % 10) + 1,         # 和值个位数+1
            (total_sum // 5) % 35 + 1,    # 和值除5
            (total_sum * 2) % 35 + 1      # 和值乘2
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_last_period_diff_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码差值杀号法 - 基于上期号码间的差值"""
        kills = []

        # 计算相邻号码的差值
        sorted_nums = sorted(period1_red)
        for i in range(len(sorted_nums) - 1):
            diff = sorted_nums[i + 1] - sorted_nums[i]

            # 基于差值生成杀号
            for base_num in period1_red:
                kill_candidates = [base_num + diff, base_num - diff]
                for kill_num in kill_candidates:
                    if 1 <= kill_num <= 35 and kill_num not in period1_red:
                        kills.append(kill_num)

        return list(set(kills))  # 去重

    def _calculate_last_period_avg_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码平均值杀号法 - 基于上期号码平均值"""
        kills = []
        avg = sum(period1_red) / len(period1_red)

        # 基于平均值的变换
        kill_candidates = [
            int(avg),                    # 平均值取整
            int(avg) + 1,               # 平均值+1
            int(avg) - 1,               # 平均值-1
            int(avg * 2) % 35 + 1,      # 平均值×2
            int(avg / 2) if avg >= 2 else 1  # 平均值÷2
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_last_period_median_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码中位数杀号法 - 基于上期号码中位数"""
        kills = []
        sorted_nums = sorted(period1_red)
        n = len(sorted_nums)

        # 计算中位数
        if n % 2 == 1:
            median = sorted_nums[n // 2]
        else:
            median = (sorted_nums[n // 2 - 1] + sorted_nums[n // 2]) // 2

        # 基于中位数的变换
        kill_candidates = [
            median + 1,
            median - 1,
            median + 2,
            median - 2,
            median * 2 % 35 + 1
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_last_period_range_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码范围杀号法 - 基于上期号码的最大值和最小值"""
        kills = []
        min_num = min(period1_red)
        max_num = max(period1_red)
        range_val = max_num - min_num

        # 基于范围的变换
        kill_candidates = [
            min_num - 1,               # 最小值-1
            max_num + 1,               # 最大值+1
            min_num + range_val,       # 最小值+范围
            max_num - range_val,       # 最大值-范围
            (min_num + max_num) // 2   # 最大最小值的中点
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_last_period_pattern_kill(self, period1_red: List[int]) -> List[int]:
        """上期号码模式杀号法 - 基于上期号码的数字模式"""
        kills = []

        # 分析数字模式
        # 1. 尾数模式
        tail_digits = [num % 10 for num in period1_red]
        most_common_tail = max(set(tail_digits), key=tail_digits.count)

        # 杀除具有相同尾数的其他号码
        for num in range(1, 36):
            if num % 10 == most_common_tail and num not in period1_red:
                kills.append(num)

        # 2. 十位数模式
        ten_digits = [num // 10 for num in period1_red]
        most_common_ten = max(set(ten_digits), key=ten_digits.count)

        # 杀除具有相同十位数的其他号码
        for num in range(1, 36):
            if num // 10 == most_common_ten and num not in period1_red:
                kills.append(num)
                if len(kills) >= 8:  # 限制杀号数量
                    break

        return list(set(kills))  # 去重

    def _calculate_prev2_period_plus_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码+1杀号法 - 基于上上期开奖号码加1"""
        kills = []
        for num in period2_red:
            kill_num = num + 1
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_prev2_period_minus_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码-1杀号法 - 基于上上期开奖号码减1"""
        kills = []
        for num in period2_red:
            kill_num = num - 1
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_prev2_period_double_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码×2杀号法 - 基于上上期开奖号码乘2"""
        kills = []
        for num in period2_red:
            kill_num = num * 2
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_prev2_period_half_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码÷2杀号法 - 基于上上期开奖号码除2"""
        kills = []
        for num in period2_red:
            if num % 2 == 0:  # 只对偶数进行除2操作
                kill_num = num // 2
                if 1 <= kill_num <= 35:
                    kills.append(kill_num)
        return kills

    def _calculate_prev2_period_reverse_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码反向杀号法 - 基于36-上上期号码"""
        kills = []
        for num in period2_red:
            kill_num = 36 - num
            if 1 <= kill_num <= 35:
                kills.append(kill_num)
        return kills

    def _calculate_prev2_period_sum_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码和值杀号法 - 基于上上期号码和值的变换"""
        kills = []
        total_sum = sum(period2_red)

        # 基于和值的多种变换
        kill_candidates = [
            total_sum % 35 + 1,           # 和值模35
            (total_sum // 10) % 35 + 1,   # 和值十位数
            (total_sum % 10) + 1,         # 和值个位数+1
            (total_sum // 5) % 35 + 1,    # 和值除5
            (total_sum * 2) % 35 + 1      # 和值乘2
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_prev2_period_diff_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码差值杀号法 - 基于上上期号码间的差值"""
        kills = []

        # 计算相邻号码的差值
        sorted_nums = sorted(period2_red)
        for i in range(len(sorted_nums) - 1):
            diff = sorted_nums[i + 1] - sorted_nums[i]

            # 基于差值生成杀号
            for base_num in period2_red:
                kill_candidates = [base_num + diff, base_num - diff]
                for kill_num in kill_candidates:
                    if 1 <= kill_num <= 35 and kill_num not in period2_red:
                        kills.append(kill_num)

        return list(set(kills))  # 去重

    def _calculate_prev2_period_avg_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码平均值杀号法 - 基于上上期号码平均值"""
        kills = []
        avg = sum(period2_red) / len(period2_red)

        # 基于平均值的变换
        kill_candidates = [
            int(avg),                    # 平均值取整
            int(avg) + 1,               # 平均值+1
            int(avg) - 1,               # 平均值-1
            int(avg * 2) % 35 + 1,      # 平均值×2
            int(avg / 2) if avg >= 2 else 1  # 平均值÷2
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_prev2_period_median_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码中位数杀号法 - 基于上上期号码中位数"""
        kills = []
        sorted_nums = sorted(period2_red)
        n = len(sorted_nums)

        # 计算中位数
        if n % 2 == 1:
            median = sorted_nums[n // 2]
        else:
            median = (sorted_nums[n // 2 - 1] + sorted_nums[n // 2]) // 2

        # 基于中位数的变换
        kill_candidates = [
            median + 1,
            median - 1,
            median + 2,
            median - 2,
            median * 2 % 35 + 1
        ]

        for kill_num in kill_candidates:
            if 1 <= kill_num <= 35:
                kills.append(kill_num)

        return kills

    def _calculate_prev2_period_pattern_kill(self, period2_red: List[int]) -> List[int]:
        """上上期号码模式杀号法 - 基于上上期号码的数字模式"""
        kills = []

        # 分析数字模式
        # 1. 尾数模式
        tail_digits = [num % 10 for num in period2_red]
        most_common_tail = max(set(tail_digits), key=tail_digits.count)

        # 杀除具有相同尾数的其他号码
        for num in range(1, 36):
            if num % 10 == most_common_tail and num not in period2_red:
                kills.append(num)

        # 2. 十位数模式
        ten_digits = [num // 10 for num in period2_red]
        most_common_ten = max(set(ten_digits), key=ten_digits.count)

        # 杀除具有相同十位数的其他号码
        for num in range(1, 36):
            if num // 10 == most_common_ten and num not in period2_red:
                kills.append(num)
                if len(kills) >= 8:  # 限制杀号数量
                    break

        return list(set(kills))  # 去重

    def _calculate_weekday_kill(self, current_period) -> List[int]:
        """星期维度杀号法 - 基于开奖日期的星期"""
        kills = []

        # 模拟根据期号推算星期（实际应用中需要真实日期）
        period_num = int(str(current_period['期号'])[-3:])  # 取期号后三位
        weekday = period_num % 7  # 0-6代表周日到周六

        # 基于星期的杀号规律
        weekday_kills = {
            0: [7, 14, 21, 28, 35],    # 周日：7的倍数
            1: [1, 8, 15, 22, 29],     # 周一：1+7n
            2: [2, 9, 16, 23, 30],     # 周二：2+7n
            3: [3, 10, 17, 24, 31],    # 周三：3+7n
            4: [4, 11, 18, 25, 32],    # 周四：4+7n
            5: [5, 12, 19, 26, 33],    # 周五：5+7n
            6: [6, 13, 20, 27, 34]     # 周六：6+7n
        }

        # 选择对应星期的杀号（取前3个）
        if weekday in weekday_kills:
            kills = weekday_kills[weekday][:3]

        return kills

    def _calculate_month_kill(self, current_period) -> List[int]:
        """月份维度杀号法 - 基于开奖月份"""
        kills = []

        # 模拟根据期号推算月份
        period_num = int(str(current_period['期号'])[-3:])
        month = (period_num % 12) + 1  # 1-12月

        # 基于月份的杀号规律
        month_kills = {
            1: [1, 13, 25],    # 1月：1+12n
            2: [2, 14, 26],    # 2月：2+12n
            3: [3, 15, 27],    # 3月：3+12n
            4: [4, 16, 28],    # 4月：4+12n
            5: [5, 17, 29],    # 5月：5+12n
            6: [6, 18, 30],    # 6月：6+12n
            7: [7, 19, 31],    # 7月：7+12n
            8: [8, 20, 32],    # 8月：8+12n
            9: [9, 21, 33],    # 9月：9+12n
            10: [10, 22, 34],  # 10月：10+12n
            11: [11, 23, 35],  # 11月：11+12n
            12: [12, 24]       # 12月：12+12n
        }

        if month in month_kills:
            kills = month_kills[month]

        return kills

    def _calculate_season_kill(self, current_period) -> List[int]:
        """季节维度杀号法 - 基于四季"""
        kills = []

        # 模拟根据期号推算季节
        period_num = int(str(current_period['期号'])[-3:])
        season = period_num % 4  # 0-3代表四季

        # 基于季节的杀号规律
        season_kills = {
            0: [1, 5, 9, 13, 17],      # 春季：生机勃勃的数字
            1: [6, 10, 14, 18, 22],    # 夏季：炎热的数字
            2: [3, 7, 11, 15, 19],     # 秋季：收获的数字
            3: [4, 8, 12, 16, 20]      # 冬季：寒冷的数字
        }

        if season in season_kills:
            kills = season_kills[season][:3]  # 取前3个

        return kills

    def _calculate_lunar_kill(self, current_period) -> List[int]:
        """农历维度杀号法 - 基于农历概念"""
        kills = []

        # 模拟农历日期
        period_num = int(str(current_period['期号'])[-3:])
        lunar_day = (period_num % 30) + 1  # 1-30农历日

        # 基于农历的杀号规律
        if lunar_day <= 15:  # 上半月
            kills = [lunar_day, lunar_day + 15]
        else:  # 下半月
            kills = [lunar_day - 15, lunar_day]

        # 添加农历特殊日期的杀号
        special_days = {
            1: [1, 11, 21, 31],    # 初一
            15: [15, 25, 35],      # 十五
            8: [8, 18, 28],        # 初八
            23: [23, 33]           # 廿三
        }

        if lunar_day in special_days:
            kills.extend(special_days[lunar_day])

        return list(set(kills))[:4]  # 去重并限制数量

    def _calculate_zodiac_kill(self, current_period) -> List[int]:
        """生肖维度杀号法 - 基于十二生肖"""
        kills = []

        # 模拟生肖年份
        period_num = int(str(current_period['期号'])[-3:])
        zodiac = period_num % 12  # 0-11代表十二生肖

        # 生肖对应的杀号
        zodiac_kills = {
            0: [1, 13, 25],    # 鼠
            1: [2, 14, 26],    # 牛
            2: [3, 15, 27],    # 虎
            3: [4, 16, 28],    # 兔
            4: [5, 17, 29],    # 龙
            5: [6, 18, 30],    # 蛇
            6: [7, 19, 31],    # 马
            7: [8, 20, 32],    # 羊
            8: [9, 21, 33],    # 猴
            9: [10, 22, 34],   # 鸡
            10: [11, 23, 35],  # 狗
            11: [12, 24]       # 猪
        }

        if zodiac in zodiac_kills:
            kills = zodiac_kills[zodiac]

        return kills

    def _calculate_element_kill(self, current_period) -> List[int]:
        """五行维度杀号法 - 基于金木水火土"""
        kills = []

        # 模拟五行
        period_num = int(str(current_period['期号'])[-3:])
        element = period_num % 5  # 0-4代表五行

        # 五行对应的杀号
        element_kills = {
            0: [1, 6, 11, 16, 21, 26, 31],    # 金：1,6
            1: [2, 7, 12, 17, 22, 27, 32],    # 木：2,7
            2: [3, 8, 13, 18, 23, 28, 33],    # 水：3,8
            3: [4, 9, 14, 19, 24, 29, 34],    # 火：4,9
            4: [5, 10, 15, 20, 25, 30, 35]    # 土：5,0
        }

        if element in element_kills:
            kills = element_kills[element][:4]  # 取前4个

        return kills

    def _calculate_color_kill(self, current_period) -> List[int]:
        """颜色维度杀号法 - 基于颜色理论"""
        kills = []

        # 模拟颜色
        period_num = int(str(current_period['期号'])[-3:])
        color = period_num % 7  # 0-6代表七种颜色

        # 颜色对应的杀号（基于色彩心理学）
        color_kills = {
            0: [1, 8, 15, 22, 29],     # 红色：热情
            1: [2, 9, 16, 23, 30],     # 橙色：活力
            2: [3, 10, 17, 24, 31],    # 黄色：智慧
            3: [4, 11, 18, 25, 32],    # 绿色：自然
            4: [5, 12, 19, 26, 33],    # 蓝色：冷静
            5: [6, 13, 20, 27, 34],    # 靛色：神秘
            6: [7, 14, 21, 28, 35]     # 紫色：高贵
        }

        if color in color_kills:
            kills = color_kills[color][:3]

        return kills

    def _calculate_direction_kill(self, current_period) -> List[int]:
        """方位维度杀号法 - 基于八卦方位"""
        kills = []

        # 模拟方位
        period_num = int(str(current_period['期号'])[-3:])
        direction = period_num % 8  # 0-7代表八个方位

        # 方位对应的杀号
        direction_kills = {
            0: [1, 9, 17, 25, 33],     # 东：震卦
            1: [2, 10, 18, 26, 34],    # 东南：巽卦
            2: [3, 11, 19, 27, 35],    # 南：离卦
            3: [4, 12, 20, 28],        # 西南：坤卦
            4: [5, 13, 21, 29],        # 西：兑卦
            5: [6, 14, 22, 30],        # 西北：乾卦
            6: [7, 15, 23, 31],        # 北：坎卦
            7: [8, 16, 24, 32]         # 东北：艮卦
        }

        if direction in direction_kills:
            kills = direction_kills[direction][:3]

        return kills

    def _calculate_energy_kill(self, current_period) -> List[int]:
        """能量维度杀号法 - 基于能量频率"""
        kills = []

        # 模拟能量频率
        period_num = int(str(current_period['期号'])[-3:])
        energy_level = period_num % 10  # 0-9代表十个能量级别

        # 能量级别对应的杀号
        if energy_level <= 3:  # 低能量
            kills = [1, 2, 3, 4, 5]
        elif energy_level <= 6:  # 中能量
            kills = [11, 12, 13, 14, 15]
        else:  # 高能量
            kills = [31, 32, 33, 34, 35]

        return kills[:3]

    def _calculate_frequency_kill(self, current_period, period1_red: List[int], period2_red: List[int]) -> List[int]:
        """频率维度杀号法 - 基于号码出现频率"""
        kills = []

        # 统计最近两期的号码频率
        all_numbers = period1_red + period2_red
        frequency = {}
        for num in all_numbers:
            frequency[num] = frequency.get(num, 0) + 1

        # 找出高频号码进行杀号
        if frequency:
            max_freq = max(frequency.values())
            high_freq_numbers = [num for num, freq in frequency.items() if freq == max_freq]

            # 杀除高频号码
            kills = high_freq_numbers[:3]

        return kills

    def _calculate_temperature_kill(self, current_period) -> List[int]:
        """温度维度杀号法 - 基于温度概念"""
        kills = []

        # 模拟温度
        period_num = int(str(current_period['期号'])[-3:])
        temperature = period_num % 100  # 0-99度

        # 基于温度的杀号
        if temperature <= 0:  # 冰点以下
            kills = [1, 2, 3]
        elif temperature <= 20:  # 低温
            kills = [4, 5, 6, 7, 8]
        elif temperature <= 40:  # 适温
            kills = [15, 16, 17, 18, 19]
        elif temperature <= 60:  # 高温
            kills = [25, 26, 27, 28, 29]
        else:  # 极高温
            kills = [32, 33, 34, 35]

        return kills[:3]

    def _calculate_weather_kill(self, current_period) -> List[int]:
        """天气维度杀号法 - 基于天气类型"""
        kills = []

        # 模拟天气
        period_num = int(str(current_period['期号'])[-3:])
        weather = period_num % 6  # 0-5代表六种天气

        # 天气对应的杀号
        weather_kills = {
            0: [1, 7, 13, 19, 25],     # 晴天
            1: [2, 8, 14, 20, 26],     # 多云
            2: [3, 9, 15, 21, 27],     # 阴天
            3: [4, 10, 16, 22, 28],    # 小雨
            4: [5, 11, 17, 23, 29],    # 大雨
            5: [6, 12, 18, 24, 30]     # 雪天
        }

        if weather in weather_kills:
            kills = weather_kills[weather][:3]

        return kills

    def _calculate_emotion_kill(self, current_period) -> List[int]:
        """情感维度杀号法 - 基于情感状态"""
        kills = []

        # 模拟情感状态
        period_num = int(str(current_period['期号'])[-3:])
        emotion = period_num % 7  # 0-6代表七种情感

        # 情感对应的杀号
        emotion_kills = {
            0: [1, 8, 15, 22, 29],     # 喜悦
            1: [2, 9, 16, 23, 30],     # 愤怒
            2: [3, 10, 17, 24, 31],    # 悲伤
            3: [4, 11, 18, 25, 32],    # 恐惧
            4: [5, 12, 19, 26, 33],    # 惊讶
            5: [6, 13, 20, 27, 34],    # 厌恶
            6: [7, 14, 21, 28, 35]     # 平静
        }

        if emotion in emotion_kills:
            kills = emotion_kills[emotion][:3]

        return kills

    def _calculate_music_kill(self, current_period) -> List[int]:
        """音乐维度杀号法 - 基于音乐理论"""
        kills = []

        # 模拟音符
        period_num = int(str(current_period['期号'])[-3:])
        note = period_num % 7  # 0-6代表七个音符

        # 音符对应的杀号（基于音乐频率）
        note_kills = {
            0: [1, 8, 15, 22, 29],     # Do
            1: [2, 9, 16, 23, 30],     # Re
            2: [3, 10, 17, 24, 31],    # Mi
            3: [4, 11, 18, 25, 32],    # Fa
            4: [5, 12, 19, 26, 33],    # Sol
            5: [6, 13, 20, 27, 34],    # La
            6: [7, 14, 21, 28, 35]     # Si
        }

        if note in note_kills:
            kills = note_kills[note][:3]

        return kills

    def _calculate_art_kill(self, current_period) -> List[int]:
        """艺术维度杀号法 - 基于艺术美学"""
        kills = []

        # 模拟艺术风格
        period_num = int(str(current_period['期号'])[-3:])
        art_style = period_num % 5  # 0-4代表五种艺术风格

        # 艺术风格对应的杀号（基于黄金比例等美学原理）
        art_kills = {
            0: [1, 8, 13, 21, 34],     # 古典主义：黄金比例
            1: [2, 5, 11, 18, 29],     # 印象派：光影变化
            2: [3, 7, 12, 19, 31],     # 现代派：几何形状
            3: [4, 9, 14, 23, 32],     # 抽象派：自由形式
            4: [6, 10, 17, 27, 35]     # 超现实：梦幻组合
        }

        if art_style in art_kills:
            kills = art_kills[art_style][:3]

        return kills

    def _calculate_philosophy_kill(self, current_period) -> List[int]:
        """哲学维度杀号法 - 基于哲学思想"""
        kills = []

        # 模拟哲学流派
        period_num = int(str(current_period['期号'])[-3:])
        philosophy = period_num % 6  # 0-5代表六种哲学流派

        # 哲学流派对应的杀号
        philosophy_kills = {
            0: [1, 6, 11, 16, 21],     # 唯物主义：物质第一
            1: [2, 7, 12, 17, 22],     # 唯心主义：精神第一
            2: [3, 8, 13, 18, 23],     # 存在主义：存在先于本质
            3: [4, 9, 14, 19, 24],     # 实用主义：实践检验真理
            4: [5, 10, 15, 20, 25],    # 理性主义：理性认识
            5: [26, 27, 28, 29, 30]    # 经验主义：经验认识
        }

        if philosophy in philosophy_kills:
            kills = philosophy_kills[philosophy][:3]

        return kills

    def _calculate_psychology_kill(self, current_period) -> List[int]:
        """心理学维度杀号法 - 基于心理学原理"""
        kills = []

        # 模拟心理状态
        period_num = int(str(current_period['期号'])[-3:])
        psychology = period_num % 8  # 0-7代表八种心理状态

        # 心理状态对应的杀号（基于心理学理论）
        psychology_kills = {
            0: [1, 9, 17, 25, 33],     # 意识：清醒状态
            1: [2, 10, 18, 26, 34],    # 潜意识：隐藏状态
            2: [3, 11, 19, 27, 35],    # 本我：原始冲动
            3: [4, 12, 20, 28],        # 自我：现实原则
            4: [5, 13, 21, 29],        # 超我：道德原则
            5: [6, 14, 22, 30],        # 外向：社交倾向
            6: [7, 15, 23, 31],        # 内向：独处倾向
            7: [8, 16, 24, 32]         # 平衡：中庸状态
        }

        if psychology in psychology_kills:
            kills = psychology_kills[psychology][:3]

        return kills

    def _calculate_sociology_kill(self, current_period) -> List[int]:
        """社会学维度杀号法 - 基于社会学理论"""
        kills = []

        # 模拟社会层次
        period_num = int(str(current_period['期号'])[-3:])
        social_class = period_num % 5  # 0-4代表五个社会层次

        # 社会层次对应的杀号
        social_kills = {
            0: [1, 2, 3, 4, 5],        # 底层：基础数字
            1: [6, 7, 8, 9, 10],       # 中下层：过渡数字
            2: [11, 12, 13, 14, 15],   # 中层：中间数字
            3: [16, 17, 18, 19, 20],   # 中上层：上升数字
            4: [31, 32, 33, 34, 35]    # 上层：顶端数字
        }

        if social_class in social_kills:
            kills = social_kills[social_class][:3]

        return kills

    def _calculate_economics_kill(self, current_period) -> List[int]:
        """经济学维度杀号法 - 基于经济学原理"""
        kills = []

        # 模拟经济周期
        period_num = int(str(current_period['期号'])[-3:])
        economic_cycle = period_num % 4  # 0-3代表四个经济周期

        # 经济周期对应的杀号
        economic_kills = {
            0: [1, 5, 9, 13, 17],      # 复苏期：上升趋势
            1: [21, 25, 29, 33],       # 繁荣期：高位数字
            2: [18, 22, 26, 30, 34],   # 衰退期：下降趋势
            3: [2, 6, 10, 14]          # 萧条期：低位数字
        }

        if economic_cycle in economic_kills:
            kills = economic_kills[economic_cycle][:3]

        return kills

    def _calculate_physics_kill(self, current_period) -> List[int]:
        """物理学维度杀号法 - 基于物理学原理"""
        kills = []

        # 模拟物理常数
        period_num = int(str(current_period['期号'])[-3:])
        physics_constant = period_num % 7  # 0-6代表七个物理常数

        # 物理常数对应的杀号
        physics_kills = {
            0: [3, 14, 15, 26, 35],    # π (圆周率)
            1: [2, 7, 18, 28],         # e (自然常数)
            2: [1, 6, 18, 33],         # φ (黄金比例)
            3: [6, 12, 24],            # 光速相关
            4: [9, 18, 27],            # 重力相关
            5: [4, 8, 16, 32],         # 量子相关
            6: [5, 10, 20, 25, 30]     # 电磁相关
        }

        if physics_constant in physics_kills:
            kills = physics_kills[physics_constant][:3]

        return kills

    def _calculate_blue_prime_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球质数杀号法"""
        kills = []

        # 12以内的质数
        blue_primes = [2, 3, 5, 7, 11]
        all_blue = period1_blue + period2_blue

        # 统计出现的质数
        appeared_primes = [p for p in blue_primes if p in all_blue]

        # 如果质数出现较多，杀除未出现的质数
        if len(appeared_primes) >= 2:
            for prime in blue_primes:
                if prime not in appeared_primes:
                    kills.append(prime)

        return kills

    def _calculate_blue_fib_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球斐波那契杀号法"""
        kills = []

        # 12以内的斐波那契数
        blue_fib = [1, 2, 3, 5, 8]
        all_blue = period1_blue + period2_blue

        # 计算特征值
        blue_sum = sum(all_blue)

        # 基于特征值选择斐波那契数杀号
        fib_index = blue_sum % len(blue_fib)
        kills.append(blue_fib[fib_index])

        return kills

    def _calculate_blue_digital_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球数字根杀号法"""
        kills = []

        def digital_root(n):
            while n >= 10:
                n = sum(int(digit) for digit in str(n))
            return n

        all_blue = period1_blue + period2_blue

        # 统计数字根分布
        root_counts = [0] * 10
        for num in all_blue:
            root = digital_root(num)
            root_counts[root] += 1

        # 找出出现最多的数字根
        max_count = max(root_counts)
        if max_count >= 2:
            max_root = root_counts.index(max_count)

            # 杀除该数字根对应的未出现蓝球
            for num in range(1, 13):
                if digital_root(num) == max_root and num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_interval_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球区间杀号法"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 将1-12分为3个区间
        intervals = [(1, 4), (5, 8), (9, 12)]

        # 统计每个区间的蓝球数量
        interval_counts = []
        for start, end in intervals:
            count = sum(1 for num in all_blue if start <= num <= end)
            interval_counts.append(count)

        # 找出蓝球最多的区间
        max_count = max(interval_counts)
        if max_count >= 2:
            max_interval_idx = interval_counts.index(max_count)
            start, end = intervals[max_interval_idx]

            # 杀除该区间中未出现的蓝球
            for num in range(start, end + 1):
                if num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_square_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球平方数杀号法"""
        kills = []

        # 12以内的平方数
        blue_squares = [1, 4, 9]  # 1^2, 2^2, 3^2
        all_blue = period1_blue + period2_blue

        appeared_squares = [s for s in blue_squares if s in all_blue]

        # 如果平方数出现，杀除其他平方数
        if appeared_squares:
            for square in blue_squares:
                if square not in appeared_squares:
                    kills.append(square)

        return kills

    def _calculate_blue_triangle_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球三角数杀号法"""
        kills = []

        # 12以内的三角数
        blue_triangles = [1, 3, 6, 10]  # T(1), T(2), T(3), T(4)
        all_blue = period1_blue + period2_blue

        appeared_triangles = [t for t in blue_triangles if t in all_blue]

        # 如果三角数出现过多，杀除未出现的
        if len(appeared_triangles) >= 2:
            for triangle in blue_triangles:
                if triangle not in appeared_triangles:
                    kills.append(triangle)

        return kills

    def _calculate_blue_lucas_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球卢卡斯数杀号法"""
        kills = []

        # 12以内的卢卡斯数
        blue_lucas = [2, 1, 3, 4, 7, 11]
        all_blue = period1_blue + period2_blue

        # 计算特征值
        blue_sum = sum(all_blue)

        # 基于特征值选择卢卡斯数
        lucas_index = blue_sum % len(blue_lucas)
        if blue_lucas[lucas_index] not in all_blue:
            kills.append(blue_lucas[lucas_index])

        return kills

    def _calculate_blue_perfect_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球完全数杀号法"""
        kills = []

        # 12以内的完全数
        blue_perfect = [6]  # 6 = 1+2+3
        all_blue = period1_blue + period2_blue

        # 如果完全数出现，根据规律杀号
        if 6 in all_blue:
            # 杀除与6相关的数字
            related_numbers = [3, 12]  # 6的一半和两倍
            for num in related_numbers:
                if num not in all_blue:
                    kills.append(num)

        return kills

    def _calculate_blue_binary_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球二进制杀号法"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 统计二进制位数分布
        bit_counts = {}
        for num in all_blue:
            bit_count = bin(num).count('1')
            bit_counts[bit_count] = bit_counts.get(bit_count, 0) + 1

        # 找出出现最多的位数
        if bit_counts:
            max_bit_count = max(bit_counts, key=bit_counts.get)

            # 杀除具有相同位数的未出现蓝球
            for num in range(1, 13):
                if bin(num).count('1') == max_bit_count and num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_modular_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球模运算杀号法"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 对模3和模4进行分析
        for mod in [3, 4]:
            remainder_counts = [0] * mod
            for num in all_blue:
                remainder_counts[num % mod] += 1

            # 找出出现最多的余数
            max_count = max(remainder_counts)
            max_remainder = remainder_counts.index(max_count)

            # 杀除该余数对应的未出现蓝球
            for num in range(1, 13):
                if num % mod == max_remainder and num not in all_blue:
                    kills.append(num)
                    if len(kills) >= 1:
                        break

            if kills:
                break

        return kills

    def _calculate_blue_palindrome_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球回文数杀号法"""
        kills = []

        # 12以内的回文数
        blue_palindromes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 11]
        all_blue = period1_blue + period2_blue

        appeared_palindromes = [p for p in blue_palindromes if p in all_blue]

        # 如果回文数出现过多，杀除未出现的
        if len(appeared_palindromes) >= 2:
            for palindrome in blue_palindromes:
                if palindrome not in appeared_palindromes:
                    kills.append(palindrome)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_power_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球幂次杀号法"""
        kills = []

        # 12以内的2的幂次
        blue_powers = [1, 2, 4, 8]  # 2^0, 2^1, 2^2, 2^3
        all_blue = period1_blue + period2_blue

        appeared_powers = [p for p in blue_powers if p in all_blue]

        # 如果幂次数出现，杀除其他幂次数
        if appeared_powers:
            for power in blue_powers:
                if power not in appeared_powers:
                    kills.append(power)
                    if len(kills) >= 2:
                        break

        return kills

    def _calculate_blue_ultra_conservative_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球超保守杀号法 - 只在绝对安全时杀号"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 超保守策略：只杀连续3次以上出现的号码
        if len(set(all_blue)) == 1:  # 两期都是同一个号码
            repeated_num = all_blue[0]
            # 杀除这个重复号码，因为连续出现3次的概率极低
            kills.append(repeated_num)

        return kills

    def _calculate_blue_pattern_break_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球模式破坏法 - 基于模式破坏理论"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 检查是否存在明显的递增或递减模式
        if len(period1_blue) == 1 and len(period2_blue) == 1:
            blue1, blue2 = period2_blue[0], period1_blue[0]  # 注意顺序：上二期，上一期

            # 如果存在递增模式
            if blue2 == blue1 + 1:
                next_in_sequence = blue2 + 1
                if 1 <= next_in_sequence <= 12:
                    kills.append(next_in_sequence)

            # 如果存在递减模式
            elif blue2 == blue1 - 1:
                next_in_sequence = blue2 - 1
                if 1 <= next_in_sequence <= 12:
                    kills.append(next_in_sequence)

            # 如果存在倍数关系
            elif blue1 > 0 and blue2 % blue1 == 0 and blue2 != blue1:
                # 杀除下一个倍数
                next_multiple = blue2 + blue1
                if 1 <= next_multiple <= 12:
                    kills.append(next_multiple)

        return kills

    def _calculate_blue_frequency_extreme_kill(self, period1_blue: List[int], period2_blue: List[int],
                                              historical_data: pd.DataFrame = None) -> List[int]:
        """蓝球频率极值法 - 基于历史频率的极端值"""
        kills = []

        if historical_data is None:
            return kills

        # 更新历史统计
        self.blue_killer._update_blue_historical_stats(historical_data)

        if not self.blue_killer.historical_stats:
            return kills

        blue_freq = self.blue_killer.historical_stats['blue_freq']
        all_blue = period1_blue + period2_blue

        # 找出频率最高的号码
        max_freq = max(blue_freq.values()) if blue_freq else 0
        most_frequent = [num for num, freq in blue_freq.items() if freq == max_freq]

        # 如果最频繁的号码在最近两期都出现了，杀除它
        for num in most_frequent:
            if num in all_blue and max_freq >= 8:  # 频率很高且最近出现
                kills.append(num)
                break  # 只杀一个

        return kills

    def _calculate_blue_distance_max_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球最大距离法 - 基于号码间最大距离"""
        kills = []
        all_blue = period1_blue + period2_blue

        if len(all_blue) >= 2:
            # 计算所有号码间的距离
            distances = []
            for i in range(len(all_blue)):
                for j in range(i + 1, len(all_blue)):
                    distances.append(abs(all_blue[i] - all_blue[j]))

            if distances:
                max_distance = max(distances)

                # 如果最大距离很大（≥8），杀除距离中点的号码
                if max_distance >= 8:
                    # 找出产生最大距离的两个号码
                    for i in range(len(all_blue)):
                        for j in range(i + 1, len(all_blue)):
                            if abs(all_blue[i] - all_blue[j]) == max_distance:
                                midpoint = (all_blue[i] + all_blue[j]) // 2
                                if 1 <= midpoint <= 12 and midpoint not in all_blue:
                                    kills.append(midpoint)
                                    return kills

        return kills

    def _calculate_blue_composite_ultra_kill(self, period1_blue: List[int], period2_blue: List[int],
                                            historical_data: pd.DataFrame = None) -> List[int]:
        """蓝球超级复合法 - 多种方法的超保守组合"""
        kills = []
        all_blue = period1_blue + period2_blue

        # 方法1：检查重复
        repeated = set(period1_blue) & set(period2_blue)

        # 方法2：检查历史超热门
        ultra_hot = []
        if historical_data is not None:
            self.blue_killer._update_blue_historical_stats(historical_data)
            if self.blue_killer.historical_stats:
                blue_freq = self.blue_killer.historical_stats['blue_freq']
                max_freq = max(blue_freq.values()) if blue_freq else 0
                ultra_hot = [num for num, freq in blue_freq.items() if freq >= max_freq - 1]

        # 方法3：检查边界聚集
        boundary_cluster = sum(1 for num in all_blue if num <= 3 or num >= 10)

        # 超保守决策：只有当多个条件同时满足时才杀号
        if repeated and len(repeated) == 1:
            repeated_num = list(repeated)[0]
            if repeated_num in ultra_hot and boundary_cluster >= 1:
                kills.append(repeated_num)

        return kills

    def _calculate_blue_mathematical_pure_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球纯数学法 - 基于纯数学关系"""
        kills = []
        all_blue = period1_blue + period2_blue

        if len(all_blue) >= 2:
            # 计算数学特征
            blue_sum = sum(all_blue)
            blue_product = 1
            for num in all_blue:
                blue_product *= num

            # 基于数学常数的杀号
            # 使用欧拉数e ≈ 2.718
            euler_based = int(blue_sum * 2.718) % 12 + 1

            # 使用圆周率π ≈ 3.14159
            pi_based = int(blue_product * 3.14159) % 12 + 1

            # 使用黄金比例φ ≈ 1.618
            golden_based = int(blue_sum * 1.618) % 12 + 1

            # 只有当计算结果不在最近出现的号码中时才杀号
            candidates = [euler_based, pi_based, golden_based]
            for candidate in candidates:
                if candidate not in all_blue and 1 <= candidate <= 12:
                    kills.append(candidate)
                    break  # 只取一个最数学的

        return kills

    def _calculate_blue_statistical_ultra_kill(self, period1_blue: List[int], period2_blue: List[int],
                                              historical_data: pd.DataFrame = None) -> List[int]:
        """蓝球超级统计法 - 基于超级统计分析"""
        kills = []

        if historical_data is None:
            return kills

        self.blue_killer._update_blue_historical_stats(historical_data)

        if not self.blue_killer.historical_stats:
            return kills

        blue_freq = self.blue_killer.historical_stats['blue_freq']
        all_blue = period1_blue + period2_blue

        # 计算统计偏差
        total_periods = self.blue_killer.historical_stats['total_periods']
        expected_freq = total_periods / 12  # 理论平均频率

        # 找出严重偏离理论频率的号码
        extreme_deviations = []
        for num in range(1, 13):
            actual_freq = blue_freq.get(num, 0)
            deviation = abs(actual_freq - expected_freq)
            if deviation >= expected_freq * 0.5:  # 偏差超过50%
                extreme_deviations.append((num, deviation, actual_freq))

        # 杀除最近出现且严重超频的号码
        extreme_deviations.sort(key=lambda x: x[1], reverse=True)
        for num, deviation, freq in extreme_deviations:
            if num in all_blue and freq > expected_freq:  # 超频且最近出现
                kills.append(num)
                break

        return kills

    def _calculate_blue_harmonic_advanced_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球高级调和法 - 基于调和级数的高级应用"""
        kills = []
        all_blue = period1_blue + period2_blue

        if len(all_blue) >= 2:
            # 计算调和平均数
            harmonic_mean = len(all_blue) / sum(1.0 / num for num in all_blue if num > 0)

            # 基于调和平均数的杀号
            harmonic_kill = round(harmonic_mean)
            if 1 <= harmonic_kill <= 12 and harmonic_kill not in all_blue:
                kills.append(harmonic_kill)

            # 调和级数的倒数
            harmonic_reciprocal = round(1.0 / harmonic_mean * 12)
            if 1 <= harmonic_reciprocal <= 12 and harmonic_reciprocal not in all_blue and harmonic_reciprocal != harmonic_kill:
                kills.append(harmonic_reciprocal)

        return kills

    def _calculate_blue_golden_ratio_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球黄金比例法 - 基于黄金比例φ ≈ 1.618"""
        kills = []
        all_blue = period1_blue + period2_blue

        if len(all_blue) >= 1:
            phi = 1.618033988749  # 黄金比例

            for num in all_blue:
                # 基于黄金比例的变换
                golden_transform1 = round(num * phi) % 12
                if golden_transform1 == 0:
                    golden_transform1 = 12

                golden_transform2 = round(num / phi) % 12
                if golden_transform2 == 0:
                    golden_transform2 = 12

                # 选择不在最近出现中的变换结果
                candidates = [golden_transform1, golden_transform2]
                for candidate in candidates:
                    if candidate not in all_blue and 1 <= candidate <= 12:
                        kills.append(candidate)
                        return kills  # 只取一个最黄金的

        return kills

    def _calculate_blue_chaos_theory_kill(self, period1_blue: List[int], period2_blue: List[int]) -> List[int]:
        """蓝球混沌理论法 - 基于混沌数学理论"""
        kills = []
        all_blue = period1_blue + period2_blue

        if len(all_blue) >= 2:
            # 使用Logistic映射 x(n+1) = r * x(n) * (1 - x(n))
            # 其中r = 3.9（混沌参数）
            r = 3.9

            # 将蓝球号码归一化到[0,1]区间
            normalized = [num / 12.0 for num in all_blue]

            # 应用混沌映射
            chaos_results = []
            for x in normalized:
                chaos_x = r * x * (1 - x)
                # 将结果映射回[1,12]
                chaos_num = round(chaos_x * 12)
                if chaos_num == 0:
                    chaos_num = 1
                elif chaos_num > 12:
                    chaos_num = 12
                chaos_results.append(chaos_num)

            # 选择混沌结果中不在最近出现的号码
            for chaos_num in chaos_results:
                if chaos_num not in all_blue:
                    kills.append(chaos_num)
                    break  # 只取一个混沌结果

        return kills

    def print_blue_results(self, results: Dict):
        """打印蓝球测试结果"""
        print(f"\n📊 蓝球杀号算法测试结果 (共{results['total_periods']}期)")
        print("=" * 80)

        # 算法成功率排序
        algo_rates = [(algo, data['success_rate']) for algo, data in results['algorithm_results'].items()]
        algo_rates.sort(key=lambda x: x[1], reverse=True)

        print("🏆 算法成功率排行:")
        for i, (algo, rate) in enumerate(algo_rates, 1):
            data = results['algorithm_results'][algo]
            status = "🎯" if rate >= 0.8 else "⚠️" if rate >= 0.6 else "❌"
            print(f"  {i}. {algo:15} {rate:6.1%} ({data['successful_kills']}/{data['total_kills']}) {status}")

        # 详细分析
        print(f"\n📋 详细分析:")
        for algo, data in results['algorithm_results'].items():
            if data['total_kills'] > 0:
                print(f"\n{algo} 算法:")
                print(f"  总杀号数: {data['total_kills']}")
                print(f"  成功杀号: {data['successful_kills']}")
                print(f"  成功率: {data['success_rate']:.1%}")

                # 显示最近几期的详细情况
                print("  最近5期详情:")
                for detail in data['details'][:5]:
                    kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
                    success_str = ','.join(map(str, detail['successful'])) if detail['successful'] else '无'
                    actual_str = ','.join(map(str, detail['actual']))
                    print(f"    {detail['period']}: 杀号[{kills_str}] 成功[{success_str}] 实际[{actual_str}]")

    def test_ultra_conservative_strategies(self, test_periods: int = 20):
        """测试超保守组合策略 - 每期至少杀5个号码且全中"""
        print(f"\n🔍 测试超保守组合策略 (最近{test_periods}期)")
        print("要求：每期至少杀5个号码，且必须全中")
        print("=" * 80)

        # 红球超保守策略组合
        red_strategies = {
            'ultra_conservative_red': {
                'algorithms': ['pentagonal_kill', 'abundant_kill', 'factorial_kill', 'catalan_kill', 'span'],
                'min_votes': 4,  # 需要4个算法支持
                'min_kills': 5,  # 最少杀5个
                'max_kills': 8   # 最多杀8个
            },
            'high_precision_red': {
                'algorithms': ['pentagonal_kill', 'abundant_kill', 'factorial_kill', 'span', 'odd_even', 'interval_kill'],
                'min_votes': 3,  # 需要3个算法支持
                'min_kills': 5,  # 最少杀5个
                'max_kills': 10  # 最多杀10个
            },
            'mathematical_red': {
                'algorithms': ['pentagonal_kill', 'abundant_kill', 'catalan_kill', 'fibonacci', 'binary_kill', 'modular_kill'],
                'min_votes': 3,  # 需要3个算法支持
                'min_kills': 5,  # 最少杀5个
                'max_kills': 12  # 最多杀12个
            }
        }

        # 蓝球超保守策略组合
        blue_strategies = {
            'ultra_conservative_blue': {
                'algorithms': ['blue_perfect', 'blue_distance_max', 'blue_modular', 'blue_mathematical_pure'],
                'min_votes': 3,  # 需要3个算法支持
                'min_kills': 2,  # 最少杀2个
                'max_kills': 4   # 最多杀4个
            },
            'high_precision_blue': {
                'algorithms': ['blue_perfect', 'blue_modular', 'blue_binary', 'blue_square', 'blue_lucas'],
                'min_votes': 2,  # 需要2个算法支持
                'min_kills': 2,  # 最少杀2个
                'max_kills': 5   # 最多杀5个
            },
            'mathematical_blue': {
                'algorithms': ['blue_mathematical_pure', 'blue_distance_max', 'blue_golden_ratio', 'blue_fib', 'blue_lucas'],
                'min_votes': 2,  # 需要2个算法支持
                'min_kills': 2,  # 最少杀2个
                'max_kills': 6   # 最多杀6个
            }
        }

        return self._test_ultra_strategies(red_strategies, blue_strategies, test_periods)

    def _test_ultra_strategies(self, red_strategies: Dict, blue_strategies: Dict, test_periods: int):
        """执行超保守策略测试"""
        results = {}

        # 测试红球策略
        print("🔴 红球超保守策略测试:")
        print("-" * 60)

        for strategy_name, params in red_strategies.items():
            print(f"\n测试 {strategy_name} 策略:")
            print(f"  算法组合: {params['algorithms']}")
            print(f"  要求: 最少{params['min_votes']}票支持，杀{params['min_kills']}-{params['max_kills']}个号码")

            strategy_results = self._test_red_ultra_strategy(strategy_name, params, test_periods)
            results[f"red_{strategy_name}"] = strategy_results

        # 测试蓝球策略
        print("\n🔵 蓝球超保守策略测试:")
        print("-" * 60)

        for strategy_name, params in blue_strategies.items():
            print(f"\n测试 {strategy_name} 策略:")
            print(f"  算法组合: {params['algorithms']}")
            print(f"  要求: 最少{params['min_votes']}票支持，杀{params['min_kills']}-{params['max_kills']}个号码")

            strategy_results = self._test_blue_ultra_strategy(strategy_name, params, test_periods)
            results[f"blue_{strategy_name}"] = strategy_results

        return results

    def _test_red_ultra_strategy(self, strategy_name: str, params: Dict, test_periods: int):
        """测试红球超保守策略"""
        total_periods = 0
        successful_periods = 0
        total_kills = 0
        successful_kills = 0
        strategy_details = []

        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break

            # 获取数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]
            period2_data = self.data.iloc[i + 2]

            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)

            # 收集指定算法的投票
            algorithm_votes = {}

            for algo_name in params['algorithms']:
                if algo_name == 'pentagonal_kill':
                    kills = self._calculate_pentagonal_kill(period1_red, period2_red)
                elif algo_name == 'abundant_kill':
                    kills = self._calculate_abundant_kill(period1_red, period2_red)
                elif algo_name == 'factorial_kill':
                    kills = self._calculate_factorial_kill(period1_red, period2_red)
                elif algo_name == 'catalan_kill':
                    kills = self._calculate_catalan_kill(period1_red, period2_red)
                elif algo_name == 'span':
                    kills = self.red_killer._calculate_span_kill(period1_red, period2_red)
                elif algo_name == 'odd_even':
                    kills = self.red_killer._calculate_odd_even_kill(period1_red, period2_red)
                elif algo_name == 'interval_kill':
                    kills = self._calculate_interval_kill(period1_red, period2_red)
                elif algo_name == 'fibonacci':
                    kills = self._calculate_fibonacci_kill(period1_red, period2_red)
                elif algo_name == 'binary_kill':
                    kills = self._calculate_binary_kill(period1_red, period2_red)
                elif algo_name == 'modular_kill':
                    kills = self._calculate_modular_kill(period1_red, period2_red)
                else:
                    kills = []

                for kill in kills:
                    if 1 <= kill <= 35 and kill not in (period1_red + period2_red):
                        if kill not in algorithm_votes:
                            algorithm_votes[kill] = []
                        algorithm_votes[kill].append(algo_name)

            # 根据策略筛选杀号
            strategy_kills = []
            for num, votes in algorithm_votes.items():
                if len(votes) >= params['min_votes']:
                    strategy_kills.append((num, len(votes)))

            # 按票数排序，取符合数量要求的杀号
            strategy_kills.sort(key=lambda x: x[1], reverse=True)
            final_kills = [num for num, votes in strategy_kills]

            # 检查是否满足最少杀号数量要求
            if len(final_kills) < params['min_kills']:
                continue  # 跳过不满足最少杀号要求的期数

            # 限制最大杀号数量
            final_kills = final_kills[:params['max_kills']]

            # 验证效果
            successful = [k for k in final_kills if k not in current_red]
            is_perfect = len(successful) == len(final_kills)  # 全中

            period_detail = {
                'period': current_period['期号'],
                'kills': final_kills,
                'successful': successful,
                'actual': current_red,
                'is_perfect': is_perfect,
                'success_rate': len(successful) / len(final_kills) if final_kills else 0
            }

            strategy_details.append(period_detail)
            total_periods += 1
            total_kills += len(final_kills)
            successful_kills += len(successful)

            if is_perfect:
                successful_periods += 1

            # 打印详情
            kills_str = ','.join(map(str, final_kills)) if final_kills else '无'
            success_str = ','.join(map(str, successful)) if successful else '无'
            actual_str = ','.join(map(str, current_red))
            status = "✅" if is_perfect else "❌"
            print(f"    {current_period['期号']}: 杀号[{kills_str}] 成功[{success_str}] 全中率{period_detail['success_rate']:.1%} {status}")

        # 策略总结
        perfect_rate = successful_periods / total_periods if total_periods > 0 else 0
        overall_success_rate = successful_kills / total_kills if total_kills > 0 else 0

        strategy_result = {
            'total_periods': total_periods,
            'successful_periods': successful_periods,
            'perfect_rate': perfect_rate,
            'total_kills': total_kills,
            'successful_kills': successful_kills,
            'overall_success_rate': overall_success_rate,
            'details': strategy_details
        }

        print(f"  📊 {strategy_name} 策略总结:")
        print(f"    有效期数: {total_periods} (满足最少{params['min_kills']}个杀号)")
        print(f"    全中期数: {successful_periods}")
        print(f"    全中率: {perfect_rate:.1%}")
        print(f"    总杀号: {total_kills}")
        print(f"    成功杀号: {successful_kills}")
        print(f"    整体成功率: {overall_success_rate:.1%}")

        return strategy_result

    def _test_blue_ultra_strategy(self, strategy_name: str, params: Dict, test_periods: int):
        """测试蓝球超保守策略"""
        total_periods = 0
        successful_periods = 0
        total_kills = 0
        successful_kills = 0
        strategy_details = []

        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break

            # 获取数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]
            period2_data = self.data.iloc[i + 2]

            _, current_blue = parse_numbers(current_period)
            _, period1_blue = parse_numbers(period1_data)
            _, period2_blue = parse_numbers(period2_data)

            # 收集指定算法的投票
            algorithm_votes = {}
            historical_data = self.data

            for algo_name in params['algorithms']:
                if algo_name == 'blue_perfect':
                    kills = self._calculate_blue_perfect_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_distance_max':
                    kills = self._calculate_blue_distance_max_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_modular':
                    kills = self._calculate_blue_modular_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_mathematical_pure':
                    kills = self._calculate_blue_mathematical_pure_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_binary':
                    kills = self._calculate_blue_binary_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_square':
                    kills = self._calculate_blue_square_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_lucas':
                    kills = self._calculate_blue_lucas_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_golden_ratio':
                    kills = self._calculate_blue_golden_ratio_kill(period1_blue, period2_blue)
                elif algo_name == 'blue_fib':
                    kills = self._calculate_blue_fib_kill(period1_blue, period2_blue)
                else:
                    kills = []

                for kill in kills:
                    if 1 <= kill <= 12 and kill not in (period1_blue + period2_blue):
                        if kill not in algorithm_votes:
                            algorithm_votes[kill] = []
                        algorithm_votes[kill].append(algo_name)

            # 根据策略筛选杀号
            strategy_kills = []
            for num, votes in algorithm_votes.items():
                if len(votes) >= params['min_votes']:
                    strategy_kills.append((num, len(votes)))

            # 按票数排序，取符合数量要求的杀号
            strategy_kills.sort(key=lambda x: x[1], reverse=True)
            final_kills = [num for num, votes in strategy_kills]

            # 检查是否满足最少杀号数量要求
            if len(final_kills) < params['min_kills']:
                continue  # 跳过不满足最少杀号要求的期数

            # 限制最大杀号数量
            final_kills = final_kills[:params['max_kills']]

            # 验证效果
            successful = [k for k in final_kills if k not in current_blue]
            is_perfect = len(successful) == len(final_kills)  # 全中

            period_detail = {
                'period': current_period['期号'],
                'kills': final_kills,
                'successful': successful,
                'actual': current_blue,
                'is_perfect': is_perfect,
                'success_rate': len(successful) / len(final_kills) if final_kills else 0
            }

            strategy_details.append(period_detail)
            total_periods += 1
            total_kills += len(final_kills)
            successful_kills += len(successful)

            if is_perfect:
                successful_periods += 1

            # 打印详情
            kills_str = ','.join(map(str, final_kills)) if final_kills else '无'
            success_str = ','.join(map(str, successful)) if successful else '无'
            actual_str = ','.join(map(str, current_blue))
            status = "✅" if is_perfect else "❌"
            print(f"    {current_period['期号']}: 杀号[{kills_str}] 成功[{success_str}] 全中率{period_detail['success_rate']:.1%} {status}")

        # 策略总结
        perfect_rate = successful_periods / total_periods if total_periods > 0 else 0
        overall_success_rate = successful_kills / total_kills if total_kills > 0 else 0

        strategy_result = {
            'total_periods': total_periods,
            'successful_periods': successful_periods,
            'perfect_rate': perfect_rate,
            'total_kills': total_kills,
            'successful_kills': successful_kills,
            'overall_success_rate': overall_success_rate,
            'details': strategy_details
        }

        print(f"  📊 {strategy_name} 策略总结:")
        print(f"    有效期数: {total_periods} (满足最少{params['min_kills']}个杀号)")
        print(f"    全中期数: {successful_periods}")
        print(f"    全中率: {perfect_rate:.1%}")
        print(f"    总杀号: {total_kills}")
        print(f"    成功杀号: {successful_kills}")
        print(f"    整体成功率: {overall_success_rate:.1%}")

        return strategy_result

    def test_combined_strategies(self, test_periods: int = 20):
        """测试组合杀号策略"""
        print(f"\n🔍 测试组合杀号策略 (最近{test_periods}期)")
        print("=" * 60)

        strategies = {
            'conservative': {'min_votes': 3, 'max_kills': 2},  # 保守策略：需要3个算法支持，最多杀2个
            'moderate': {'min_votes': 2, 'max_kills': 4},      # 中等策略：需要2个算法支持，最多杀4个
            'aggressive': {'min_votes': 1, 'max_kills': 6}     # 激进策略：需要1个算法支持，最多杀6个
        }

        results = {}

        for strategy_name, params in strategies.items():
            print(f"\n测试 {strategy_name} 策略 (最少{params['min_votes']}票，最多{params['max_kills']}个杀号):")

            total_periods = 0
            total_kills = 0
            successful_kills = 0
            strategy_details = []

            for i in range(test_periods):
                if i + 2 >= len(self.data):
                    break

                # 获取数据
                current_period = self.data.iloc[i]
                period1_data = self.data.iloc[i + 1]
                period2_data = self.data.iloc[i + 2]

                current_red, _ = parse_numbers(current_period)
                period1_red, _ = parse_numbers(period1_data)
                period2_red, _ = parse_numbers(period2_data)

                # 收集所有算法的投票
                algorithm_votes = {}

                # 各算法投票 (使用有效的算法)
                algorithms = [
                    ('min_sum', self.red_killer._calculate_min_two_sum_kill(period1_red, period2_red)),
                    ('max_diff', self.red_killer._calculate_max_two_diff_kill(period1_red, period2_red)),
                    ('sum_tail', self.red_killer._calculate_sum_tail_kill(period1_red, period2_red)),
                    ('span', self.red_killer._calculate_span_kill(period1_red, period2_red)),
                    ('odd_even', self.red_killer._calculate_odd_even_kill(period1_red, period2_red)),
                    ('consecutive', self.red_killer._calculate_consecutive_kill(period1_red, period2_red)),
                    ('ac_value', self._calculate_ac_value_kill(period1_red, period2_red)),
                    ('fibonacci', self._calculate_fibonacci_kill(period1_red, period2_red)),
                    ('position_sum', self._calculate_position_sum_kill(period1_red, period2_red)),
                    ('interval_kill', self._calculate_interval_kill(period1_red, period2_red)),
                    ('remainder_kill', self._calculate_remainder_kill(period1_red, period2_red)),
                    ('digital_root', self._calculate_digital_root_kill(period1_red, period2_red)),
                    ('symmetry_kill', self._calculate_symmetry_kill(period1_red, period2_red)),
                    ('triangle_kill', self._calculate_triangle_kill(period1_red, period2_red)),
                    ('lucas_kill', self._calculate_lucas_kill(period1_red, period2_red)),
                    ('catalan_kill', self._calculate_catalan_kill(period1_red, period2_red)),
                    ('harmonic_kill', self._calculate_harmonic_kill(period1_red, period2_red)),
                    ('pentagonal_kill', self._calculate_pentagonal_kill(period1_red, period2_red)),
                    ('binary_kill', self._calculate_binary_kill(period1_red, period2_red)),
                    ('factorial_kill', self._calculate_factorial_kill(period1_red, period2_red)),
                    ('modular_kill', self._calculate_modular_kill(period1_red, period2_red)),
                    ('palindrome_kill', self._calculate_palindrome_kill(period1_red, period2_red)),
                    ('abundant_kill', self._calculate_abundant_kill(period1_red, period2_red)),
                    ('deficient_kill', self._calculate_deficient_kill(period1_red, period2_red))
                ]

                for algo_name, kills in algorithms:
                    for kill in kills:
                        if 1 <= kill <= 35 and kill not in (period1_red + period2_red):
                            if kill not in algorithm_votes:
                                algorithm_votes[kill] = []
                            algorithm_votes[kill].append(algo_name)

                # 根据策略筛选杀号
                strategy_kills = []
                for num, votes in algorithm_votes.items():
                    if len(votes) >= params['min_votes']:
                        strategy_kills.append((num, len(votes)))

                # 按票数排序，取前N个
                strategy_kills.sort(key=lambda x: x[1], reverse=True)
                final_kills = [num for num, votes in strategy_kills[:params['max_kills']]]

                # 验证效果
                successful = [k for k in final_kills if k not in current_red]

                period_detail = {
                    'period': current_period['期号'],
                    'kills': final_kills,
                    'successful': successful,
                    'actual': current_red,
                    'success_rate': len(successful) / len(final_kills) if final_kills else 1.0
                }

                strategy_details.append(period_detail)
                total_periods += 1
                total_kills += len(final_kills)
                successful_kills += len(successful)

                # 打印详情
                kills_str = ','.join(map(str, final_kills)) if final_kills else '无'
                success_str = ','.join(map(str, successful)) if successful else '无'
                actual_str = ','.join(map(str, current_red))
                success_rate = len(successful) / len(final_kills) if final_kills else 1.0
                status = "✅" if success_rate == 1.0 else "⚠️" if success_rate >= 0.8 else "❌"
                print(f"  {current_period['期号']}: 杀号[{kills_str}] 成功[{success_str}] 成功率{success_rate:.1%} {status}")

            # 策略总结
            overall_success_rate = successful_kills / total_kills if total_kills > 0 else 1.0
            results[strategy_name] = {
                'total_periods': total_periods,
                'total_kills': total_kills,
                'successful_kills': successful_kills,
                'success_rate': overall_success_rate,
                'details': strategy_details
            }

            print(f"  📊 {strategy_name} 策略总结:")
            print(f"    总期数: {total_periods}")
            print(f"    总杀号: {total_kills}")
            print(f"    成功杀号: {successful_kills}")
            print(f"    成功率: {overall_success_rate:.1%}")

        return results

    def find_best_algorithms(self, red_results: Dict, blue_results: Dict):
        """找出最佳算法组合"""
        print(f"\n🎯 最佳算法组合分析")
        print("=" * 60)

        # 红球最佳算法
        print("🔴 红球最佳算法:")
        red_algos = [(algo, data['success_rate']) for algo, data in red_results['algorithm_results'].items()
                     if data['total_kills'] > 0]
        red_algos.sort(key=lambda x: x[1], reverse=True)

        for i, (algo, rate) in enumerate(red_algos[:3], 1):
            data = red_results['algorithm_results'][algo]
            print(f"  {i}. {algo}: {rate:.1%} ({data['successful_kills']}/{data['total_kills']})")

        # 蓝球最佳算法
        print("\n🔵 蓝球最佳算法:")
        blue_algos = [(algo, data['success_rate']) for algo, data in blue_results['algorithm_results'].items()
                      if data['total_kills'] > 0]
        blue_algos.sort(key=lambda x: x[1], reverse=True)

        for i, (algo, rate) in enumerate(blue_algos[:3], 1):
            data = blue_results['algorithm_results'][algo]
            print(f"  {i}. {algo}: {rate:.1%} ({data['successful_kills']}/{data['total_kills']})")

        # 推荐组合
        print(f"\n💡 推荐杀号策略:")
        if red_algos:
            best_red = red_algos[0]
            print(f"  红球主力算法: {best_red[0]} (成功率{best_red[1]:.1%})")

        if blue_algos:
            best_blue = blue_algos[0]
            print(f"  蓝球主力算法: {best_blue[0]} (成功率{best_blue[1]:.1%})")

    def analyze_ultra_conservative_results(self, results: Dict):
        """分析超保守策略结果"""
        print(f"\n🎯 超保守策略分析报告")
        print("=" * 80)
        print("要求：每期至少杀5个红球号码或2个蓝球号码，且必须全中")
        print()

        # 分析红球策略
        print("🔴 红球超保守策略分析:")
        print("-" * 60)

        red_strategies = [(k, v) for k, v in results.items() if k.startswith('red_')]
        red_strategies.sort(key=lambda x: x[1]['perfect_rate'], reverse=True)

        for strategy_name, data in red_strategies:
            strategy_display = strategy_name.replace('red_', '')
            print(f"\n📊 {strategy_display} 策略:")
            print(f"  有效期数: {data['total_periods']} (满足最少杀号要求)")
            print(f"  全中期数: {data['successful_periods']}")
            print(f"  全中率: {data['perfect_rate']:.1%}")
            print(f"  平均每期杀号: {data['total_kills'] / data['total_periods']:.1f}个" if data['total_periods'] > 0 else "  平均每期杀号: 0个")
            print(f"  整体成功率: {data['overall_success_rate']:.1%}")

            if data['perfect_rate'] >= 0.5:  # 全中率≥50%
                print("  ✅ 推荐使用")
            elif data['perfect_rate'] >= 0.3:  # 全中率≥30%
                print("  ⚠️ 谨慎使用")
            else:
                print("  ❌ 不推荐")

        # 分析蓝球策略
        print("\n🔵 蓝球超保守策略分析:")
        print("-" * 60)

        blue_strategies = [(k, v) for k, v in results.items() if k.startswith('blue_')]
        blue_strategies.sort(key=lambda x: x[1]['perfect_rate'], reverse=True)

        for strategy_name, data in blue_strategies:
            strategy_display = strategy_name.replace('blue_', '')
            print(f"\n📊 {strategy_display} 策略:")
            print(f"  有效期数: {data['total_periods']} (满足最少杀号要求)")
            print(f"  全中期数: {data['successful_periods']}")
            print(f"  全中率: {data['perfect_rate']:.1%}")
            print(f"  平均每期杀号: {data['total_kills'] / data['total_periods']:.1f}个" if data['total_periods'] > 0 else "  平均每期杀号: 0个")
            print(f"  整体成功率: {data['overall_success_rate']:.1%}")

            if data['perfect_rate'] >= 0.6:  # 全中率≥60%
                print("  ✅ 推荐使用")
            elif data['perfect_rate'] >= 0.4:  # 全中率≥40%
                print("  ⚠️ 谨慎使用")
            else:
                print("  ❌ 不推荐")

        # 找出最佳组合
        print(f"\n🏆 最佳超保守策略组合推荐:")
        print("-" * 60)

        best_red = max(red_strategies, key=lambda x: x[1]['perfect_rate']) if red_strategies else None
        best_blue = max(blue_strategies, key=lambda x: x[1]['perfect_rate']) if blue_strategies else None

        if best_red:
            red_name = best_red[0].replace('red_', '')
            red_data = best_red[1]
            print(f"🔴 最佳红球策略: {red_name}")
            print(f"   全中率: {red_data['perfect_rate']:.1%}")
            print(f"   有效期数: {red_data['total_periods']}")
            print(f"   平均杀号: {red_data['total_kills'] / red_data['total_periods']:.1f}个" if red_data['total_periods'] > 0 else "   平均杀号: 0个")

        if best_blue:
            blue_name = best_blue[0].replace('blue_', '')
            blue_data = best_blue[1]
            print(f"\n🔵 最佳蓝球策略: {blue_name}")
            print(f"   全中率: {blue_data['perfect_rate']:.1%}")
            print(f"   有效期数: {blue_data['total_periods']}")
            print(f"   平均杀号: {blue_data['total_kills'] / blue_data['total_periods']:.1f}个" if blue_data['total_periods'] > 0 else "   平均杀号: 0个")

        # 实战建议
        print(f"\n💡 实战建议:")
        print("-" * 60)

        if best_red and best_red[1]['perfect_rate'] >= 0.5:
            print(f"✅ 红球推荐使用 {best_red[0].replace('red_', '')} 策略")
        else:
            print("⚠️ 红球策略全中率偏低，建议降低杀号数量要求")

        if best_blue and best_blue[1]['perfect_rate'] >= 0.6:
            print(f"✅ 蓝球推荐使用 {best_blue[0].replace('blue_', '')} 策略")
        else:
            print("⚠️ 蓝球策略全中率偏低，建议降低杀号数量要求")

        print(f"\n📝 注意事项:")
        print("1. 超保守策略追求100%杀号成功，但会减少有效期数")
        print("2. 全中率与有效期数存在权衡关系")
        print("3. 建议根据实际需求调整最少杀号数量要求")

    def test_precise_single_kill_algorithms(self, test_periods: int = 50):
        """测试精准单号杀号算法 - 每个算法每期只杀1个号码"""
        print(f"\n🎯 测试精准单号杀号算法 (最近{test_periods}期)")
        print("要求：每个算法每期只能计算出1个号码")
        print("=" * 80)

        # 选择10个最佳算法进行精准测试
        selected_algorithms = [
            'pentagonal_kill',      # 97.6% - 五边形数杀号法
            'abundant_kill',        # 95.8% - 过剩数杀号法
            'factorial_kill',       # 95.0% - 阶乘杀号法
            'energy_kill',          # 92.5% - 能量维度杀号法
            'prev2_period_pattern_kill',  # 91.7% - 上上期模式杀号法
            'catalan_kill',         # 91.3% - 卡塔兰数杀号法
            'span',                 # 91.1% - 跨度计算法
            'last_period_plus',     # 91.0% - 上期+1杀号法
            'direction_kill',       # 90.5% - 方位维度杀号法
            'psychology_kill'       # 90.5% - 心理学维度杀号法
        ]

        results = {
            'total_periods': 0,
            'algorithm_results': {},
            'success_details': []
        }

        for algo in selected_algorithms:
            results['algorithm_results'][algo] = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'details': []
            }

        # 逐期测试
        for i in range(test_periods):
            if i + 2 >= len(self.data):
                break

            # 获取当前期和前两期数据
            current_period = self.data.iloc[i]
            period1_data = self.data.iloc[i + 1]  # 上一期
            period2_data = self.data.iloc[i + 2]  # 上二期

            # 解析号码
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period1_data)
            period2_red, _ = parse_numbers(period2_data)

            period_info = {
                'period': current_period['期号'],
                'actual_red': current_red,
                'prev_periods': [period1_red, period2_red],
                'current_period': current_period,
                'algorithm_kills': {}
            }

            # 测试各个算法 - 每个算法只取第一个杀号
            for algo in selected_algorithms:
                kills = self._get_single_kill_number(algo, period1_red, period2_red, current_period)

                # 验证杀号效果 - 只取第一个号码
                if kills and len(kills) > 0:
                    single_kill = kills[0]  # 只取第一个杀号
                    if 1 <= single_kill <= 35 and single_kill not in (period1_red + period2_red):
                        is_successful = single_kill not in current_red

                        period_info['algorithm_kills'][algo] = {
                            'kill': single_kill,
                            'successful': is_successful
                        }

                        # 更新统计
                        results['algorithm_results'][algo]['total_kills'] += 1
                        if is_successful:
                            results['algorithm_results'][algo]['successful_kills'] += 1

                        results['algorithm_results'][algo]['details'].append({
                            'period': period_info['period'],
                            'kill': single_kill,
                            'successful': is_successful,
                            'actual': current_red
                        })
                    else:
                        period_info['algorithm_kills'][algo] = {
                            'kill': None,
                            'successful': False
                        }
                else:
                    period_info['algorithm_kills'][algo] = {
                        'kill': None,
                        'successful': False
                    }

            results['total_periods'] += 1
            results['success_details'].append(period_info)

        # 计算成功率
        for algo in selected_algorithms:
            algo_result = results['algorithm_results'][algo]
            if algo_result['total_kills'] > 0:
                algo_result['success_rate'] = algo_result['successful_kills'] / algo_result['total_kills']

        return results

    def _get_single_kill_number(self, algo: str, period1_red: List[int], period2_red: List[int], current_period) -> List[int]:
        """获取单个杀号 - 每个算法只返回1个最佳杀号"""
        if algo == 'pentagonal_kill':
            kills = self._calculate_pentagonal_kill(period1_red, period2_red)
        elif algo == 'abundant_kill':
            kills = self._calculate_abundant_kill(period1_red, period2_red)
        elif algo == 'factorial_kill':
            kills = self._calculate_factorial_kill(period1_red, period2_red)
        elif algo == 'energy_kill':
            kills = self._calculate_energy_kill(current_period)
        elif algo == 'prev2_period_pattern_kill':
            kills = self._calculate_prev2_period_pattern_kill(period2_red)
        elif algo == 'catalan_kill':
            kills = self._calculate_catalan_kill(period1_red, period2_red)
        elif algo == 'span':
            kills = self.red_killer._calculate_span_kill(period1_red, period2_red)
        elif algo == 'last_period_plus':
            kills = self._calculate_last_period_plus_kill(period1_red)
        elif algo == 'direction_kill':
            kills = self._calculate_direction_kill(current_period)
        elif algo == 'psychology_kill':
            kills = self._calculate_psychology_kill(current_period)
        else:
            kills = []

        # 只返回第一个杀号，如果没有则返回空列表
        return kills[:1] if kills else []

    def print_precise_results(self, results: Dict):
        """打印精准单号杀号测试结果"""
        print(f"\n📊 精准单号杀号算法测试结果 (共{results['total_periods']}期)")
        print("=" * 80)

        # 算法成功率排序
        algo_rates = [(algo, data['success_rate']) for algo, data in results['algorithm_results'].items()]
        algo_rates.sort(key=lambda x: x[1], reverse=True)

        print("🏆 精准单号杀号成功率排行:")
        for i, (algo, rate) in enumerate(algo_rates, 1):
            data = results['algorithm_results'][algo]
            status = "🎯" if rate >= 0.8 else "⚠️" if rate >= 0.6 else "❌"
            print(f"  {i}. {algo:25} {rate:6.1%} ({data['successful_kills']}/{data['total_kills']}) {status}")

        # 详细分析
        print(f"\n📋 详细分析:")
        for algo, data in results['algorithm_results'].items():
            if data['total_kills'] > 0:
                print(f"\n{algo} 算法:")
                print(f"  总杀号数: {data['total_kills']}")
                print(f"  成功杀号: {data['successful_kills']}")
                print(f"  成功率: {data['success_rate']:.1%}")

                # 显示最近几期的详细情况
                print("  最近5期详情:")
                for detail in data['details'][:5]:
                    kill_str = str(detail['kill']) if detail['kill'] else '无'
                    actual_str = ','.join(map(str, detail['actual']))
                    status = "✅" if detail['successful'] else "❌"
                    print(f"    {detail['period']}: 杀号[{kill_str}] 实际[{actual_str}] {status}")


def main():
    """主函数"""
    print("🎯 精准单号杀号算法专门测试系统")
    print("=" * 60)

    # 初始化测试器
    tester = KillAlgorithmTester()

    # 加载数据
    if not tester.load_data():
        return

    # 测试精准单号杀号算法
    precise_results = tester.test_precise_single_kill_algorithms(test_periods=50)
    tester.print_precise_results(precise_results)

    print(f"\n🎉 精准单号杀号测试完成！")


if __name__ == "__main__":
    main()
