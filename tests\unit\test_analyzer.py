"""
测试分析器功能
"""

from utils import load_data
from analyzer import LotteryAnalyzer

def test_analyzer():
    """测试分析器"""
    print("开始测试分析器...")
    
    try:
        # 加载数据
        data = load_data()
        print(f"数据加载成功，共{len(data)}行")
        
        # 创建分析器
        analyzer = LotteryAnalyzer(data)
        print("分析器创建成功")
        
        # 测试特征提取
        print("\n测试特征提取...")
        red_odd_even = analyzer.get_feature_sequence('red_odd_even')
        print(f"红球奇偶比前10期: {red_odd_even[:10]}")
        
        red_size = analyzer.get_feature_sequence('red_size')
        print(f"红球大小比前10期: {red_size[:10]}")
        
        blue_size = analyzer.get_feature_sequence('blue_size')
        print(f"蓝球大小比前10期: {blue_size[:10]}")
        
        # 测试频率计算
        print("\n测试频率计算...")
        red_odd_even_freq = analyzer.calculate_state_frequencies('red_odd_even')
        print(f"红球奇偶比频率: {red_odd_even_freq}")
        
        print("\n分析器测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_analyzer()
