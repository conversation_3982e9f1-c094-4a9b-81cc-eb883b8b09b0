"""
模型配置模块
定义各种模型的配置参数
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any


@dataclass
class MarkovConfig:
    """马尔科夫模型配置"""
    order: int = 2
    smoothing: float = 0.1
    min_samples: int = 5
    use_weighted_transitions: bool = True
    decay_factor: float = 0.9


@dataclass
class BayesConfig:
    """贝叶斯模型配置"""
    prior_weight: float = 0.4
    recent_window: int = 20
    smoothing_factor: float = 0.01
    use_adaptive_prior: bool = True


@dataclass
class NeuralConfig:
    """神经网络配置"""
    hidden_layers: List[int] = field(default_factory=lambda: [64, 32, 16])
    dropout_rate: float = 0.2
    learning_rate: float = 0.001
    epochs: int = 100
    batch_size: int = 32
    validation_split: float = 0.2
    early_stopping_patience: int = 10
    use_batch_normalization: bool = True
    activation: str = "relu"
    optimizer: str = "adam"


@dataclass
class EnsembleConfig:
    """集成学习配置"""
    weights: Dict[str, float] = field(default_factory=lambda: {
        'markov': 0.3,
        'bayes': 0.3,
        'neural': 0.4
    })
    use_dynamic_weights: bool = True
    weight_update_frequency: int = 10
    min_weight: float = 0.1
    max_weight: float = 0.8


@dataclass
class ModelConfig:
    """模型总配置"""
    markov: MarkovConfig = field(default_factory=MarkovConfig)
    bayes: BayesConfig = field(default_factory=BayesConfig)
    neural: NeuralConfig = field(default_factory=NeuralConfig)
    ensemble: EnsembleConfig = field(default_factory=EnsembleConfig)
    
    # 通用配置
    random_seed: int = 42
    use_gpu: bool = False
    model_save_path: str = "models/saved"
    auto_save: bool = True
    
    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证马尔科夫配置
            if self.markov.order < 1:
                raise ValueError("马尔科夫阶数必须大于0")
            
            # 验证贝叶斯配置
            if not 0 < self.bayes.prior_weight < 1:
                raise ValueError("贝叶斯先验权重必须在0-1之间")
            
            # 验证神经网络配置
            if self.neural.learning_rate <= 0:
                raise ValueError("学习率必须大于0")
            
            if self.neural.epochs <= 0:
                raise ValueError("训练轮数必须大于0")
            
            # 验证集成权重
            total_weight = sum(self.ensemble.weights.values())
            if abs(total_weight - 1.0) > 0.01:
                raise ValueError(f"集成权重总和必须为1.0，当前为{total_weight}")
            
            return True
            
        except Exception as e:
            print(f"模型配置验证失败: {e}")
            return False
