#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查所有生成器的杀号过滤功能
"""

from src.systems.main import LotteryPredictor

def check_all_generators():
    """检查所有生成器"""
    print("🔍 检查所有生成器的杀号过滤功能")
    print("=" * 80)
    
    # 初始化预测器
    predictor = LotteryPredictor()
    
    # 测试杀号数据
    kill_numbers = {
        'red': [[13, 14, 35, 33, 18, 15, 24, 23]],
        'blue': [[2]]
    }
    
    print(f"测试杀号: {kill_numbers}")
    print()
    
    # 测试所有生成器
    generators = [
        ('dynamic', predictor.dynamic_generator),
        ('precision', predictor.precision_generator),
        ('diversified', predictor.diversified_generator),
        ('insight', predictor.insight_generator),
        ('advanced', predictor.advanced_generator),
        ('traditional', predictor.generator)
    ]
    
    for name, generator in generators:
        print(f"🧪 测试 {name} 生成器:")
        
        # 检查是否有_apply_kill_filter方法
        if hasattr(generator, '_apply_kill_filter'):
            print(f"  ✅ 有_apply_kill_filter方法")
        else:
            print(f"  ❌ 没有_apply_kill_filter方法")
        
        # 测试生成
        try:
            if name == 'dynamic':
                red, blue = generator.generate_dynamic_numbers(
                    "3:2", "2:3", "1:1", kill_numbers, 42, 0
                )
            elif name == 'precision':
                red, blue = generator.generate_precision_numbers(
                    "3:2", "2:3", "1:1", kill_numbers, 42
                )
            elif name == 'diversified':
                red, blue = generator.generate_diversified_numbers(
                    "3:2", "2:3", "1:1", kill_numbers, 42
                )
            elif name == 'insight':
                historical_numbers = [([1,2,3,4,5], [1,2])]
                red, blue = generator.generate_numbers_with_insights(
                    "3:2", "2:3", "1:1", historical_numbers, kill_numbers, 42
                )
            elif name == 'advanced':
                historical_numbers = [([1,2,3,4,5], [1,2])]
                red, blue = generator.generate_optimal_combination(
                    "3:2", "2:3", "1:1", kill_numbers, historical_numbers, 42
                )
            else:  # traditional
                historical_numbers = [([1,2,3,4,5], [1,2])]
                target_sum_range = (80, 120)
                red, blue = generator.generate_numbers_by_state(
                    "3:2", "2:3", "1:1", kill_numbers, 42, historical_numbers, target_sum_range
                )
            
            print(f"  生成结果: 红球{red}, 蓝球{blue}")
            
            # 检查违规
            red_kills = kill_numbers['red'][0]
            blue_kills = kill_numbers['blue'][0]
            
            red_violations = [num for num in red if num in red_kills]
            blue_violations = [num for num in blue if num in blue_kills]
            
            if red_violations:
                print(f"  ❌ 红球违规: {red_violations}")
            else:
                print(f"  ✅ 红球杀号生效")
            
            if blue_violations:
                print(f"  ❌ 蓝球违规: {blue_violations}")
            else:
                print(f"  ✅ 蓝球杀号生效")
                
        except Exception as e:
            print(f"  ❌ 生成失败: {e}")
        
        print()

def check_specific_generator():
    """检查特定生成器"""
    print("🔍 检查可能有问题的生成器:")
    print("-" * 60)
    
    predictor = LotteryPredictor()
    
    # 测试insight生成器（第4个，可能是问题源）
    print("🧪 重点测试insight生成器:")
    
    kill_numbers = {
        'red': [[13, 14, 35, 33, 18, 15, 24, 23]],
        'blue': [[2]]
    }
    
    historical_numbers = [([1,2,3,4,5], [1,2])]
    
    try:
        for i in range(5):
            red, blue = predictor.insight_generator.generate_numbers_with_insights(
                "3:2", "2:3", "1:1", historical_numbers, kill_numbers, 42 + i
            )
            
            print(f"  第{i+1}次: 红球{red}, 蓝球{blue}")
            
            # 检查是否包含15
            if 15 in red:
                print(f"    ❌ 包含被杀号码15！")
            else:
                print(f"    ✅ 未包含被杀号码")
                
    except Exception as e:
        print(f"  ❌ insight生成器测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_enhanced_selector():
    """检查增强选择器"""
    print("\n🔍 检查增强选择器:")
    print("-" * 60)
    
    try:
        from src.models.enhanced_number_selector import EnhancedNumberSelector
        
        selector = EnhancedNumberSelector()
        
        # 检查是否有杀号过滤
        if hasattr(selector, '_apply_kill_filter'):
            print("✅ EnhancedNumberSelector有_apply_kill_filter方法")
        else:
            print("❌ EnhancedNumberSelector没有_apply_kill_filter方法")
        
        # 测试选择器
        historical_data = [([1,2,3,4,5], [1,2])]
        selector.initialize(historical_data)
        
        kill_numbers = {
            'red': [[13, 14, 35, 33, 18, 15, 24, 23]],
            'blue': [[2]]
        }
        
        red, blue = selector.select_enhanced_numbers(
            "3:2", "2:3", "1:1", kill_numbers, 42
        )
        
        print(f"增强选择结果: 红球{red}, 蓝球{blue}")
        
        if 15 in red:
            print(f"❌ 增强选择器包含被杀号码15！")
        else:
            print(f"✅ 增强选择器正常")
            
    except Exception as e:
        print(f"❌ 增强选择器测试失败: {e}")

def main():
    """主函数"""
    try:
        check_all_generators()
        check_specific_generator()
        check_enhanced_selector()
        
        print("🎯 检查总结:")
        print("=" * 80)
        print("需要重点关注:")
        print("1. 哪个生成器没有_apply_kill_filter方法")
        print("2. 哪个生成器生成了被杀号码15")
        print("3. 增强选择器是否正确过滤杀号")
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
