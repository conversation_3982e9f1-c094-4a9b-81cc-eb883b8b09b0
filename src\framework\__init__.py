"""
统一回测框架
提供标准化的回测接口和数据模型
"""

from .interfaces import PredictorInterface, EvaluatorInterface
from .data_models import BacktestConfig, BacktestResult, PredictionResult, EvaluationResult
from .backtest_framework import BacktestFramework
from .result_display import ResultDisplayer
from .predictor_adapter import create_predictor_adapter

__all__ = [
    'PredictorInterface',
    'EvaluatorInterface',
    'BacktestConfig',
    'BacktestResult',
    'PredictionResult',
    'EvaluationResult',
    'BacktestFramework',
    'ResultDisplayer',
    'create_predictor_adapter'
]
