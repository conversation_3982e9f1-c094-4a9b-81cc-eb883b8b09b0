#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
紧急修复红球杀号算法
基于实际失败案例的分析和修正
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict

class EmergencyRedBallFix:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        
        # 分析失败案例，找出我们错误杀掉的号码模式
        self.failed_kills_analysis = self._analyze_failed_kills()
        
    def _analyze_failed_kills(self):
        """分析失败的杀号案例"""
        print("🔍 分析失败的杀号案例...")
        
        # 这些是我们错误杀掉但实际开出的号码
        failed_kills = [
            # 从用户提供的数据中提取
            [3, 26, 1, 2],      # 25061→25062
            [14, 33, 1, 19],    # 25062→25063  
            [2],                # 25063→25064
            [27, 14],           # 25064→25065
            [26, 32, 18],       # 25065→25066
            [10, 18],           # 25066→25067
            [15, 18, 27, 34],   # 25067→25068
            [6, 10, 12, 21],    # 25068→25069
        ]
        
        # 统计被错误杀掉的号码频率
        error_counter = Counter()
        for failed_list in failed_kills:
            error_counter.update(failed_list)
        
        print("❌ 经常被错误杀掉的号码:")
        for num, count in error_counter.most_common(10):
            print(f"  号码 {num:2d}: 被错误杀掉 {count} 次")
        
        return {
            'frequently_wrong_kills': [num for num, count in error_counter.most_common(15)],
            'error_patterns': error_counter
        }
    
    def calculate_corrected_red_kills(self, recent_periods: List[List[int]], target_count: int = 13) -> List[int]:
        """
        修正的红球杀号算法
        
        核心策略：
        1. 避免杀掉经常被错误杀掉的号码
        2. 重新定义"冷号"
        3. 使用更保守的策略
        """
        if len(recent_periods) < 2:
            return list(range(1, target_count + 1))
        
        # 策略1: 识别真正的长期冷号
        true_cold_numbers = self._identify_true_cold_numbers()
        
        # 策略2: 避免杀掉近期活跃号码
        recent_active = self._get_recent_active_numbers(recent_periods)
        
        # 策略3: 避免杀掉经常被错误杀掉的号码
        frequently_wrong = self.failed_kills_analysis['frequently_wrong_kills']
        
        # 生成候选杀号
        kill_candidates = []
        
        # 优先选择真正的冷号
        for num in true_cold_numbers:
            if num not in recent_active and num not in frequently_wrong:
                kill_candidates.append(num)
        
        # 如果不够，添加一些相对安全的号码
        if len(kill_candidates) < target_count:
            safe_numbers = self._get_safe_kill_numbers(recent_periods, recent_active, frequently_wrong)
            kill_candidates.extend(safe_numbers)
        
        # 去重并限制数量
        final_kills = list(dict.fromkeys(kill_candidates))[:target_count]
        
        print(f"🔧 修正杀号策略:")
        print(f"  真正冷号: {len([n for n in final_kills if n in true_cold_numbers])}个")
        print(f"  避免近期活跃: 排除了{len(recent_active)}个号码")
        print(f"  避免错误模式: 排除了{len(frequently_wrong)}个号码")
        
        return final_kills
    
    def _identify_true_cold_numbers(self) -> List[int]:
        """识别真正的长期冷号"""
        try:
            # 分析最近30期的出现频率
            recent_30_counter = Counter()
            for i, row in self.data.head(30).iterrows():
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(row)
                recent_30_counter.update(red_balls)
            
            # 分析最近50期的出现频率
            recent_50_counter = Counter()
            for i, row in self.data.head(50).iterrows():
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(row)
                recent_50_counter.update(red_balls)
            
            # 找出在两个时间窗口都很少出现的号码
            true_cold = []
            for num in range(1, 36):
                freq_30 = recent_30_counter.get(num, 0)
                freq_50 = recent_50_counter.get(num, 0)
                
                # 真正的冷号：30期内出现≤1次，50期内出现≤2次
                if freq_30 <= 1 and freq_50 <= 2:
                    true_cold.append(num)
            
            print(f"🧊 识别出 {len(true_cold)} 个真正冷号: {true_cold}")
            return true_cold
            
        except:
            return [16, 17, 23, 24, 25, 30, 31]  # 备用冷号
    
    def _get_recent_active_numbers(self, recent_periods: List[List[int]]) -> Set[int]:
        """获取近期活跃号码"""
        active_numbers = set()
        
        # 前3期出现的号码都认为是活跃的
        for i, period in enumerate(recent_periods[:3]):
            active_numbers.update(period)
        
        return active_numbers
    
    def _get_safe_kill_numbers(self, recent_periods: List[List[int]], 
                              recent_active: Set[int], frequently_wrong: List[int]) -> List[int]:
        """获取相对安全的杀号"""
        safe_numbers = []
        
        # 选择一些中等频率但不在禁止列表中的号码
        for num in range(1, 36):
            if (num not in recent_active and 
                num not in frequently_wrong and 
                num not in safe_numbers):
                safe_numbers.append(num)
        
        return safe_numbers
    
    def validate_against_failed_cases(self):
        """验证修正算法是否能避免之前的失败"""
        print("\n🧪 验证修正算法...")
        
        # 模拟之前失败的情况
        test_cases = [
            {
                'recent_periods': [[15, 18, 27, 28, 34], [7, 25, 32, 33, 35]],
                'actual_result': [6, 10, 12, 21, 22],
                'period': '25068→25069'
            }
        ]
        
        for case in test_cases:
            corrected_kills = self.calculate_corrected_red_kills(case['recent_periods'], 13)
            
            # 检查是否还会误杀
            wrong_kills = [num for num in corrected_kills if num in case['actual_result']]
            
            print(f"  {case['period']}:")
            print(f"    修正杀号: {corrected_kills}")
            print(f"    实际开奖: {case['actual_result']}")
            print(f"    误杀数量: {len(wrong_kills)} ({wrong_kills})")
            
            if len(wrong_kills) == 0:
                print(f"    ✅ 修正成功，避免了误杀")
            else:
                print(f"    ⚠️  仍有误杀，需要进一步调整")

def test_emergency_fix():
    """测试紧急修复算法"""
    print("🚨 测试紧急修复的红球杀号算法")
    print("=" * 60)
    
    # 加载数据
    try:
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 初始化修复算法
        emergency_fix = EmergencyRedBallFix(data)
        
        # 验证修正效果
        emergency_fix.validate_against_failed_cases()
        
        # 获取最近几期数据进行测试
        recent_periods = []
        for i in range(6):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(data.iloc[i])
                recent_periods.append(red_balls)
        
        print(f"\n📊 使用最近期数据测试修正算法:")
        corrected_kills = emergency_fix.calculate_corrected_red_kills(recent_periods, 13)
        print(f"修正后杀号: {sorted(corrected_kills)} (共{len(corrected_kills)}个)")
        
        # 分析特征
        if corrected_kills:
            odd_count = sum(1 for num in corrected_kills if num % 2 == 1)
            small_count = sum(1 for num in corrected_kills if num <= 17)
            print(f"特征分析: 奇数{odd_count}个, 偶数{13-odd_count}个, 小数{small_count}个, 大数{13-small_count}个")
            print(f"号码范围: {min(corrected_kills)}-{max(corrected_kills)}")
        
        print(f"\n✅ 紧急修复测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_emergency_fix()
