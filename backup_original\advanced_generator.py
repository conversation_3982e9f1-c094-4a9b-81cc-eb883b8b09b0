"""
高级号码生成器
基于概率分布和多特征融合的智能号码生成
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from collections import Counter, defaultdict
from itertools import combinations
from utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio
)


class AdvancedNumberGenerator:
    """高级号码生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        
        # 号码概率分布
        self.red_probabilities = None
        self.blue_probabilities = None
        
        # 特征权重
        self.feature_weights = {
            'odd_even': 0.25,
            'size': 0.25,
            'frequency': 0.20,
            'pattern': 0.15,
            'correlation': 0.15
        }
    
    def analyze_historical_patterns(self, historical_data: List[Tuple[List[int], List[int]]]) -> None:
        """
        分析历史模式，建立概率分布
        
        Args:
            historical_data: 历史开奖数据
        """
        if not historical_data:
            return
        
        # 分析红球概率分布
        self._analyze_red_probabilities(historical_data)
        
        # 分析蓝球概率分布
        self._analyze_blue_probabilities(historical_data)
    
    def _analyze_red_probabilities(self, historical_data: List[Tuple[List[int], List[int]]]) -> None:
        """分析红球概率分布"""
        red_frequencies = Counter()
        position_frequencies = defaultdict(Counter)
        
        # 统计频率
        for red_balls, _ in historical_data:
            red_frequencies.update(red_balls)
            
            # 按位置统计
            sorted_red = sorted(red_balls)
            for pos, num in enumerate(sorted_red):
                position_frequencies[pos][num] += 1
        
        # 计算基础概率
        total_appearances = sum(red_frequencies.values())
        base_probabilities = {}
        for num in self.red_range:
            base_probabilities[num] = red_frequencies.get(num, 0) / total_appearances
        
        # 时间衰减权重
        time_weighted_probs = {}
        for num in self.red_range:
            weighted_sum = 0
            total_weight = 0
            
            for i, (red_balls, _) in enumerate(historical_data):
                if num in red_balls:
                    # 越近的数据权重越大
                    weight = np.exp(-0.05 * i)
                    weighted_sum += weight
                    total_weight += weight
            
            if total_weight > 0:
                time_weighted_probs[num] = weighted_sum / len(historical_data)
            else:
                time_weighted_probs[num] = 0.01  # 最小概率
        
        # 融合概率
        self.red_probabilities = {}
        for num in self.red_range:
            base_prob = base_probabilities.get(num, 0.01)
            time_prob = time_weighted_probs.get(num, 0.01)
            
            # 加权平均
            self.red_probabilities[num] = 0.6 * base_prob + 0.4 * time_prob
        
        # 归一化
        total_prob = sum(self.red_probabilities.values())
        if total_prob > 0:
            for num in self.red_probabilities:
                self.red_probabilities[num] /= total_prob
    
    def _analyze_blue_probabilities(self, historical_data: List[Tuple[List[int], List[int]]]) -> None:
        """分析蓝球概率分布"""
        blue_frequencies = Counter()
        
        for _, blue_balls in historical_data:
            blue_frequencies.update(blue_balls)
        
        # 计算概率
        total_appearances = sum(blue_frequencies.values())
        self.blue_probabilities = {}
        
        for num in self.blue_range:
            freq = blue_frequencies.get(num, 0)
            self.blue_probabilities[num] = freq / total_appearances if total_appearances > 0 else 1.0 / len(self.blue_range)
        
        # 时间衰减调整
        time_weighted_probs = {}
        for num in self.blue_range:
            weighted_sum = 0
            total_weight = 0
            
            for i, (_, blue_balls) in enumerate(historical_data):
                if num in blue_balls:
                    weight = np.exp(-0.05 * i)
                    weighted_sum += weight
                    total_weight += weight
            
            if total_weight > 0:
                time_weighted_probs[num] = weighted_sum / len(historical_data)
            else:
                time_weighted_probs[num] = 0.01
        
        # 融合概率
        for num in self.blue_range:
            base_prob = self.blue_probabilities.get(num, 0.01)
            time_prob = time_weighted_probs.get(num, 0.01)
            self.blue_probabilities[num] = 0.7 * base_prob + 0.3 * time_prob
        
        # 归一化
        total_prob = sum(self.blue_probabilities.values())
        if total_prob > 0:
            for num in self.blue_probabilities:
                self.blue_probabilities[num] /= total_prob
    
    def generate_optimal_combination(self, 
                                   target_odd_even: str,
                                   target_size: str,
                                   target_blue_size: str,
                                   kill_numbers: Dict[str, List[List[int]]] = None,
                                   historical_data: List[Tuple[List[int], List[int]]] = None,
                                   seed: int = 0) -> Tuple[List[int], List[int]]:
        """
        生成最优号码组合
        
        Args:
            target_odd_even: 目标红球奇偶比
            target_size: 目标红球大小比
            target_blue_size: 目标蓝球大小比
            kill_numbers: 杀号列表
            historical_data: 历史数据
            seed: 随机种子
            
        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        if historical_data:
            self.analyze_historical_patterns(historical_data)
        
        # 生成红球
        red_balls = self._generate_optimal_red_balls(
            target_odd_even, target_size, kill_numbers, seed
        )
        
        # 生成蓝球
        blue_balls = self._generate_optimal_blue_balls(
            target_blue_size, kill_numbers, seed
        )
        
        return red_balls, blue_balls
    
    def _generate_optimal_red_balls(self, 
                                  target_odd_even: str,
                                  target_size: str,
                                  kill_numbers: Dict[str, List[List[int]]] = None,
                                  seed: int = 0) -> List[int]:
        """生成最优红球组合"""
        # 解析目标状态
        odd_count, even_count = state_to_ratio(target_odd_even)
        small_count, big_count = state_to_ratio(target_size)
        
        # 创建候选池
        candidates = set(self.red_range)
        
        # 移除杀号
        if kill_numbers and 'red' in kill_numbers:
            for kill_list in kill_numbers['red']:
                candidates -= set(kill_list)
        
        candidates = list(candidates)
        
        # 按条件分类
        odd_candidates = [n for n in candidates if n % 2 == 1]
        even_candidates = [n for n in candidates if n % 2 == 0]
        small_candidates = [n for n in candidates if 1 <= n <= 18]
        big_candidates = [n for n in candidates if 19 <= n <= 35]
        
        # 使用概率分布选择
        best_combination = None
        best_score = -1
        
        # 多次尝试生成最优组合
        for attempt in range(50):
            try:
                combination = self._probabilistic_selection(
                    odd_candidates, even_candidates, odd_count, even_count,
                    small_candidates, big_candidates, small_count, big_count,
                    seed + attempt
                )
                
                if combination and len(set(combination)) == 5:
                    score = self._evaluate_combination_quality(combination)
                    if score > best_score:
                        best_score = score
                        best_combination = combination
            except:
                continue
        
        return sorted(best_combination) if best_combination else sorted(candidates[:5])
    
    def _probabilistic_selection(self, 
                               odd_candidates: List[int], 
                               even_candidates: List[int],
                               odd_count: int, 
                               even_count: int,
                               small_candidates: List[int], 
                               big_candidates: List[int],
                               small_count: int, 
                               big_count: int,
                               seed: int = 0) -> List[int]:
        """基于概率的选择"""
        np.random.seed(seed)
        
        # 创建四个分组
        odd_small = [n for n in odd_candidates if n in small_candidates]
        odd_big = [n for n in odd_candidates if n in big_candidates]
        even_small = [n for n in even_candidates if n in small_candidates]
        even_big = [n for n in even_candidates if n in big_candidates]
        
        # 尝试不同的分配策略
        for odd_small_need in range(min(odd_count, small_count) + 1):
            for odd_big_need in range(min(odd_count - odd_small_need, big_count) + 1):
                if odd_small_need + odd_big_need <= odd_count:
                    remaining_odd = odd_count - odd_small_need - odd_big_need
                    if remaining_odd == 0:
                        even_small_need = small_count - odd_small_need
                        even_big_need = big_count - odd_big_need
                        
                        if (even_small_need >= 0 and even_big_need >= 0 and 
                            even_small_need + even_big_need == even_count and
                            len(odd_small) >= odd_small_need and
                            len(odd_big) >= odd_big_need and
                            len(even_small) >= even_small_need and
                            len(even_big) >= even_big_need):
                            
                            selected = []
                            selected.extend(self._weighted_random_select(odd_small, odd_small_need))
                            selected.extend(self._weighted_random_select(odd_big, odd_big_need))
                            selected.extend(self._weighted_random_select(even_small, even_small_need))
                            selected.extend(self._weighted_random_select(even_big, even_big_need))
                            
                            if len(set(selected)) == 5:
                                return selected
        
        # 备选方案
        all_candidates = list(set(odd_candidates + even_candidates))
        return self._weighted_random_select(all_candidates, 5)
    
    def _weighted_random_select(self, candidates: List[int], count: int) -> List[int]:
        """基于权重的随机选择"""
        if not candidates or count <= 0:
            return []
        
        if count >= len(candidates):
            return candidates[:]
        
        # 计算权重
        weights = []
        for num in candidates:
            weight = self.red_probabilities.get(num, 1.0) if self.red_probabilities else 1.0
            weights.append(weight)
        
        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(candidates)] * len(candidates)
        
        # 无重复随机选择
        selected = []
        remaining_candidates = candidates[:]
        remaining_weights = weights[:]
        
        for _ in range(count):
            if not remaining_candidates:
                break
            
            # 重新归一化权重
            total_weight = sum(remaining_weights)
            if total_weight > 0:
                normalized_weights = [w / total_weight for w in remaining_weights]
            else:
                normalized_weights = [1.0 / len(remaining_weights)] * len(remaining_weights)
            
            # 选择一个号码
            chosen_idx = np.random.choice(len(remaining_candidates), p=normalized_weights)
            selected.append(remaining_candidates[chosen_idx])
            
            # 移除已选择的号码
            remaining_candidates.pop(chosen_idx)
            remaining_weights.pop(chosen_idx)
        
        return selected
    
    def _generate_optimal_blue_balls(self, 
                                   target_size: str,
                                   kill_numbers: Dict[str, List[List[int]]] = None,
                                   seed: int = 0) -> List[int]:
        """生成最优蓝球组合"""
        small_count, big_count = state_to_ratio(target_size)
        
        # 创建候选池
        candidates = set(self.blue_range)
        
        # 移除杀号
        if kill_numbers and 'blue' in kill_numbers:
            for kill_list in kill_numbers['blue']:
                candidates -= set(kill_list)
        
        candidates = list(candidates)
        
        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 6]
        big_candidates = [n for n in candidates if 7 <= n <= 12]
        
        np.random.seed(seed)
        
        selected = []
        selected.extend(self._weighted_random_select_blue(small_candidates, small_count))
        selected.extend(self._weighted_random_select_blue(big_candidates, big_count))
        
        # 如果数量不足，补充
        if len(selected) < 2:
            remaining = list(set(candidates) - set(selected))
            needed = 2 - len(selected)
            selected.extend(self._weighted_random_select_blue(remaining, needed))
        
        return sorted(selected[:2])
    
    def _weighted_random_select_blue(self, candidates: List[int], count: int) -> List[int]:
        """蓝球加权随机选择"""
        if not candidates or count <= 0:
            return []
        
        if count >= len(candidates):
            return candidates[:]
        
        # 计算权重
        weights = []
        for num in candidates:
            weight = self.blue_probabilities.get(num, 1.0) if self.blue_probabilities else 1.0
            weights.append(weight)
        
        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(candidates)] * len(candidates)
        
        # 无重复选择
        selected = []
        remaining_candidates = candidates[:]
        remaining_weights = weights[:]
        
        for _ in range(count):
            if not remaining_candidates:
                break
            
            total_weight = sum(remaining_weights)
            if total_weight > 0:
                normalized_weights = [w / total_weight for w in remaining_weights]
            else:
                normalized_weights = [1.0 / len(remaining_weights)] * len(remaining_weights)
            
            chosen_idx = np.random.choice(len(remaining_candidates), p=normalized_weights)
            selected.append(remaining_candidates[chosen_idx])
            
            remaining_candidates.pop(chosen_idx)
            remaining_weights.pop(chosen_idx)
        
        return selected
    
    def _evaluate_combination_quality(self, combination: List[int]) -> float:
        """评估组合质量"""
        if len(set(combination)) != 5:
            return 0.0
        
        score = 0.0
        
        # 和值评分
        total_sum = sum(combination)
        if 80 <= total_sum <= 120:
            score += 25
        elif 70 <= total_sum <= 130:
            score += 15
        
        # 跨度评分
        span = max(combination) - min(combination)
        if 15 <= span <= 25:
            score += 20
        elif 10 <= span <= 30:
            score += 10
        
        # 分布评分
        sorted_combo = sorted(combination)
        gaps = [sorted_combo[i+1] - sorted_combo[i] for i in range(4)]
        avg_gap = sum(gaps) / len(gaps)
        if 3 <= avg_gap <= 8:
            score += 20
        
        # 连号评分
        consecutive_count = sum(1 for i in range(4) if sorted_combo[i+1] - sorted_combo[i] == 1)
        if consecutive_count <= 2:
            score += 15
        
        # 概率评分
        if self.red_probabilities:
            prob_score = sum(self.red_probabilities.get(num, 0) for num in combination)
            score += prob_score * 100
        
        return score
