"""
高级预测系统
集成扩展数据集、神经网络和自适应学习的完整预测系统
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.extended_data_manager import ExtendedDataManager
from src.models.neural.neural_predictor import NeuralPredictor
from src.models.adaptive_learning_system import AdaptiveLearningSystem
from src.generators.dynamic_generator import DynamicGenerator
from src.systems.main import LotteryPredictor
from src.utils.utils import load_data, parse_numbers, check_hit_2_plus_1


class AdvancedPredictionSystem:
    """高级预测系统"""
    
    def __init__(self):
        """初始化高级预测系统"""
        print("初始化高级预测系统...")
        
        # 核心组件
        self.data_manager = ExtendedDataManager()
        self.neural_predictor = NeuralPredictor()
        self.adaptive_system = AdaptiveLearningSystem()
        self.dynamic_generator = DynamicGenerator()
        self.base_predictor = LotteryPredictor()
        
        # 系统状态
        self.is_initialized = False
        self.model_version = "v2.0"
        self.last_update = None
        
        # 性能跟踪
        self.prediction_history = []
        self.performance_metrics = {
            'total_predictions': 0,
            'red_odd_even_hits': 0,
            'red_size_hits': 0,
            'blue_size_hits': 0,
            'hit_2_plus_1': 0
        }
    
    def initialize_system(self, retrain_neural: bool = True, 
                         neural_epochs: int = 100) -> bool:
        """
        初始化系统
        
        Args:
            retrain_neural: 是否重新训练神经网络
            neural_epochs: 神经网络训练轮数
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            print("=" * 60)
            print("🚀 高级预测系统初始化")
            print("=" * 60)
            
            # 1. 加载扩展数据集
            print("\n1. 加载扩展数据集...")
            extended_data = self.data_manager.load_extended_dataset()
            
            if len(extended_data) == 0:
                print("❌ 无法加载数据集")
                return False
            
            stats = self.data_manager.get_data_statistics()
            print(f"✅ 数据集加载成功: {stats}")
            
            # 2. 训练神经网络模型
            if retrain_neural:
                print("\n2. 训练神经网络模型...")
                
                # 获取训练数据
                train_data, val_data = self.data_manager.get_training_data(train_ratio=0.8)
                
                if len(train_data) < 50:
                    print("⚠️ 训练数据不足，跳过神经网络训练")
                else:
                    # 尝试加载已有模型
                    model_path = "neural_models.pkl"
                    if not self.neural_predictor.load_models(model_path):
                        print("训练新的神经网络模型...")
                        self.neural_predictor.train_models(train_data, epochs=neural_epochs)
                        self.neural_predictor.save_models(model_path)
                    else:
                        print("✅ 已加载预训练的神经网络模型")
            
            # 3. 初始化自适应学习系统
            print("\n3. 初始化自适应学习系统...")
            
            # 尝试加载学习状态
            learning_state_path = "adaptive_learning_state.json"
            if not self.adaptive_system.load_learning_state(learning_state_path):
                print("创建新的自适应学习状态")
                
                # 使用最近的数据初始化
                recent_data = extended_data.head(50)
                for _, row in recent_data.iterrows():
                    try:
                        red_balls, blue_balls = parse_numbers(row)
                        self.adaptive_system.update_with_new_result(
                            row['期号'], red_balls, blue_balls
                        )
                    except:
                        continue
            
            # 4. 初始化动态生成器
            print("\n4. 初始化动态生成器...")
            # 动态生成器会在预测时自动初始化
            
            # 5. 初始化基础预测器
            print("\n5. 初始化基础预测器...")
            # 基础预测器使用原有数据
            
            self.is_initialized = True
            self.last_update = datetime.now()
            
            print("\n✅ 高级预测系统初始化完成!")
            print(f"   模型版本: {self.model_version}")
            print(f"   初始化时间: {self.last_update}")
            
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            return False
    
    def predict_next_period(self, current_period_index: int = 0) -> Dict:
        """
        预测下一期号码
        
        Args:
            current_period_index: 当前期次索引
            
        Returns:
            Dict: 预测结果
        """
        if not self.is_initialized:
            raise ValueError("系统尚未初始化，请先调用 initialize_system()")
        
        print(f"\n🎯 开始预测第 {current_period_index + 1} 期...")
        
        try:
            # 1. 获取基础预测
            print("1. 获取基础预测...")
            base_prediction = self.base_predictor.predict_next_period(current_period_index)
            
            # 2. 神经网络预测
            neural_predictions = {}
            if self.neural_predictor.is_trained:
                try:
                    print("2. 神经网络预测...")
                    data = load_data()
                    if len(data) > current_period_index + 10:
                        recent_data = data.iloc[current_period_index:]
                        neural_predictions = self.neural_predictor.predict(recent_data, lookback=5)
                        print(f"   神经网络预测: {neural_predictions}")
                except Exception as e:
                    print(f"   神经网络预测失败: {e}")
            
            # 3. 自适应调整
            print("3. 应用自适应调整...")
            combined_predictions = {**base_prediction}
            
            # 合并神经网络预测
            for key, (pred_value, confidence) in neural_predictions.items():
                if key in combined_predictions:
                    # 加权平均
                    base_conf = combined_predictions[key][1] if isinstance(combined_predictions[key], tuple) else 0.5
                    neural_weight = 0.3  # 神经网络权重
                    
                    if confidence > base_conf:
                        combined_predictions[key] = (pred_value, confidence * neural_weight + base_conf * (1 - neural_weight))
            
            # 应用自适应调整
            adaptive_predictions = self.adaptive_system.get_adaptive_predictions(combined_predictions)
            
            # 4. 动态号码生成
            print("4. 动态号码生成...")
            
            # 提取状态预测
            red_odd_even_state = adaptive_predictions.get('red_odd_even_prediction', ('3:2', 0.5))[0]
            red_size_state = adaptive_predictions.get('red_size_prediction', ('2:3', 0.5))[0]
            blue_size_state = adaptive_predictions.get('blue_size_prediction', ('1:1', 0.5))[0]
            
            # 获取杀号
            kill_numbers = adaptive_predictions.get('kill_numbers', {'red': [], 'blue': []})
            
            # 生成号码
            predicted_red, predicted_blue = self.dynamic_generator.generate_dynamic_numbers(
                red_odd_even_state, red_size_state, blue_size_state,
                kill_numbers, current_period_index, current_period_index
            )
            
            # 5. 整合最终预测结果
            final_prediction = {
                'period_index': current_period_index,
                'prediction_time': datetime.now(),
                'model_version': self.model_version,
                
                # 状态预测
                'red_odd_even_prediction': adaptive_predictions.get('red_odd_even_prediction', ('3:2', 0.5)),
                'red_size_prediction': adaptive_predictions.get('red_size_prediction', ('2:3', 0.5)),
                'blue_size_prediction': adaptive_predictions.get('blue_size_prediction', ('1:1', 0.5)),
                
                # 号码预测
                'generated_numbers': (predicted_red, predicted_blue),
                
                # 杀号
                'kill_numbers': kill_numbers,
                
                # 预测来源
                'prediction_sources': {
                    'base_predictor': True,
                    'neural_network': len(neural_predictions) > 0,
                    'adaptive_system': True,
                    'dynamic_generator': True
                },
                
                # 置信度信息
                'confidence_scores': {
                    'red_odd_even': adaptive_predictions.get('red_odd_even_prediction', ('3:2', 0.5))[1],
                    'red_size': adaptive_predictions.get('red_size_prediction', ('2:3', 0.5))[1],
                    'blue_size': adaptive_predictions.get('blue_size_prediction', ('1:1', 0.5))[1]
                },
                
                # 系统权重
                'current_weights': self.adaptive_system.get_current_weights()
            }
            
            # 记录预测历史
            self.prediction_history.append(final_prediction)
            
            print("✅ 预测完成!")
            return final_prediction
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            raise
    
    def update_with_actual_result(self, period_num: int, red_balls: List[int], 
                                blue_balls: List[int]):
        """
        使用实际开奖结果更新系统
        
        Args:
            period_num: 期号
            red_balls: 红球号码
            blue_balls: 蓝球号码
        """
        print(f"\n📊 更新系统 - 期号: {period_num}")
        
        # 查找对应的预测结果
        corresponding_prediction = None
        for pred in self.prediction_history:
            if abs(pred['period_index'] - (len(self.prediction_history) - 1)) < 2:
                corresponding_prediction = pred
                break
        
        # 更新自适应学习系统
        predictions_for_adaptive = None
        if corresponding_prediction:
            predictions_for_adaptive = {
                'red_odd_even': corresponding_prediction['red_odd_even_prediction'][0],
                'red_size': corresponding_prediction['red_size_prediction'][0],
                'blue_size': corresponding_prediction['blue_size_prediction'][0],
                'predicted_numbers': corresponding_prediction['generated_numbers']
            }
        
        self.adaptive_system.update_with_new_result(
            period_num, red_balls, blue_balls, predictions_for_adaptive
        )
        
        # 更新性能指标
        if corresponding_prediction:
            self._update_performance_metrics(
                red_balls, blue_balls, corresponding_prediction
            )
        
        # 保存学习状态
        self.adaptive_system.save_learning_state("adaptive_learning_state.json")
        
        print("✅ 系统更新完成")
    
    def _update_performance_metrics(self, actual_red: List[int], actual_blue: List[int], 
                                  prediction: Dict):
        """更新性能指标"""
        self.performance_metrics['total_predictions'] += 1
        
        # 检查状态预测
        actual_odd = sum(1 for x in actual_red if x % 2 == 1)
        actual_red_odd_even = f"{actual_odd}:{5-actual_odd}"
        if prediction['red_odd_even_prediction'][0] == actual_red_odd_even:
            self.performance_metrics['red_odd_even_hits'] += 1
        
        actual_small = sum(1 for x in actual_red if x <= 18)
        actual_red_size = f"{actual_small}:{5-actual_small}"
        if prediction['red_size_prediction'][0] == actual_red_size:
            self.performance_metrics['red_size_hits'] += 1
        
        actual_blue_small = sum(1 for x in actual_blue if x <= 6)
        actual_blue_size = f"{actual_blue_small}:{2-actual_blue_small}"
        if prediction['blue_size_prediction'][0] == actual_blue_size:
            self.performance_metrics['blue_size_hits'] += 1
        
        # 检查2+1命中
        pred_red, pred_blue = prediction['generated_numbers']
        if check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue)):
            self.performance_metrics['hit_2_plus_1'] += 1
    
    def get_system_status(self) -> Dict:
        """获取系统状态"""
        status = {
            'is_initialized': self.is_initialized,
            'model_version': self.model_version,
            'last_update': self.last_update,
            'components_status': {
                'data_manager': len(self.data_manager.extended_data) if self.data_manager.extended_data is not None else 0,
                'neural_predictor': self.neural_predictor.is_trained,
                'adaptive_system': len(self.adaptive_system.recent_data),
                'dynamic_generator': True
            },
            'performance_metrics': self.performance_metrics.copy(),
            'prediction_count': len(self.prediction_history)
        }
        
        # 计算命中率
        if self.performance_metrics['total_predictions'] > 0:
            total = self.performance_metrics['total_predictions']
            status['hit_rates'] = {
                'red_odd_even': self.performance_metrics['red_odd_even_hits'] / total,
                'red_size': self.performance_metrics['red_size_hits'] / total,
                'blue_size': self.performance_metrics['blue_size_hits'] / total,
                'hit_2_plus_1': self.performance_metrics['hit_2_plus_1'] / total
            }
        
        return status
    
    def run_comprehensive_backtest(self, num_periods: int = 20) -> Dict:
        """
        运行综合回测
        
        Args:
            num_periods: 回测期数
            
        Returns:
            Dict: 回测结果
        """
        print(f"\n🧪 开始综合回测 ({num_periods} 期)...")
        
        data = load_data()
        if len(data) < num_periods + 10:
            raise ValueError("数据不足以进行回测")
        
        results = []
        
        for i in range(num_periods):
            try:
                print(f"\n回测第 {i+1}/{num_periods} 期...")
                
                # 预测
                prediction = self.predict_next_period(i)
                
                # 获取实际结果
                actual_row = data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                
                # 评估预测
                evaluation = self._evaluate_single_prediction(
                    prediction, actual_red, actual_blue
                )
                
                results.append({
                    'period_index': i,
                    'period_num': actual_row['期号'],
                    'prediction': prediction,
                    'actual': (actual_red, actual_blue),
                    'evaluation': evaluation
                })
                
                # 更新系统
                self.update_with_actual_result(actual_row['期号'], actual_red, actual_blue)
                
            except Exception as e:
                print(f"回测第 {i+1} 期失败: {e}")
                continue
        
        # 计算总体统计
        overall_stats = self._calculate_backtest_stats(results)
        
        print(f"\n✅ 综合回测完成!")
        return {
            'results': results,
            'overall_stats': overall_stats,
            'system_status': self.get_system_status()
        }
    
    def _evaluate_single_prediction(self, prediction: Dict, actual_red: List[int], 
                                   actual_blue: List[int]) -> Dict:
        """评估单次预测"""
        evaluation = {}
        
        # 状态预测评估
        actual_odd = sum(1 for x in actual_red if x % 2 == 1)
        actual_red_odd_even = f"{actual_odd}:{5-actual_odd}"
        evaluation['red_odd_even_hit'] = prediction['red_odd_even_prediction'][0] == actual_red_odd_even
        
        actual_small = sum(1 for x in actual_red if x <= 18)
        actual_red_size = f"{actual_small}:{5-actual_small}"
        evaluation['red_size_hit'] = prediction['red_size_prediction'][0] == actual_red_size
        
        actual_blue_small = sum(1 for x in actual_blue if x <= 6)
        actual_blue_size = f"{actual_blue_small}:{2-actual_blue_small}"
        evaluation['blue_size_hit'] = prediction['blue_size_prediction'][0] == actual_blue_size
        
        # 号码预测评估
        pred_red, pred_blue = prediction['generated_numbers']
        red_hits = len(set(pred_red) & set(actual_red))
        blue_hits = len(set(pred_blue) & set(actual_blue))
        
        evaluation['red_hits'] = red_hits
        evaluation['blue_hits'] = blue_hits
        evaluation['hit_2_plus_1'] = check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue))
        
        return evaluation
    
    def _calculate_backtest_stats(self, results: List[Dict]) -> Dict:
        """计算回测统计"""
        if not results:
            return {}
        
        total = len(results)
        
        stats = {
            'total_periods': total,
            'red_odd_even_rate': sum(r['evaluation']['red_odd_even_hit'] for r in results) / total,
            'red_size_rate': sum(r['evaluation']['red_size_hit'] for r in results) / total,
            'blue_size_rate': sum(r['evaluation']['blue_size_hit'] for r in results) / total,
            'hit_2_plus_1_rate': sum(r['evaluation']['hit_2_plus_1'] for r in results) / total,
            'avg_red_hits': np.mean([r['evaluation']['red_hits'] for r in results]),
            'avg_blue_hits': np.mean([r['evaluation']['blue_hits'] for r in results])
        }
        
        # 综合命中率
        stats['overall_rate'] = (stats['red_odd_even_rate'] + stats['red_size_rate'] + stats['blue_size_rate']) / 3
        
        return stats


def test_advanced_system():
    """测试高级预测系统"""
    system = AdvancedPredictionSystem()
    
    # 初始化系统
    if not system.initialize_system(retrain_neural=False, neural_epochs=50):
        print("系统初始化失败")
        return
    
    # 运行小规模回测
    try:
        backtest_results = system.run_comprehensive_backtest(num_periods=5)
        
        print("\n📊 回测结果:")
        stats = backtest_results['overall_stats']
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.1%}")
            else:
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"回测失败: {e}")


if __name__ == "__main__":
    test_advanced_system()
