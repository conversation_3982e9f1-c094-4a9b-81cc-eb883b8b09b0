#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试期号直接调用杀号预测功能
"""

import sys
import os
sys.path.append('src/systems')

from src.systems.main import LotteryPredictor

def test_period_prediction():
    """测试期号直接调用功能"""
    print("🎯 测试期号直接调用杀号预测功能")
    print("=" * 60)
    
    # 初始化系统
    system = LotteryPredictor()
    
    # 测试几个期号
    test_periods = ["25068", "25067", "25066", "25065", "25064"]
    
    for period in test_periods:
        print(f"\n📅 测试期号: {period}")
        print("-" * 40)
        
        try:
            # 调用期号预测功能
            result = system.predict_kill_numbers_by_period(period)
            
            print(f"✅ 预测成功:")
            print(f"  红球杀号: {result['red_universal']}")
            print(f"  蓝球杀号: {result['blue_universal']}")
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")

if __name__ == "__main__":
    test_period_prediction()
