"""
测试马尔科夫模型
"""

from utils import load_data
from analyzer import LotteryAnalyzer
from markov_model import MarkovModel

def test_markov():
    """测试马尔科夫模型"""
    print("开始测试马尔科夫模型...")
    
    try:
        # 加载数据
        data = load_data()
        analyzer = LotteryAnalyzer(data)
        
        # 创建马尔科夫模型
        red_markov = MarkovModel('red')
        print("红球马尔科夫模型创建成功")
        
        # 训练模型
        red_odd_even_sequence = analyzer.get_feature_sequence('red_odd_even')
        print(f"训练序列长度: {len(red_odd_even_sequence)}")
        print(f"训练序列前10期: {red_odd_even_sequence[:10]}")
        
        red_markov.train(red_odd_even_sequence)
        print("模型训练完成")
        
        # 测试预测
        current_state = red_odd_even_sequence[0]  # 最新状态
        predicted_state, probability = red_markov.predict_next_state(current_state)
        print(f"当前状态: {current_state}")
        print(f"预测下一状态: {predicted_state}, 概率: {probability:.3f}")
        
        # 测试状态概率
        state_probs = red_markov.get_state_probabilities(current_state)
        print(f"所有状态概率: {state_probs}")
        
        print("\n马尔科夫模型测试完成!")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_markov()
