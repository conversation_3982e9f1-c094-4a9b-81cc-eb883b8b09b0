"""
基于数据洞察的号码生成器
重点关注杀号准确性和蓝球预测优势
"""

import numpy as np
from typing import List, Tuple, Dict, Set
from collections import Counter, defaultdict
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, state_to_ratio
)


class InsightBasedGenerator:
    """基于洞察的号码生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
        
        # 基于数据分析的策略权重
        self.strategy_weights = {
            'frequency_based': 0.4,     # 基于号码频率（最可靠）
            'kill_based': 0.3,          # 基于杀号（92%成功率）
            'state_based': 0.2,         # 基于状态预测（降低权重）
            'trend_based': 0.1          # 基于趋势分析
        }
        
        # 特征置信度（基于数据分析结果）
        self.feature_confidence = {
            'blue_size': 0.473,         # 蓝球相对可预测
            'red_odd_even': 0.236,      # 红球难预测
            'red_size': 0.210           # 红球最难预测
        }
    
    def generate_numbers_with_insights(self, 
                                     red_odd_even_state: str,
                                     red_size_state: str,
                                     blue_size_state: str,
                                     historical_data: List[Tuple[List[int], List[int]]] = None,
                                     kill_numbers: Dict[str, List[List[int]]] = None,
                                     seed: int = 0) -> Tuple[List[int], List[int]]:
        """
        基于洞察生成号码
        
        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            historical_data: 历史数据
            kill_numbers: 杀号列表
            seed: 随机种子
            
        Returns:
            Tuple[List[int], List[int]]: (红球, 蓝球)
        """
        # 分析历史数据
        if historical_data:
            self._analyze_patterns(historical_data)
        
        # 生成红球（多策略融合）
        red_balls = self._generate_red_balls_multi_strategy(
            red_odd_even_state, red_size_state, historical_data, kill_numbers, seed
        )
        
        # 生成蓝球（重点优化，因为更可预测）
        blue_balls = self._generate_blue_balls_optimized(
            blue_size_state, historical_data, kill_numbers, seed
        )
        
        return red_balls, blue_balls
    
    def _analyze_patterns(self, historical_data: List[Tuple[List[int], List[int]]]) -> None:
        """分析历史模式"""
        if not historical_data:
            return
        
        # 分析号码频率
        self.red_frequencies = Counter()
        self.blue_frequencies = Counter()
        
        # 分析最近趋势（最近20期权重更高）
        for i, (red_balls, blue_balls) in enumerate(historical_data[:20]):
            weight = np.exp(-0.05 * i)  # 时间衰减权重
            
            for num in red_balls:
                self.red_frequencies[num] += weight
            for num in blue_balls:
                self.blue_frequencies[num] += weight
        
        # 分析冷热号码
        self.red_hot_numbers = [num for num, _ in self.red_frequencies.most_common(10)]
        self.red_cold_numbers = [num for num in self.red_range if self.red_frequencies[num] < np.mean(list(self.red_frequencies.values()))]
        
        self.blue_hot_numbers = [num for num, _ in self.blue_frequencies.most_common(6)]
        self.blue_cold_numbers = [num for num in self.blue_range if self.blue_frequencies[num] < np.mean(list(self.blue_frequencies.values()))]
    
    def _generate_red_balls_multi_strategy(self, 
                                         odd_even_state: str,
                                         size_state: str,
                                         historical_data: List[Tuple[List[int], List[int]]],
                                         kill_numbers: Dict[str, List[List[int]]],
                                         seed: int) -> List[int]:
        """多策略生成红球"""
        np.random.seed(seed)
        
        # 策略1: 基于频率的候选池
        frequency_candidates = self._get_frequency_based_candidates('red', historical_data)

        # 策略1.5: 应用通用杀号过滤（重要！）
        kill_filtered_candidates = self._apply_kill_numbers(frequency_candidates, kill_numbers, 'red')

        # 策略2: 基于位置杀号的候选池（按位置精确杀号）
        position_candidates = self._apply_position_kill_numbers(kill_filtered_candidates, kill_numbers, 'red')
        
        # 策略3和4: 位置感知的候选池已经准备好，直接使用
        
        # 生成最终组合 - 使用位置感知选择
        red_balls = self._select_position_aware_combination(
            position_candidates, odd_even_state, size_state, seed
        )
        
        return sorted(red_balls)
    
    def _generate_blue_balls_optimized(self, 
                                     size_state: str,
                                     historical_data: List[Tuple[List[int], List[int]]],
                                     kill_numbers: Dict[str, List[List[int]]],
                                     seed: int) -> List[int]:
        """优化的蓝球生成（重点关注，因为更可预测）"""
        np.random.seed(seed + 100)
        
        # 蓝球预测置信度高，更依赖状态预测
        small_count, big_count = state_to_ratio(size_state)
        
        # 基于频率的候选池
        frequency_candidates = self._get_frequency_based_candidates('blue', historical_data)
        
        # 应用杀号
        kill_filtered_candidates = self._apply_kill_numbers(frequency_candidates, kill_numbers, 'blue')
        
        # 按大小分类
        small_candidates = [n for n in kill_filtered_candidates if 1 <= n <= 6]
        big_candidates = [n for n in kill_filtered_candidates if 7 <= n <= 12]
        
        # 智能选择（基于频率权重）
        selected = []
        
        # 选择小号
        if small_count > 0 and small_candidates:
            weights = [self.blue_frequencies.get(num, 1) for num in small_candidates]
            total_weight = sum(weights)
            if total_weight > 0:
                probs = [w / total_weight for w in weights]
                selected_small = np.random.choice(
                    small_candidates, 
                    size=min(small_count, len(small_candidates)), 
                    replace=False, 
                    p=probs
                )
                selected.extend(selected_small)
        
        # 选择大号
        if big_count > 0 and big_candidates:
            remaining_candidates = [n for n in big_candidates if n not in selected]
            if remaining_candidates:
                weights = [self.blue_frequencies.get(num, 1) for num in remaining_candidates]
                total_weight = sum(weights)
                if total_weight > 0:
                    probs = [w / total_weight for w in weights]
                    selected_big = np.random.choice(
                        remaining_candidates, 
                        size=min(big_count, len(remaining_candidates)), 
                        replace=False, 
                        p=probs
                    )
                    selected.extend(selected_big)
        
        # 如果数量不足，补充
        if len(selected) < 2:
            remaining = [n for n in kill_filtered_candidates if n not in selected]
            needed = 2 - len(selected)
            if remaining:
                weights = [self.blue_frequencies.get(num, 1) for num in remaining]
                total_weight = sum(weights)
                if total_weight > 0:
                    probs = [w / total_weight for w in weights]
                    additional = np.random.choice(
                        remaining, 
                        size=min(needed, len(remaining)), 
                        replace=False, 
                        p=probs
                    )
                    selected.extend(additional)
        
        return sorted(selected[:2])
    
    def _get_frequency_based_candidates(self, ball_type: str, historical_data: List[Tuple[List[int], List[int]]]) -> List[int]:
        """基于频率获取候选号码"""
        if ball_type == 'red':
            # 红球：平衡热号和冷号
            if hasattr(self, 'red_hot_numbers') and hasattr(self, 'red_cold_numbers'):
                # 70%热号 + 30%冷号的策略
                hot_count = int(len(self.red_hot_numbers) * 0.7)
                cold_count = int(len(self.red_cold_numbers) * 0.3)
                
                candidates = self.red_hot_numbers[:hot_count] + self.red_cold_numbers[:cold_count]
                # 补充到足够数量
                remaining = [n for n in self.red_range if n not in candidates]
                candidates.extend(remaining[:max(0, 25 - len(candidates))])
                return candidates
            else:
                return self.red_range
        else:
            # 蓝球：更依赖频率分析
            if hasattr(self, 'blue_hot_numbers'):
                # 优先选择热号
                return self.blue_hot_numbers + [n for n in self.blue_range if n not in self.blue_hot_numbers]
            else:
                return self.blue_range
    
    def _apply_position_kill_numbers(self, candidates: List[int], kill_numbers: Dict[str, List[List[int]]], ball_type: str) -> Dict[int, List[int]]:
        """
        按位置应用杀号 - 真正的位置感知杀号

        Args:
            candidates: 候选号码列表
            kill_numbers: 杀号字典
            ball_type: 球类型 ('red' or 'blue')

        Returns:
            Dict[int, List[int]]: {位置: 该位置的候选号码列表}
        """
        if ball_type == 'red':
            position_count = 5
            number_range = range(1, 36)
        else:
            position_count = 2
            number_range = range(1, 13)

        # 初始化每个位置的候选池
        position_candidates = {}
        for pos in range(1, position_count + 1):
            position_candidates[pos] = list(candidates)

        # 应用位置杀号
        if kill_numbers and ball_type in kill_numbers:
            kill_lists = kill_numbers[ball_type]

            for pos, kill_list in enumerate(kill_lists, 1):
                if pos <= position_count and kill_list:
                    # 从该位置的候选池中移除杀号
                    position_candidates[pos] = [n for n in position_candidates[pos] if n not in kill_list]

                    # 如果候选池过小，补充一些安全号码
                    if len(position_candidates[pos]) < 10:
                        safe_numbers = [n for n in number_range if n not in kill_list and n not in position_candidates[pos]]
                        position_candidates[pos].extend(safe_numbers[:10 - len(position_candidates[pos])])

        return position_candidates

    def _apply_kill_filter(self, candidates: List[int], kill_numbers: Dict[str, List[List[int]]], ball_type: str) -> List[int]:
        """应用杀号过滤（标准接口）"""
        return self._apply_kill_numbers(candidates, kill_numbers, ball_type)

    def _apply_kill_numbers(self, candidates: List[int], kill_numbers: Dict[str, List[List[int]]], ball_type: str) -> List[int]:
        """应用杀号（92%成功率，重点策略）- 保留原方法用于蓝球"""
        if not kill_numbers or ball_type not in kill_numbers:
            return candidates

        filtered = set(candidates)

        # 移除所有杀号
        for kill_list in kill_numbers[ball_type]:
            filtered -= set(kill_list)

        return list(filtered)
    
    def _filter_by_state(self, candidates: List[int], odd_even_state: str, size_state: str, ball_type: str) -> List[int]:
        """基于状态筛选候选号码"""
        if ball_type == 'red':
            # 红球状态预测置信度低，放宽筛选条件
            return candidates  # 暂时不做严格筛选
        else:
            # 蓝球状态预测置信度高，严格按状态筛选
            small_count, big_count = state_to_ratio(size_state)
            
            small_candidates = [n for n in candidates if 1 <= n <= 6]
            big_candidates = [n for n in candidates if 7 <= n <= 12]
            
            # 确保有足够的候选号码满足状态要求
            if len(small_candidates) >= small_count and len(big_candidates) >= big_count:
                return candidates
            else:
                # 如果候选不足，放宽限制
                return candidates
    
    def _apply_trend_adjustment(self, candidates: List[int], ball_type: str) -> List[int]:
        """基于趋势调整候选池"""
        # 基于最近期数的分布调整
        return candidates  # 简化实现
    
    def _select_optimal_combination(self, candidates: List[int], 
                                  odd_even_state: str, 
                                  size_state: str, 
                                  ball_type: str, 
                                  seed: int) -> List[int]:
        """选择最优组合"""
        if ball_type == 'red':
            return self._select_red_combination(candidates, odd_even_state, size_state, seed)
        else:
            return candidates[:2]  # 蓝球已在前面处理
    
    def _select_red_combination(self, candidates: List[int], 
                              odd_even_state: str, 
                              size_state: str, 
                              seed: int) -> List[int]:
        """选择红球组合"""
        if len(candidates) < 5:
            # 候选不足，补充
            remaining = [n for n in self.red_range if n not in candidates]
            candidates.extend(remaining[:5 - len(candidates)])
        
        np.random.seed(seed)
        
        # 解析目标状态
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)
        
        # 多次尝试生成满足条件的组合
        best_combination = None
        best_score = -1
        
        for attempt in range(20):  # 减少尝试次数，提高效率
            try:
                # 按奇偶和大小分类
                odd_candidates = [n for n in candidates if n % 2 == 1]
                even_candidates = [n for n in candidates if n % 2 == 0]
                small_candidates = [n for n in candidates if 1 <= n <= 18]
                big_candidates = [n for n in candidates if 19 <= n <= 35]
                
                # 尝试满足状态要求
                selected = []
                
                # 选择奇数
                if odd_count > 0 and odd_candidates:
                    weights = [self.red_frequencies.get(num, 1) for num in odd_candidates]
                    total_weight = sum(weights)
                    if total_weight > 0:
                        probs = [w / total_weight for w in weights]
                        selected_odd = np.random.choice(
                            odd_candidates, 
                            size=min(odd_count, len(odd_candidates)), 
                            replace=False, 
                            p=probs
                        )
                        selected.extend(selected_odd)
                
                # 选择偶数
                if even_count > 0:
                    remaining_even = [n for n in even_candidates if n not in selected]
                    if remaining_even:
                        weights = [self.red_frequencies.get(num, 1) for num in remaining_even]
                        total_weight = sum(weights)
                        if total_weight > 0:
                            probs = [w / total_weight for w in weights]
                            selected_even = np.random.choice(
                                remaining_even, 
                                size=min(even_count, len(remaining_even)), 
                                replace=False, 
                                p=probs
                            )
                            selected.extend(selected_even)
                
                # 如果数量不足5个，随机补充
                if len(selected) < 5:
                    remaining = [n for n in candidates if n not in selected]
                    needed = 5 - len(selected)
                    if remaining:
                        weights = [self.red_frequencies.get(num, 1) for num in remaining]
                        total_weight = sum(weights)
                        if total_weight > 0:
                            probs = [w / total_weight for w in weights]
                            additional = np.random.choice(
                                remaining, 
                                size=min(needed, len(remaining)), 
                                replace=False, 
                                p=probs
                            )
                            selected.extend(additional)
                
                if len(set(selected)) == 5:
                    # 评估组合质量
                    score = self._evaluate_combination(selected)
                    if score > best_score:
                        best_score = score
                        best_combination = list(set(selected))
                        
            except Exception as e:
                continue
        
        # 如果没有找到好的组合，使用简单策略
        if not best_combination or len(best_combination) < 5:
            np.random.seed(seed)
            best_combination = list(np.random.choice(candidates, size=min(5, len(candidates)), replace=False))
            
            # 补充到5个
            while len(best_combination) < 5:
                remaining = [n for n in self.red_range if n not in best_combination]
                if remaining:
                    best_combination.append(np.random.choice(remaining))
                else:
                    break
        
        return best_combination[:5]
    
    def _evaluate_combination(self, combination: List[int]) -> float:
        """评估组合质量"""
        if len(set(combination)) != 5:
            return 0
        
        score = 0
        
        # 和值评分
        total_sum = sum(combination)
        if 80 <= total_sum <= 120:
            score += 20
        
        # 跨度评分
        span = max(combination) - min(combination)
        if 15 <= span <= 25:
            score += 15
        
        # 频率评分
        if hasattr(self, 'red_frequencies'):
            freq_score = sum(self.red_frequencies.get(num, 1) for num in combination)
            score += freq_score / 100
        
        return score

    def _select_position_aware_combination(self, position_candidates: Dict[int, List[int]],
                                         odd_even_state: str,
                                         size_state: str,
                                         seed: int) -> List[int]:
        """
        位置感知的红球组合选择

        Args:
            position_candidates: {位置: 候选号码列表}
            odd_even_state: 奇偶比状态
            size_state: 大小比状态
            seed: 随机种子

        Returns:
            List[int]: 选中的红球号码
        """
        np.random.seed(seed)

        # 解析目标状态
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)

        selected = []

        # 按位置选择号码
        for pos in range(1, 6):
            if pos in position_candidates and position_candidates[pos]:
                candidates = position_candidates[pos]

                # 基于位置特性和状态要求选择
                if hasattr(self, 'red_frequencies'):
                    # 使用频率权重选择
                    weights = [self.red_frequencies.get(num, 1) for num in candidates]
                    total_weight = sum(weights)
                    if total_weight > 0:
                        probs = [w / total_weight for w in weights]
                        chosen = np.random.choice(candidates, p=probs)
                        selected.append(chosen)
                    else:
                        selected.append(np.random.choice(candidates))
                else:
                    selected.append(np.random.choice(candidates))
            else:
                # 如果该位置没有候选，从全局范围选择
                remaining = [n for n in range(1, 36) if n not in selected]
                if remaining:
                    selected.append(np.random.choice(remaining))

        # 确保选择了5个不同的号码
        selected = list(set(selected))

        # 如果不足5个，补充
        while len(selected) < 5:
            remaining = [n for n in range(1, 36) if n not in selected]
            if remaining:
                selected.append(np.random.choice(remaining))
            else:
                break

        return sorted(selected[:5])
