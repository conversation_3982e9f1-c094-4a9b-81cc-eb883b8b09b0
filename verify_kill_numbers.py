#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证主程序和advanced_probabilistic_system中生成的杀号是否一致
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入两个系统
from src.systems.main import LotteryPredictor
from advanced_probabilistic_system import AdvancedProbabilisticSystem

def compare_kill_numbers():
    """对比两个系统生成的杀号"""
    print("🔍 验证主程序和advanced_probabilistic_system的杀号一致性")
    print("=" * 70)
    
    # 初始化两个系统
    main_predictor = LotteryPredictor()
    advanced_system = AdvancedProbabilisticSystem()
    
    # 加载数据
    if not advanced_system.load_data():
        print("❌ advanced_probabilistic_system数据加载失败")
        return
    
    advanced_system.initialize_system()
    
    # 测试几个期号
    test_periods = ["25068", "25067", "25066"]
    
    for period in test_periods:
        print(f"\n📅 测试期号: {period}")
        print("-" * 50)
        
        try:
            # 方法1：使用主程序的期号调用
            main_result = main_predictor.predict_kill_numbers_by_period(period)
            
            print(f"🎯 主程序杀号结果:")
            print(f"  红球杀号: {main_result['red_universal']}")
            print(f"  蓝球杀号: {main_result['blue_universal']}")
            
            # 方法2：直接使用advanced_probabilistic_system
            # 找到期号对应的数据
            period_index = None
            for i, row in advanced_system.data.iterrows():
                if str(row['期号']) == str(period):
                    period_index = i
                    break
            
            if period_index is None:
                print(f"❌ 在advanced_probabilistic_system中未找到期号 {period}")
                continue
            
            # 获取训练数据（与主程序保持一致）
            train_data = advanced_system.data.iloc[period_index + 1:period_index + 301]
            
            # 构建period_data
            period_data = {
                'current': train_data.iloc[0],
                'last': train_data.iloc[1],
                'prev2': train_data.iloc[2],
                'prev3': train_data.iloc[3],
                'prev4': train_data.iloc[4],
                'prev5': train_data.iloc[5]
            }
            
            # 重新初始化系统（使用相同的训练数据）
            advanced_system.data = train_data
            advanced_system.initialize_system()
            
            # 获取杀号
            red_kills_full = advanced_system.ensemble_system.predict_ensemble_kills(period_data, target_count=5)
            
            # 过滤掉前两期出现的号码（与主程序逻辑一致）
            from src.utils.utils import parse_numbers
            period1_red, _ = parse_numbers(period_data['current'])
            period2_red, _ = parse_numbers(period_data['last'])
            red_kills_filtered = [k for k in red_kills_full if k not in (period1_red + period2_red) and 1 <= k <= 35]
            
            # 蓝球杀号（简化策略）
            from collections import Counter
            recent_blues = []
            for key in ['current', 'last', 'prev2']:
                if key in period_data:
                    _, blue_balls = parse_numbers(period_data[key])
                    recent_blues.extend(blue_balls)
            
            blue_freq = Counter(recent_blues)
            all_blues = list(range(1, 13))
            min_freq = min(blue_freq.get(b, 0) for b in all_blues)
            candidates = [b for b in all_blues if blue_freq.get(b, 0) == min_freq]
            blue_kills = [candidates[0]] if candidates else [12]
            
            print(f"🔬 Advanced系统杀号结果:")
            print(f"  红球杀号: {red_kills_filtered}")
            print(f"  蓝球杀号: {blue_kills}")
            
            # 对比结果
            red_match = set(main_result['red_universal']) == set(red_kills_filtered)
            blue_match = set(main_result['blue_universal']) == set(blue_kills)
            
            print(f"\n✅ 对比结果:")
            print(f"  红球杀号一致: {'✅' if red_match else '❌'}")
            print(f"  蓝球杀号一致: {'✅' if blue_match else '❌'}")
            
            if not red_match:
                print(f"  红球差异: 主程序{main_result['red_universal']} vs Advanced{red_kills_filtered}")
            
            if not blue_match:
                print(f"  蓝球差异: 主程序{main_result['blue_universal']} vs Advanced{blue_kills}")
            
        except Exception as e:
            print(f"❌ 期号 {period} 对比失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎉 杀号一致性验证完成！")

if __name__ == "__main__":
    compare_kill_numbers()
