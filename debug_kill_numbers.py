#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试杀号数据传递和应用
"""

from src.systems.main import LotteryPredictor
from src.generators.precision_generator import PrecisionGenerator
import pandas as pd

def debug_kill_numbers():
    """调试杀号数据传递"""
    print("🔍 调试杀号数据传递和应用")
    print("=" * 60)
    
    # 初始化预测系统
    predictor = LotteryPredictor()
    
    # 进行一次预测并捕获杀号数据
    print("\n📊 获取杀号数据...")
    prediction = predictor.predict_next_period(0)
    
    kill_numbers = prediction['kill_numbers']
    print(f"杀号数据结构: {kill_numbers}")
    print(f"红球杀号: {kill_numbers.get('red_universal', [])}")
    print(f"蓝球杀号: {kill_numbers.get('blue_universal', [])}")
    
    # 测试生成器是否正确应用杀号
    print(f"\n🧪 测试生成器杀号应用...")
    
    # 构造测试杀号数据
    test_kill_numbers = {
        'red': [kill_numbers.get('red_universal', [])],
        'blue': [kill_numbers.get('blue_universal', [])]
    }
    
    print(f"测试杀号格式: {test_kill_numbers}")
    
    # 测试精准生成器
    precision_gen = PrecisionGenerator()
    
    print(f"\n📈 测试精准生成器...")
    for i in range(3):
        red, blue = precision_gen.generate_precision_numbers(
            "3:2", "2:3", "1:1", test_kill_numbers, seed=i
        )
        
        print(f"第{i+1}次生成:")
        print(f"  红球: {red}")
        print(f"  蓝球: {blue}")
        
        # 检查是否包含被杀号码
        red_kills = kill_numbers.get('red_universal', [])
        blue_kills = kill_numbers.get('blue_universal', [])
        
        red_violations = [num for num in red if num in red_kills]
        blue_violations = [num for num in blue if num in blue_kills]
        
        print(f"  红球违规: {red_violations} (应该为空)")
        print(f"  蓝球违规: {blue_violations} (应该为空)")
        
        if red_violations or blue_violations:
            print(f"  ❌ 杀号未生效！")
        else:
            print(f"  ✅ 杀号正常生效")

def test_kill_filter_directly():
    """直接测试杀号过滤函数"""
    print(f"\n🔧 直接测试杀号过滤函数...")
    
    precision_gen = PrecisionGenerator()
    
    # 测试数据
    candidates = list(range(1, 36))  # 红球候选
    kill_numbers = {
        'red': [[7, 24, 4, 13, 17, 22, 31, 35]],  # 模拟杀号
        'blue': [[9, 12, 3]]
    }
    
    print(f"原始候选: {candidates[:10]}...{candidates[-5:]}")
    print(f"杀号数据: {kill_numbers}")
    
    # 应用杀号过滤
    filtered = precision_gen._apply_kill_filter(candidates, kill_numbers, 'red')
    
    print(f"过滤后候选: {filtered[:10]}...{filtered[-5:]}")
    print(f"过滤前数量: {len(candidates)}")
    print(f"过滤后数量: {len(filtered)}")
    
    # 检查被杀号码是否还在
    killed_numbers = kill_numbers['red'][0]
    remaining_killed = [num for num in killed_numbers if num in filtered]
    
    print(f"被杀号码: {killed_numbers}")
    print(f"仍在候选中的被杀号码: {remaining_killed}")
    
    if remaining_killed:
        print(f"❌ 杀号过滤失败！")
    else:
        print(f"✅ 杀号过滤成功")

def check_kill_data_format():
    """检查杀号数据格式"""
    print(f"\n📋 检查杀号数据格式...")
    
    predictor = LotteryPredictor()
    
    # 获取训练数据
    train_data = predictor.data.iloc[1:101]  # 使用第2-101期作为训练数据
    
    # 直接调用杀号预测函数
    kill_result = predictor._universal_kill_prediction(train_data)
    
    print(f"杀号预测结果: {kill_result}")
    print(f"红球杀号类型: {type(kill_result.get('red_universal', []))}")
    print(f"蓝球杀号类型: {type(kill_result.get('blue_universal', []))}")
    
    # 检查主系统如何转换杀号格式
    print(f"\n🔄 检查主系统杀号格式转换...")
    
    # 模拟主系统的杀号格式转换
    formatted_kill_numbers = {
        'red_universal': kill_result.get('red_universal', []),
        'blue_universal': kill_result.get('blue_universal', [])
    }
    
    print(f"主系统杀号格式: {formatted_kill_numbers}")
    
    # 检查生成器期望的格式
    expected_format = {
        'red': [kill_result.get('red_universal', [])],
        'blue': [kill_result.get('blue_universal', [])]
    }
    
    print(f"生成器期望格式: {expected_format}")
    
    # 这里可能是问题所在！
    if formatted_kill_numbers != expected_format:
        print(f"⚠️  格式不匹配！主系统使用的格式与生成器期望的不同")
        return formatted_kill_numbers, expected_format
    else:
        print(f"✅ 格式匹配")
        return None, None

def trace_kill_number_flow():
    """追踪杀号数据流"""
    print(f"\n🔍 追踪杀号数据流...")
    
    predictor = LotteryPredictor()
    
    # 步骤1: 获取杀号预测
    train_data = predictor.data.iloc[1:101]
    kill_prediction = predictor._universal_kill_prediction(train_data)
    print(f"步骤1 - 杀号预测: {kill_prediction}")
    
    # 步骤2: 主系统格式化
    # 查看主系统如何处理杀号数据
    print(f"\n步骤2 - 查看主系统predict_next_period中的杀号处理...")
    
    # 模拟主系统的处理过程
    prediction = predictor.predict_next_period(0)
    actual_kill_numbers = prediction['kill_numbers']
    print(f"实际传递给生成器的杀号: {actual_kill_numbers}")
    
    # 步骤3: 检查生成器接收到的数据
    print(f"\n步骤3 - 检查生成器接收的数据格式...")
    
    # 检查是否有格式转换问题
    if 'red_universal' in actual_kill_numbers:
        print(f"发现问题：杀号使用'red_universal'键，但生成器期望'red'键")
        
        # 正确的格式应该是
        correct_format = {
            'red': [actual_kill_numbers.get('red_universal', [])],
            'blue': [actual_kill_numbers.get('blue_universal', [])]
        }
        print(f"正确格式应该是: {correct_format}")
        
        return True  # 发现格式问题
    
    return False  # 没有发现格式问题

def main():
    """主函数"""
    debug_kill_numbers()
    test_kill_filter_directly()
    wrong_format, correct_format = check_kill_data_format()
    has_format_issue = trace_kill_number_flow()
    
    print(f"\n🎯 调试总结:")
    if has_format_issue or wrong_format:
        print(f"❌ 发现杀号数据格式问题！")
        print(f"   主系统使用: 'red_universal', 'blue_universal'")
        print(f"   生成器期望: 'red', 'blue'")
        print(f"   这导致杀号过滤失效")
    else:
        print(f"✅ 杀号数据格式正常")

if __name__ == "__main__":
    main()
