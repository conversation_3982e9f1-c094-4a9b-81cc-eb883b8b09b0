#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
对比主程序和kill_count_analysis_system的回测逻辑
"""

import pandas as pd
from src.systems.main import LotteryPredictor

def analyze_main_backtest_logic():
    """分析主程序回测逻辑"""
    print("🎯 主程序回测逻辑分析")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    
    print("📋 主程序回测特征:")
    print("  1. 回测方法: run_backtest()")
    print("  2. 默认回测期数: 10期")
    print("  3. 显示期数: 10期")
    print("  4. 训练数据: 每期使用200期历史数据")
    print("  5. 数据保留: 最少保留200期作为训练集")
    print()
    
    # 模拟回测逻辑
    num_periods = 10
    min_train_periods = 200
    max_backtest = min(num_periods, len(predictor.data) - min_train_periods)
    
    print(f"📊 回测参数:")
    print(f"  总数据期数: {len(predictor.data)}")
    print(f"  请求回测期数: {num_periods}")
    print(f"  实际回测期数: {max_backtest}")
    print(f"  保留训练期数: {len(predictor.data) - max_backtest}")
    print()
    
    print("🔄 回测流程:")
    print("  1. 确定回测期数范围")
    print("  2. 对每期执行:")
    print("     a. 调用 predict_next_period(i)")
    print("     b. 使用第i+1期之后的200期数据训练")
    print("     c. 预测第i期的号码")
    print("     d. 与实际开奖对比")
    print("     e. 计算各项命中率")
    print("  3. 统计整体表现")
    print("  4. 显示最新期结果")
    print()
    
    print("📈 评估指标:")
    print("  - 红球奇偶比命中率")
    print("  - 红球大小比命中率") 
    print("  - 蓝球大小比命中率")
    print("  - 2+1命中率（三项比值全中）")
    print("  - 红球杀号成功率")
    print("  - 蓝球杀号成功率")
    print()
    
    # 模拟单期回测逻辑
    print("🧪 单期回测逻辑示例 (第1期):")
    
    i = 0  # 回测第1期
    current_period = predictor.data.iloc[i]
    
    print(f"  预测目标: {current_period['期号']}期")
    
    # 训练数据获取
    max_train_periods = 200
    train_start = i + 1
    train_end = min(len(predictor.data), train_start + max_train_periods)
    
    print(f"  训练数据: 第{train_start+1}期到第{train_end}期 (共{train_end-train_start}期)")
    
    if train_end > train_start:
        train_start_period = predictor.data.iloc[train_start]['期号']
        train_end_period = predictor.data.iloc[train_end-1]['期号']
        print(f"  训练期号范围: {train_start_period} 到 {train_end_period}")
    
    print(f"  时间逻辑: 训练数据都在预测目标({current_period['期号']})之前")

def analyze_kill_count_backtest_logic():
    """分析kill_count_analysis_system回测逻辑"""
    print("\n🔬 kill_count_analysis_system回测逻辑分析")
    print("=" * 80)
    
    print("📋 kill_count_analysis_system回测特征:")
    print("  1. 回测方法: _test_specific_kill_count()")
    print("  2. 默认回测期数: 30期")
    print("  3. 测试目标: 不同杀号数量的成功率")
    print("  4. 训练数据: 使用全部历史数据")
    print("  5. 数据格式: period_data字典格式")
    print()
    
    # 模拟kill_count_analysis_system的逻辑
    test_periods = 30
    
    print(f"📊 回测参数:")
    print(f"  测试期数: {test_periods}")
    print(f"  杀号数量范围: 1-15个")
    print(f"  数据要求: 至少需要i+5期数据")
    print()
    
    print("🔄 回测流程:")
    print("  1. 对每个杀号数量(1-15)执行:")
    print("  2. 对每期执行:")
    print("     a. 构造period_data (current到prev5)")
    print("     b. 调用 predict_ensemble_kills()")
    print("     c. 使用全部历史数据训练")
    print("     d. 过滤前两期出现的号码")
    print("     e. 与实际开奖对比")
    print("  3. 统计杀号成功率")
    print("  4. 分析最佳杀号数量")
    print()
    
    print("📈 评估指标:")
    print("  - 全中率 (perfect_rate)")
    print("  - 杀号成功率 (kill_success_rate)")
    print("  - 平均杀号数 (avg_kills)")
    print("  - 成功杀号数统计")
    print()
    
    # 模拟单期回测逻辑
    print("🧪 单期回测逻辑示例 (第1期):")
    
    predictor = LotteryPredictor()
    
    i = 0  # 回测第1期
    if i + 5 < len(predictor.data):
        current_period = predictor.data.iloc[i]
        
        print(f"  预测目标: {current_period['期号']}期")
        
        # period_data构造
        period_data = {
            'current': predictor.data.iloc[i],
            'last': predictor.data.iloc[i + 1],
            'prev2': predictor.data.iloc[i + 2],
            'prev3': predictor.data.iloc[i + 3],
            'prev4': predictor.data.iloc[i + 4],
            'prev5': predictor.data.iloc[i + 5]
        }
        
        print(f"  历史数据: current({period_data['current']['期号']}) 到 prev5({period_data['prev5']['期号']})")
        print(f"  训练数据: 全部{len(predictor.data)}期历史数据")
        print(f"  时间逻辑: 使用6期相邻历史数据")

def compare_key_differences():
    """对比关键差异"""
    print("\n🔍 关键差异对比")
    print("=" * 80)
    
    print("📊 回测参数对比:")
    print("| 项目 | 主程序 | kill_count_analysis |")
    print("|------|--------|---------------------|")
    print("| 回测期数 | 10期 | 30期 |")
    print("| 训练数据 | 200期 | 1500期(全部) |")
    print("| 数据格式 | DataFrame | period_data字典 |")
    print("| 时间窗口 | 滑动200期 | 固定6期 |")
    print()
    
    print("🎯 回测目标对比:")
    print("| 项目 | 主程序 | kill_count_analysis |")
    print("|------|--------|---------------------|")
    print("| 主要目标 | 整体预测性能 | 杀号数量优化 |")
    print("| 评估指标 | 多维度命中率 | 杀号成功率 |")
    print("| 应用场景 | 实际预测验证 | 参数研究 |")
    print()
    
    print("⚙️ 算法逻辑对比:")
    print("| 项目 | 主程序 | kill_count_analysis |")
    print("|------|--------|---------------------|")
    print("| 预测方法 | predict_next_period() | predict_ensemble_kills() |")
    print("| 算法类型 | BayesianMarkovKiller | EnsembleKillSystem |")
    print("| 重新训练 | 每期重新训练 | 一次性训练 |")
    print("| 数据更新 | 滑动窗口 | 静态全量 |")
    print()
    
    print("🔄 时间逻辑对比:")
    print("主程序时间逻辑:")
    print("  预测第i期 → 使用第i+1到i+200期数据训练")
    print("  严格避免未来信息泄露")
    print("  每期都有不同的训练集")
    print()
    
    print("kill_count_analysis时间逻辑:")
    print("  预测第i期 → 使用current到prev5期数据")
    print("  使用全部历史数据训练")
    print("  相邻6期数据窗口")

def analyze_data_flow():
    """分析数据流向"""
    print("\n📊 数据流向分析")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    
    print("🎯 主程序数据流:")
    print("  1. 数据加载: 1500期DataFrame")
    print("  2. 回测循环: for i in range(max_backtest)")
    print("  3. 训练数据: data.iloc[i+1:i+201]")
    print("  4. 预测执行: predict_next_period(i)")
    print("  5. 结果对比: 与data.iloc[i]对比")
    print()
    
    print("🔬 kill_count_analysis数据流:")
    print("  1. 数据加载: 1500期DataFrame")
    print("  2. 系统初始化: EnsembleKillSystem(data)")
    print("  3. 回测循环: for i in range(test_periods)")
    print("  4. 数据构造: period_data字典")
    print("  5. 预测执行: predict_ensemble_kills(period_data)")
    print("  6. 结果对比: 与current期对比")
    print()
    
    print("⚠️ 关键差异:")
    print("  1. 训练数据范围:")
    print("     - 主程序: 动态200期窗口")
    print("     - kill_count: 静态全量数据")
    print()
    print("  2. 数据传递方式:")
    print("     - 主程序: DataFrame切片")
    print("     - kill_count: 字典格式")
    print()
    print("  3. 时间窗口策略:")
    print("     - 主程序: 长期历史(200期)")
    print("     - kill_count: 短期相邻(6期)")

def main():
    """主函数"""
    try:
        analyze_main_backtest_logic()
        analyze_kill_count_backtest_logic()
        compare_key_differences()
        analyze_data_flow()
        
        print("\n🎯 回测逻辑差异总结:")
        print("=" * 80)
        print("✅ 相同点:")
        print("  - 都使用历史数据验证预测效果")
        print("  - 都避免使用未来信息")
        print("  - 都计算成功率统计")
        print()
        print("❌ 不同点:")
        print("  1. 回测规模: 10期 vs 30期")
        print("  2. 训练策略: 滑动窗口 vs 全量数据")
        print("  3. 评估目标: 综合性能 vs 杀号优化")
        print("  4. 数据格式: DataFrame vs 字典")
        print("  5. 时间窗口: 200期 vs 6期")
        print()
        print("🏆 结论:")
        print("  两个系统的回测逻辑完全不同，各有特色：")
        print("  - 主程序: 实用导向，全面评估")
        print("  - kill_count: 研究导向，专项优化")
        
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
