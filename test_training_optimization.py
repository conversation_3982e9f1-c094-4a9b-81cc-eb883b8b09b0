#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试训练期数优化后的逻辑
"""

from src.systems.main import LotteryPredictor

def test_training_optimization():
    """测试训练期数优化"""
    print("🔧 测试训练期数优化后的逻辑")
    print("=" * 80)
    
    # 初始化预测器
    predictor = LotteryPredictor()
    
    # 获取数据信息
    total_periods = len(predictor.data)
    print(f"📊 数据集信息:")
    print(f"  总期数: {total_periods}")
    print(f"  最新期号: {predictor.data.iloc[0]['期号']}")
    print(f"  最早期号: {predictor.data.iloc[-1]['期号']}")
    print()
    
    # 测试新的训练逻辑
    print("🧪 测试新的训练逻辑:")
    
    for i in range(3):  # 测试前3期
        print(f"\n  测试第{i+1}期预测:")
        print(f"    预测目标: {predictor.data.iloc[i]['期号']}期")
        
        # 模拟新的训练数据获取逻辑
        max_train_periods = 200
        train_start = i + 1
        train_end = min(len(predictor.data), train_start + max_train_periods)
        train_data = predictor.data.iloc[train_start:train_end].copy()
        
        print(f"    训练数据范围: 第{train_start+1}期 到 第{train_end}期")
        print(f"    训练期数: {len(train_data)}期")
        
        if len(train_data) > 0:
            print(f"    训练起始期号: {train_data.iloc[0]['期号']}")
            print(f"    训练结束期号: {train_data.iloc[-1]['期号']}")
        
        # 验证训练数据充足性
        if len(train_data) < 10:
            print(f"    ⚠️  训练数据不足")
        elif len(train_data) < 200:
            print(f"    ⚠️  训练数据有限: {len(train_data)}期")
        else:
            print(f"    ✅ 训练数据充足: {len(train_data)}期")

def test_backtest_logic():
    """测试回测逻辑"""
    print("\n🔄 测试回测逻辑:")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    total_periods = len(predictor.data)
    
    # 测试回测参数
    num_periods = 10  # 回测期数
    min_train_periods = 200  # 最少训练期数
    
    max_backtest = min(num_periods, total_periods - min_train_periods)
    
    print(f"📊 回测参数:")
    print(f"  请求回测期数: {num_periods}")
    print(f"  最少训练期数: {min_train_periods}")
    print(f"  实际回测期数: {max_backtest}")
    print(f"  保留训练期数: {total_periods - max_backtest}")
    print(f"  数据利用率: {max_backtest/total_periods:.1%}")
    
    # 验证每期回测的训练数据
    print(f"\n📚 各期回测的训练数据验证:")
    
    for i in range(min(5, max_backtest)):
        print(f"\n  回测第{i+1}期:")
        
        # 模拟训练数据获取
        max_train_periods = 200
        train_start = i + 1
        train_end = min(len(predictor.data), train_start + max_train_periods)
        available_train_periods = train_end - train_start
        
        print(f"    预测目标: {predictor.data.iloc[i]['期号']}期")
        print(f"    训练数据: 第{train_start+1}期到第{train_end}期")
        print(f"    训练期数: {available_train_periods}期")
        
        # 检查训练数据是否足够
        if available_train_periods >= 200:
            print(f"    ✅ 训练数据充足: 使用完整200期")
        elif available_train_periods >= 50:
            print(f"    ⚠️  训练数据有限: 仅{available_train_periods}期")
        else:
            print(f"    ❌ 训练数据不足: 仅{available_train_periods}期")

def test_single_prediction():
    """测试单次预测"""
    print("\n🎯 测试单次预测:")
    print("=" * 80)
    
    predictor = LotteryPredictor()
    
    print("📊 执行单次预测测试...")
    
    try:
        # 执行预测
        prediction = predictor.predict_next_period(0)
        
        print(f"✅ 预测成功完成")
        print(f"  预测期号: {prediction['period']}")
        print(f"  杀号数据: 红球{len(prediction['kill_numbers'].get('red_universal', []))}个, 蓝球{len(prediction['kill_numbers'].get('blue_universal', []))}个")
        print(f"  生成组合: {len(prediction.get('all_combinations', []))}组")
        print(f"  贝叶斯选择: {len(prediction.get('bayes_selected', []))}组")
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        import traceback
        traceback.print_exc()

def test_efficiency_comparison():
    """测试效率对比"""
    print("\n⚡ 效率对比测试:")
    print("=" * 80)
    
    import time
    
    predictor = LotteryPredictor()
    
    print("📊 测试训练数据加载效率...")
    
    # 测试不同训练期数的加载时间
    test_periods = [50, 100, 200, 500, 1000]
    
    for periods in test_periods:
        if periods > len(predictor.data) - 1:
            continue
            
        start_time = time.time()
        
        # 模拟训练数据获取
        train_start = 1
        train_end = min(len(predictor.data), train_start + periods)
        train_data = predictor.data.iloc[train_start:train_end].copy()
        
        end_time = time.time()
        load_time = (end_time - start_time) * 1000  # 转换为毫秒
        
        print(f"  {periods:4d}期训练数据: {load_time:.2f}ms")

def main():
    """主函数"""
    try:
        test_training_optimization()
        test_backtest_logic()
        test_single_prediction()
        test_efficiency_comparison()
        
        print("\n🎯 优化总结:")
        print("=" * 80)
        print("✅ 训练期数限制: 200期（提高效率）")
        print("✅ 回测期数设置: 10期（快速验证）")
        print("✅ 每次重新训练: 确保数据时效性")
        print("✅ 保留充足训练集: 避免数据不足")
        print("✅ 时间逻辑正确: 避免未来信息泄露")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
