#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
杀号数量分析系统
分析不同杀号数量下的成功率变化关系
基于贝叶斯+马尔科夫链的高级概率系统
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from itertools import combinations
from collections import defaultdict, Counter
import math
import matplotlib.pyplot as plt

# 导入之前的高级概率系统
from advanced_probabilistic_system import AdvancedProbabilisticSystem, EnsembleKillSystem

class KillCountAnalysisSystem:
    def __init__(self):
        self.data = None
        self.ensemble_system = None
        self.test_periods = 30
        self.kill_count_range = range(1, 16)  # 测试1-15个杀号
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
            
            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"✅ 成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue
            
            print(f"❌ 未找到数据文件: {data_files}")
            return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def initialize_system(self):
        """初始化概率系统"""
        print("🔧 初始化杀号数量分析系统...")
        self.ensemble_system = EnsembleKillSystem(self.data)
        # 使用之前找到的最佳权重
        self.ensemble_system.weights = {'bayesian': 0.5, 'markov1': 0.3, 'markov2': 0.2}
        print("✅ 系统初始化完成")
    
    def test_different_kill_counts(self) -> Dict:
        """测试不同杀号数量的成功率"""
        print(f"\n🔍 测试不同杀号数量的成功率变化...")
        print("=" * 80)
        
        results = {}
        
        for kill_count in self.kill_count_range:
            print(f"\n测试杀号数量: {kill_count}个")
            
            stats = self._test_specific_kill_count(kill_count)
            results[kill_count] = stats
            
            print(f"  全中率: {stats['perfect_rate']:.1%} ({stats['perfect_periods']}/{stats['total_periods']})")
            print(f"  杀号成功率: {stats['kill_success_rate']:.1%} ({stats['successful_periods']}/{stats['total_periods']})")
            print(f"  平均杀号数: {stats['avg_kills']:.1f}")
        
        return results
    
    def _test_specific_kill_count(self, target_kill_count: int) -> Dict:
        """测试特定杀号数量的表现"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_periods': 0,  # 修改：杀号成功的期数
            'kill_success_rate': 0.0,
            'avg_kills': 0.0,
            'period_details': []
        }
        
        for i in range(self.test_periods):
            if i + 5 >= len(self.data):
                break
                
            # 获取历史数据
            current_period = self.data.iloc[i]
            period_data = {
                'current': current_period,
                'last': self.data.iloc[i + 1],
                'prev2': self.data.iloc[i + 2],
                'prev3': self.data.iloc[i + 3],
                'prev4': self.data.iloc[i + 4],
                'prev5': self.data.iloc[i + 5]
            }
            
            # 解析号码
            from test_kill_algorithm import parse_numbers
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period_data['last'])
            period2_red, _ = parse_numbers(period_data['prev2'])
            
            # 使用集成系统预测杀号
            try:
                predicted_kills = self.ensemble_system.predict_ensemble_kills(period_data, target_count=target_kill_count)
                
                # 过滤掉前两期出现的号码
                valid_kills = [k for k in predicted_kills if k not in (period1_red + period2_red) and 1 <= k <= 35]
                
                if valid_kills:
                    stats['total_periods'] += 1
                    stats['total_kills'] += len(valid_kills)
                    
                    # 检查杀号成功情况（修改：全部杀号正确才算成功）
                    successful_kills = sum(1 for k in valid_kills if k not in current_red)
                    is_kill_success = successful_kills == len(valid_kills)  # 全部杀号正确才算成功

                    if is_kill_success:
                        stats['successful_periods'] += 1

                    # 检查是否全中（保持原逻辑，与杀号成功是同一个概念）
                    is_perfect = is_kill_success
                    if is_perfect:
                        stats['perfect_periods'] += 1
                    
                    # 记录详情
                    stats['period_details'].append({
                        'period': current_period['期号'],
                        'kills': valid_kills,
                        'successful': successful_kills,
                        'total': len(valid_kills),
                        'perfect': is_perfect,
                        'kill_success': is_kill_success,  # 添加杀号成功标记
                        'actual_red': current_red
                    })
                    
            except Exception as e:
                continue
        
        # 计算统计数据
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
            stats['avg_kills'] = stats['total_kills'] / stats['total_periods']
            # 修改：杀号成功率 = 杀号成功的期数 / 总期数
            stats['kill_success_rate'] = stats['successful_periods'] / stats['total_periods']
        
        return stats
    
    def analyze_success_rate_relationship(self, results: Dict):
        """分析成功率与杀号数量的关系"""
        print(f"\n📊 杀号数量与成功率关系分析")
        print("=" * 80)
        
        # 提取数据
        kill_counts = []
        perfect_rates = []
        kill_success_rates = []
        avg_kills = []
        
        for kill_count, stats in results.items():
            kill_counts.append(kill_count)
            perfect_rates.append(stats['perfect_rate'])
            kill_success_rates.append(stats['kill_success_rate'])
            avg_kills.append(stats['avg_kills'])
        
        # 打印详细表格
        print("杀号目标 | 实际杀号 | 全中率  | 杀号成功率 | 全中期数")
        print("-" * 60)
        
        for i, kill_count in enumerate(kill_counts):
            stats = results[kill_count]
            print(f"   {kill_count:2d}个   |   {avg_kills[i]:4.1f}个  | {perfect_rates[i]:6.1%} |   {kill_success_rates[i]:6.1%}   | {stats['perfect_periods']:2d}/{stats['total_periods']:2d}")
        
        # 分析趋势
        print(f"\n📈 趋势分析:")
        
        # 找出全中率的变化点
        print(f"🎯 全中率变化:")
        for i in range(1, len(perfect_rates)):
            change = perfect_rates[i] - perfect_rates[i-1]
            if abs(change) > 0.05:  # 变化超过5%
                direction = "上升" if change > 0 else "下降"
                print(f"  {kill_counts[i-1]}→{kill_counts[i]}个杀号: {direction}{abs(change):.1%}")
        
        # 找出最佳平衡点
        best_balance_idx = self._find_best_balance_point(kill_counts, perfect_rates, avg_kills)
        if best_balance_idx is not None:
            best_count = kill_counts[best_balance_idx]
            best_rate = perfect_rates[best_balance_idx]
            best_avg = avg_kills[best_balance_idx]
            print(f"\n🏆 最佳平衡点: {best_count}个杀号目标")
            print(f"   全中率: {best_rate:.1%}")
            print(f"   实际平均杀号: {best_avg:.1f}个")
        
        # 数学关系分析
        self._analyze_mathematical_relationship(kill_counts, perfect_rates, kill_success_rates)
        
        return {
            'kill_counts': kill_counts,
            'perfect_rates': perfect_rates,
            'kill_success_rates': kill_success_rates,
            'avg_kills': avg_kills
        }
    
    def _find_best_balance_point(self, kill_counts: List[int], perfect_rates: List[float], avg_kills: List[float]) -> int:
        """找出最佳平衡点"""
        best_score = 0
        best_idx = None
        
        for i, (count, rate, avg) in enumerate(zip(kill_counts, perfect_rates, avg_kills)):
            # 综合评分：全中率权重60%，杀号效率权重40%
            # 杀号效率 = 杀号数量 / 最大可能杀号数量
            efficiency = min(avg / 10, 1.0)  # 假设10个杀号为合理上限
            score = rate * 0.6 + efficiency * 0.4
            
            if score > best_score:
                best_score = score
                best_idx = i
        
        return best_idx
    
    def _analyze_mathematical_relationship(self, kill_counts: List[int], perfect_rates: List[float], kill_success_rates: List[float]):
        """分析数学关系"""
        print(f"\n🔬 数学关系分析:")
        
        # 分析全中率的衰减模式
        print(f"📉 全中率衰减分析:")
        
        # 计算相邻点的衰减率
        decay_rates = []
        for i in range(1, len(perfect_rates)):
            if perfect_rates[i-1] > 0:
                decay_rate = (perfect_rates[i-1] - perfect_rates[i]) / perfect_rates[i-1]
                decay_rates.append(decay_rate)
                print(f"  {kill_counts[i-1]}→{kill_counts[i]}个: 衰减{decay_rate:.1%}")
        
        # 分析是否符合指数衰减
        if len(decay_rates) > 3:
            avg_decay = sum(decay_rates) / len(decay_rates)
            print(f"  平均衰减率: {avg_decay:.1%}")
            
            # 检查是否接近理论预期
            # 理论上，如果每个杀号独立，全中率应该按指数衰减
            print(f"\n💡 理论分析:")
            print(f"  如果杀号独立，全中率应该按 (单个成功率)^杀号数 衰减")
            
            # 估算单个杀号的平均成功率
            if kill_success_rates:
                avg_single_rate = sum(kill_success_rates) / len(kill_success_rates)
                print(f"  估算单个杀号成功率: {avg_single_rate:.1%}")
                
                print(f"  理论vs实际对比:")
                for i, count in enumerate(kill_counts[:8]):  # 只显示前8个
                    theoretical = avg_single_rate ** count
                    actual = perfect_rates[i]
                    diff = actual - theoretical
                    print(f"    {count}个杀号: 理论{theoretical:.1%} vs 实际{actual:.1%} (差异{diff:+.1%})")
    
    def plot_results(self, analysis_data: Dict):
        """绘制结果图表"""
        try:
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
            plt.rcParams['axes.unicode_minus'] = False
            
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 图1：全中率vs杀号数量
            ax1.plot(analysis_data['kill_counts'], [r*100 for r in analysis_data['perfect_rates']], 
                    'bo-', linewidth=2, markersize=8, label='全中率')
            ax1.set_xlabel('杀号数量')
            ax1.set_ylabel('全中率 (%)')
            ax1.set_title('杀号数量 vs 全中率')
            ax1.grid(True, alpha=0.3)
            ax1.legend()
            
            # 图2：杀号成功率vs杀号数量
            ax2.plot(analysis_data['kill_counts'], [r*100 for r in analysis_data['kill_success_rates']], 
                    'ro-', linewidth=2, markersize=8, label='杀号成功率')
            ax2.set_xlabel('杀号数量')
            ax2.set_ylabel('杀号成功率 (%)')
            ax2.set_title('杀号数量 vs 杀号成功率')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            
            plt.tight_layout()
            plt.savefig('kill_count_analysis.png', dpi=300, bbox_inches='tight')
            print(f"\n📊 图表已保存为 kill_count_analysis.png")
            
        except ImportError:
            print(f"\n⚠️ matplotlib未安装，跳过图表绘制")
        except Exception as e:
            print(f"\n⚠️ 图表绘制失败: {e}")

def main():
    """主函数"""
    print("🎯 杀号数量分析系统")
    print("分析不同杀号数量下的成功率变化关系")
    print("=" * 60)
    
    system = KillCountAnalysisSystem()
    
    if not system.load_data():
        return
    
    system.initialize_system()
    
    # 测试不同杀号数量
    results = system.test_different_kill_counts()
    
    # 分析关系
    analysis_data = system.analyze_success_rate_relationship(results)
    
    # 绘制图表
    system.plot_results(analysis_data)
    
    print(f"\n🎉 杀号数量分析完成！")

if __name__ == "__main__":
    main()
