"""
统一回测框架核心实现
解决期号作为循环条件的问题，提供标准化的回测接口
"""

import pandas as pd
import time
from datetime import datetime
from typing import List, Dict, Any, Tuple
from .interfaces import PredictorInterface, EvaluatorInterface
from .data_models import (
    BacktestConfig, BacktestResult, PredictionResult, 
    EvaluationResult, PeriodResult, Statistics
)


class BacktestFramework:
    """
    统一回测框架
    
    核心设计原则：
    1. 期号只作为标志，不作为循环条件
    2. 基于数据索引进行循环
    3. 提供标准化的接口
    4. 支持不同的预测器和评估器
    """
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化回测框架
        
        Args:
            data: 历史数据，必须包含'期号'列
        """
        self.data = data
        self.evaluator = DefaultEvaluator()
        self._validate_data()
    
    def _validate_data(self):
        """验证数据格式"""
        if self.data is None or self.data.empty:
            raise ValueError("数据不能为空")
        
        if '期号' not in self.data.columns:
            raise ValueError("数据必须包含'期号'列")
        
        print(f"✅ 数据验证通过：共 {len(self.data)} 期数据")
    
    def run_backtest(self, 
                    predictor: PredictorInterface, 
                    config: BacktestConfig) -> BacktestResult:
        """
        运行回测
        
        Args:
            predictor: 预测器实例
            config: 回测配置
            
        Returns:
            BacktestResult: 完整的回测结果
        """
        if not config.validate():
            raise ValueError("回测配置无效")
        
        print(f"🧪 开始回测 - {predictor.get_predictor_name()}")
        print(f"📊 配置：{config.num_periods}期回测，最少{config.min_train_periods}期训练")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # 计算回测范围（关键：基于数据索引，不基于期号）
        max_backtest = min(
            config.num_periods, 
            len(self.data) - config.min_train_periods
        )
        
        if max_backtest <= 0:
            raise ValueError(f"数据不足：需要至少{config.min_train_periods + 1}期数据")
        
        print(f"📈 将回测 {max_backtest} 期数据（数据索引：{config.min_train_periods} 到 {config.min_train_periods + max_backtest - 1}）")
        
        period_results = []
        
        # 核心循环：基于数据索引，不基于期号
        for data_index in range(config.min_train_periods, config.min_train_periods + max_backtest):
            try:
                # 期号只作为标志使用
                period_number = str(self.data.iloc[data_index]['期号'])
                print(f"正在处理数据索引 {data_index}，期号 {period_number}...")
                
                # 预测（预测器内部决定如何使用历史数据）
                prediction = predictor.predict_for_period(data_index, self.data)
                
                # 获取实际开奖数据
                actual_data = self._extract_actual_data(data_index)
                
                # 评估预测结果
                evaluation = self.evaluator.evaluate(prediction, actual_data)
                
                # 记录结果
                period_result = PeriodResult(
                    prediction=prediction,
                    evaluation=evaluation,
                    success=True
                )
                period_results.append(period_result)
                
            except Exception as e:
                print(f"❌ 数据索引 {data_index} 处理失败: {e}")
                # 记录失败的结果
                period_result = PeriodResult(
                    prediction=PredictionResult(
                        period_number=str(self.data.iloc[data_index]['期号']),
                        data_index=data_index,
                        red_odd_even_predictions=[],
                        red_size_predictions=[],
                        blue_size_predictions=[],
                        generated_numbers=([], []),
                        kill_numbers={}
                    ),
                    evaluation=EvaluationResult(
                        period_number=str(self.data.iloc[data_index]['期号']),
                        data_index=data_index,
                        actual_red=[],
                        actual_blue=[],
                        actual_red_odd_even="",
                        actual_red_size="",
                        actual_blue_size=""
                    ),
                    success=False,
                    error_message=str(e)
                )
                period_results.append(period_result)
                continue
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"✅ 回测完成，共处理 {len(period_results)} 期，耗时 {duration:.2f} 秒")
        
        # 计算统计信息
        statistics = self._calculate_statistics(period_results, config)
        
        # 构建回测结果
        result = BacktestResult(
            config=config,
            period_results=period_results,
            statistics=statistics,
            predictor_name=predictor.get_predictor_name(),
            start_time=start_time,
            end_time=end_time,
            total_duration=duration,
            data_source="lottery_data",
            total_data_periods=len(self.data),
            backtest_data_range=f"索引{config.min_train_periods}-{config.min_train_periods + max_backtest - 1}"
        )
        
        return result
    
    def _extract_actual_data(self, data_index: int) -> Dict[str, Any]:
        """提取实际开奖数据"""
        row = self.data.iloc[data_index]
        
        # 解析红球和蓝球
        red_balls, blue_balls = self._parse_numbers(row)
        
        # 计算比例
        red_odd_even = self._calculate_odd_even_ratio(red_balls)
        red_size = self._calculate_size_ratio(red_balls, 18)  # 红球分界线18
        blue_size = self._calculate_size_ratio(blue_balls, 6)  # 蓝球分界线6
        
        return {
            'period_number': str(row['期号']),
            'red_balls': red_balls,
            'blue_balls': blue_balls,
            'red_odd_even': red_odd_even,
            'red_size': red_size,
            'blue_size': blue_size
        }
    
    def _parse_numbers(self, row) -> Tuple[List[int], List[int]]:
        """解析号码"""
        # 这里需要根据实际数据格式调整
        if '红球' in row and '蓝球' in row:
            red_str = str(row['红球']).strip()
            blue_str = str(row['蓝球']).strip()
        else:
            # 假设格式为 "01 02 03 04 05 + 01 02"
            numbers_str = str(row.get('开奖号码', ''))
            if '+' in numbers_str:
                red_str, blue_str = numbers_str.split('+')
            else:
                # 其他格式处理
                red_str = ""
                blue_str = ""
        
        red_balls = [int(x) for x in red_str.split() if x.isdigit()]
        blue_balls = [int(x) for x in blue_str.split() if x.isdigit()]
        
        return red_balls, blue_balls
    
    def _calculate_odd_even_ratio(self, numbers: List[int]) -> str:
        """计算奇偶比"""
        odd_count = sum(1 for n in numbers if n % 2 == 1)
        even_count = len(numbers) - odd_count
        return f"{odd_count}:{even_count}"
    
    def _calculate_size_ratio(self, numbers: List[int], boundary: int) -> str:
        """计算大小比"""
        small_count = sum(1 for n in numbers if n <= boundary)
        big_count = len(numbers) - small_count
        return f"{small_count}:{big_count}"
    
    def _calculate_statistics(self, period_results: List[PeriodResult], config: BacktestConfig) -> Statistics:
        """计算统计信息"""
        if not period_results:
            return Statistics()
        
        successful_results = [r for r in period_results if r.success]
        total_periods = len(successful_results)
        
        if total_periods == 0:
            return Statistics()
        
        # 计算各项命中率
        hit_counts = {}
        hit_rates = {}
        
        for metric in config.metrics:
            if metric in ['red_odd_even_hit', 'red_size_hit', 'blue_size_hit']:
                count = sum(1 for r in successful_results if r.evaluation.hits.get(metric, False))
                hit_counts[metric] = count
                hit_rates[metric] = count / total_periods
        
        # 计算2+1命中率
        hit_2_plus_1_count = sum(1 for r in successful_results if r.evaluation.hit_2_plus_1)
        hit_2_plus_1_rate = hit_2_plus_1_count / total_periods
        
        # 计算杀号成功率
        kill_success_counts = {}
        kill_success_rates = {}
        
        for kill_type in ['red_kill_success', 'blue_kill_success']:
            count = sum(1 for r in successful_results if r.evaluation.kill_success.get(kill_type, False))
            kill_success_counts[kill_type] = count
            kill_success_rates[kill_type] = count / total_periods
        
        # 计算平均命中数
        avg_red_hits = sum(r.evaluation.red_hits for r in successful_results) / total_periods
        avg_blue_hits = sum(r.evaluation.blue_hits for r in successful_results) / total_periods
        avg_total_hits = sum(r.evaluation.total_hits for r in successful_results) / total_periods
        
        return Statistics(
            total_periods=total_periods,
            successful_periods=len(successful_results),
            hit_rates=hit_rates,
            hit_counts=hit_counts,
            kill_success_rates=kill_success_rates,
            kill_success_counts=kill_success_counts,
            hit_2_plus_1_rate=hit_2_plus_1_rate,
            hit_2_plus_1_count=hit_2_plus_1_count,
            avg_red_hits=avg_red_hits,
            avg_blue_hits=avg_blue_hits,
            avg_total_hits=avg_total_hits
        )


class DefaultEvaluator(EvaluatorInterface):
    """默认评估器"""
    
    def evaluate(self, prediction: PredictionResult, actual_data: dict) -> EvaluationResult:
        """评估预测结果"""
        # 提取实际数据
        actual_red = actual_data['red_balls']
        actual_blue = actual_data['blue_balls']
        actual_red_odd_even = actual_data['red_odd_even']
        actual_red_size = actual_data['red_size']
        actual_blue_size = actual_data['blue_size']
        
        # 计算命中情况
        hits = {}
        
        # 比例预测命中
        if prediction.red_odd_even_predictions:
            pred_red_odd_even = prediction.red_odd_even_predictions[0][0]
            hits['red_odd_even_hit'] = pred_red_odd_even == actual_red_odd_even
        
        if prediction.red_size_predictions:
            pred_red_size = prediction.red_size_predictions[0][0]
            hits['red_size_hit'] = pred_red_size == actual_red_size
        
        if prediction.blue_size_predictions:
            pred_blue_size = prediction.blue_size_predictions[0][0]
            hits['blue_size_hit'] = pred_blue_size == actual_blue_size
        
        # 号码命中
        pred_red, pred_blue = prediction.generated_numbers
        red_hits = len(set(pred_red) & set(actual_red))
        blue_hits = len(set(pred_blue) & set(actual_blue))
        total_hits = red_hits + blue_hits
        
        # 2+1命中
        hit_2_plus_1 = (hits.get('red_odd_even_hit', False) and 
                       hits.get('red_size_hit', False) and 
                       hits.get('blue_size_hit', False))
        
        # 杀号成功（简化版本）
        kill_success = {}
        if 'red_universal' in prediction.kill_numbers:
            red_kills = prediction.kill_numbers['red_universal']
            kill_success['red_kill_success'] = not any(k in actual_red for k in red_kills)
        
        if 'blue_universal' in prediction.kill_numbers:
            blue_kills = prediction.kill_numbers['blue_universal']
            kill_success['blue_kill_success'] = not any(k in actual_blue for k in blue_kills)
        
        return EvaluationResult(
            period_number=prediction.period_number,
            data_index=prediction.data_index,
            actual_red=actual_red,
            actual_blue=actual_blue,
            actual_red_odd_even=actual_red_odd_even,
            actual_red_size=actual_red_size,
            actual_blue_size=actual_blue_size,
            hits=hits,
            kill_success=kill_success,
            red_hits=red_hits,
            blue_hits=blue_hits,
            total_hits=total_hits,
            hit_2_plus_1=hit_2_plus_1
        )
    
    def get_supported_metrics(self) -> list:
        """获取支持的评估指标"""
        return [
            'red_odd_even_hit', 'red_size_hit', 'blue_size_hit',
            'hit_2_plus_1', 'red_kill_success', 'blue_kill_success',
            'red_hits', 'blue_hits', 'total_hits'
        ]
