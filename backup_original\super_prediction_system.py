"""
超级预测系统
集成所有优化：修复Bug、增强神经网络、外部特征、集成学习、平衡生成
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
import os
from datetime import datetime

from enhanced_neural_predictor import EnhancedNeuralPredictor
from external_features import ExternalFeatureExtractor
from adaptive_learning_system import AdaptiveLearningSystem
from balanced_dynamic_generator import BalancedDynamicGenerator
from main import LotteryPredictor
from utils import load_data, parse_numbers, check_hit_2_plus_1


class SuperPredictionSystem:
    """超级预测系统"""
    
    def __init__(self):
        """初始化超级预测系统"""
        print("初始化超级预测系统...")
        
        # 核心组件
        self.enhanced_neural = EnhancedNeuralPredictor()
        self.external_features = ExternalFeatureExtractor()
        self.adaptive_system = AdaptiveLearningSystem()
        self.balanced_generator = BalancedDynamicGenerator()
        self.base_predictor = LotteryPredictor()
        
        # 系统状态
        self.is_initialized = False
        self.model_version = "v3.0-Super"
        self.last_update = None
        
        # 性能跟踪
        self.prediction_history = []
        self.performance_metrics = {
            'total_predictions': 0,
            'red_odd_even_hits': 0,
            'red_size_hits': 0,
            'blue_size_hits': 0,
            'hit_2_plus_1': 0,
            'neural_confidence_avg': 0.0,
            'external_feature_impact': 0.0
        }
        
        # 集成权重
        self.ensemble_weights = {
            'base_predictor': 0.3,
            'enhanced_neural': 0.4,
            'external_features': 0.2,
            'adaptive_adjustment': 0.1
        }
    
    def initialize_system(self, retrain_neural: bool = True, 
                         neural_epochs: int = 100) -> bool:
        """
        初始化超级系统
        
        Args:
            retrain_neural: 是否重新训练神经网络
            neural_epochs: 神经网络训练轮数
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            print("=" * 60)
            print("🚀 超级预测系统初始化")
            print("=" * 60)
            
            # 1. 加载数据
            print("\n1. 加载数据...")
            data = load_data()
            if len(data) == 0:
                print("❌ 无法加载数据")
                return False
            print(f"✅ 数据加载成功: {len(data)} 期")
            
            # 2. 训练增强版神经网络
            if retrain_neural:
                print("\n2. 训练增强版集成神经网络...")
                
                # 使用后80%的数据训练
                train_size = int(len(data) * 0.8)
                train_data = data.iloc[train_size:]
                
                if len(train_data) >= 50:
                    model_path = "enhanced_neural_models.pkl"
                    if not os.path.exists(model_path):
                        self.enhanced_neural.train_ensemble_models(
                            train_data, lookback=10, epochs=neural_epochs
                        )
                        # 保存模型（简化实现）
                        print("✅ 增强版神经网络训练完成")
                    else:
                        print("✅ 已存在预训练的增强版神经网络模型")
                else:
                    print("⚠️ 训练数据不足，跳过神经网络训练")
            
            # 3. 初始化外部特征提取器
            print("\n3. 初始化外部特征提取器...")
            
            # 测试外部特征提取
            test_period = data.iloc[0]['期号'] if len(data) > 0 else 25068
            test_features = self.external_features.extract_all_external_features(test_period)
            print(f"✅ 外部特征提取器初始化完成，特征数: {len(test_features)}")
            
            # 4. 初始化自适应学习系统（已修复Bug）
            print("\n4. 初始化自适应学习系统...")
            
            # 使用最近30期数据初始化
            recent_data = data.head(30)
            for _, row in recent_data.iterrows():
                try:
                    red_balls, blue_balls = parse_numbers(row)
                    self.adaptive_system.update_with_new_result(
                        row['期号'], red_balls, blue_balls
                    )
                except:
                    continue
            print("✅ 自适应学习系统初始化完成")
            
            # 5. 初始化平衡动态生成器
            print("\n5. 初始化平衡动态生成器...")
            # 平衡生成器会在预测时自动初始化
            print("✅ 平衡动态生成器初始化完成")
            
            self.is_initialized = True
            self.last_update = datetime.now()
            
            print(f"\n✅ 超级预测系统初始化完成!")
            print(f"   模型版本: {self.model_version}")
            print(f"   初始化时间: {self.last_update}")
            
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def predict_next_period_super(self, current_period_index: int = 0) -> Dict:
        """
        超级预测下一期号码
        
        Args:
            current_period_index: 当前期次索引
            
        Returns:
            Dict: 超级预测结果
        """
        if not self.is_initialized:
            raise ValueError("系统尚未初始化，请先调用 initialize_system()")
        
        print(f"\n🎯 超级预测第 {current_period_index + 1} 期...")
        
        try:
            data = load_data()
            current_period = data.iloc[current_period_index]['期号'] if current_period_index < len(data) else 25069
            
            # 1. 基础预测
            print("1. 基础预测...")
            base_prediction = self.base_predictor.predict_next_period(current_period_index)
            
            # 2. 增强版神经网络预测
            neural_predictions = {}
            neural_confidence = 0.0
            
            if self.enhanced_neural.is_trained:
                try:
                    print("2. 增强版神经网络集成预测...")
                    recent_data = data.iloc[current_period_index:]
                    neural_predictions = self.enhanced_neural.predict_ensemble(recent_data, lookback=10)
                    neural_confidence = np.mean([conf for _, conf in neural_predictions.values()])
                    print(f"   神经网络预测: {neural_predictions}")
                    print(f"   平均置信度: {neural_confidence:.3f}")
                except Exception as e:
                    print(f"   神经网络预测失败: {e}")
            
            # 3. 外部特征分析
            print("3. 外部特征分析...")
            external_features = self.external_features.extract_all_external_features(current_period)
            
            # 基于外部特征的简单调整
            external_adjustments = self._analyze_external_features(external_features)
            print(f"   外部特征影响: {external_adjustments}")
            
            # 4. 集成预测结果
            print("4. 集成多源预测...")
            integrated_predictions = self._integrate_predictions(
                base_prediction, neural_predictions, external_adjustments
            )
            
            # 5. 自适应调整
            print("5. 自适应调整...")
            adaptive_predictions = self.adaptive_system.get_adaptive_predictions(integrated_predictions)
            
            # 6. 平衡动态号码生成
            print("6. 平衡动态号码生成...")
            
            # 提取状态预测
            red_odd_even_state = adaptive_predictions.get('red_odd_even_prediction', ('3:2', 0.5))[0]
            red_size_state = adaptive_predictions.get('red_size_prediction', ('2:3', 0.5))[0]
            blue_size_state = adaptive_predictions.get('blue_size_prediction', ('1:1', 0.5))[0]
            
            # 获取杀号
            kill_numbers = adaptive_predictions.get('kill_numbers', {'red': [], 'blue': []})
            
            # 生成平衡号码
            predicted_red, predicted_blue = self.balanced_generator.generate_balanced_numbers(
                red_odd_even_state, red_size_state, blue_size_state,
                kill_numbers, current_period_index, current_period_index
            )
            
            # 7. 构建最终预测结果
            final_prediction = {
                'period_index': current_period_index,
                'period_num': current_period,
                'prediction_time': datetime.now(),
                'model_version': self.model_version,
                
                # 状态预测（集成结果）
                'red_odd_even_prediction': adaptive_predictions.get('red_odd_even_prediction', ('3:2', 0.5)),
                'red_size_prediction': adaptive_predictions.get('red_size_prediction', ('2:3', 0.5)),
                'blue_size_prediction': adaptive_predictions.get('blue_size_prediction', ('1:1', 0.5)),
                
                # 号码预测
                'generated_numbers': (predicted_red, predicted_blue),
                
                # 杀号
                'kill_numbers': kill_numbers,
                
                # 预测来源和置信度
                'prediction_sources': {
                    'base_predictor': True,
                    'enhanced_neural': len(neural_predictions) > 0,
                    'external_features': True,
                    'adaptive_system': True,
                    'balanced_generator': True
                },
                
                'confidence_scores': {
                    'red_odd_even': adaptive_predictions.get('red_odd_even_prediction', ('3:2', 0.5))[1],
                    'red_size': adaptive_predictions.get('red_size_prediction', ('2:3', 0.5))[1],
                    'blue_size': adaptive_predictions.get('blue_size_prediction', ('1:1', 0.5))[1],
                    'neural_avg': neural_confidence,
                    'external_impact': external_adjustments.get('impact_score', 0.0)
                },
                
                # 集成权重
                'ensemble_weights': self.ensemble_weights.copy(),
                
                # 外部特征
                'external_features': external_features,
                
                # 系统状态
                'adaptive_weights': self.adaptive_system.get_current_weights()
            }
            
            # 记录预测历史
            self.prediction_history.append(final_prediction)
            
            print("✅ 超级预测完成!")
            return final_prediction
            
        except Exception as e:
            print(f"❌ 超级预测失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def _analyze_external_features(self, features: Dict[str, float]) -> Dict:
        """分析外部特征的影响"""
        adjustments = {'impact_score': 0.0}
        
        # 基于时间特征的简单调整
        if features.get('is_holiday', 0) > 0.5:
            adjustments['holiday_effect'] = 0.1
            adjustments['impact_score'] += 0.1
        
        if features.get('is_weekend', 0) > 0.5:
            adjustments['weekend_effect'] = 0.05
            adjustments['impact_score'] += 0.05
        
        # 基于季节的调整
        season = features.get('season', 0)
        if season > 0.7:  # 冬季
            adjustments['winter_effect'] = 0.08
            adjustments['impact_score'] += 0.08
        
        # 基于月相的调整
        lunar_phase = features.get('lunar_phase', 0)
        if 0.4 <= lunar_phase <= 0.6:  # 满月附近
            adjustments['lunar_effect'] = 0.06
            adjustments['impact_score'] += 0.06
        
        return adjustments
    
    def _integrate_predictions(self, base_pred: Dict, neural_pred: Dict, 
                             external_adj: Dict) -> Dict:
        """集成多源预测结果"""
        integrated = base_pred.copy()
        
        # 集成神经网络预测
        for key, (pred_value, confidence) in neural_pred.items():
            if key in integrated:
                base_conf = integrated[key][1] if isinstance(integrated[key], tuple) else 0.5
                
                # 加权平均置信度
                neural_weight = self.ensemble_weights['enhanced_neural']
                base_weight = self.ensemble_weights['base_predictor']
                
                total_weight = neural_weight + base_weight
                combined_conf = (neural_weight * confidence + base_weight * base_conf) / total_weight
                
                # 如果神经网络置信度更高，使用神经网络的预测
                if confidence > base_conf:
                    integrated[key] = (pred_value, combined_conf)
        
        # 应用外部特征调整
        external_impact = external_adj.get('impact_score', 0.0)
        if external_impact > 0.1:
            # 轻微调整置信度
            for key in integrated:
                if isinstance(integrated[key], tuple) and len(integrated[key]) == 2:
                    pred_value, conf = integrated[key]
                    if isinstance(conf, (int, float)):  # 确保conf是数字
                        adjusted_conf = min(1.0, conf * (1 + external_impact * 0.1))
                        integrated[key] = (pred_value, adjusted_conf)
        
        return integrated
    
    def update_with_actual_result_super(self, period_num: int, red_balls: List[int], 
                                      blue_balls: List[int]):
        """
        使用实际开奖结果更新超级系统
        
        Args:
            period_num: 期号
            red_balls: 红球号码
            blue_balls: 蓝球号码
        """
        print(f"\n📊 更新超级系统 - 期号: {period_num}")
        
        # 查找对应的预测结果
        corresponding_prediction = None
        for pred in self.prediction_history:
            if pred['period_num'] == period_num:
                corresponding_prediction = pred
                break
        
        # 更新自适应学习系统
        predictions_for_adaptive = None
        if corresponding_prediction:
            predictions_for_adaptive = {
                'red_odd_even': corresponding_prediction['red_odd_even_prediction'][0],
                'red_size': corresponding_prediction['red_size_prediction'][0],
                'blue_size': corresponding_prediction['blue_size_prediction'][0],
                'predicted_numbers': corresponding_prediction['generated_numbers']
            }
        
        self.adaptive_system.update_with_new_result(
            period_num, red_balls, blue_balls, predictions_for_adaptive
        )
        
        # 更新性能指标
        if corresponding_prediction:
            self._update_performance_metrics_super(
                red_balls, blue_balls, corresponding_prediction
            )
        
        # 更新平衡生成器的策略性能
        if corresponding_prediction:
            pred_red, pred_blue = corresponding_prediction['generated_numbers']
            hit_2_plus_1 = check_hit_2_plus_1((pred_red, pred_blue), (red_balls, blue_balls))
            
            # 这里需要知道使用了哪个策略，简化实现
            # self.balanced_generator.update_performance(strategy, hit_2_plus_1)
        
        print("✅ 超级系统更新完成")
    
    def _update_performance_metrics_super(self, actual_red: List[int], actual_blue: List[int], 
                                        prediction: Dict):
        """更新超级系统性能指标"""
        self.performance_metrics['total_predictions'] += 1
        
        # 检查状态预测
        actual_odd = sum(1 for x in actual_red if x % 2 == 1)
        actual_red_odd_even = f"{actual_odd}:{5-actual_odd}"
        if prediction['red_odd_even_prediction'][0] == actual_red_odd_even:
            self.performance_metrics['red_odd_even_hits'] += 1
        
        actual_small = sum(1 for x in actual_red if x <= 18)
        actual_red_size = f"{actual_small}:{5-actual_small}"
        if prediction['red_size_prediction'][0] == actual_red_size:
            self.performance_metrics['red_size_hits'] += 1
        
        actual_blue_small = sum(1 for x in actual_blue if x <= 6)
        actual_blue_size = f"{actual_blue_small}:{2-actual_blue_small}"
        if prediction['blue_size_prediction'][0] == actual_blue_size:
            self.performance_metrics['blue_size_hits'] += 1
        
        # 检查2+1命中
        pred_red, pred_blue = prediction['generated_numbers']
        if check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue)):
            self.performance_metrics['hit_2_plus_1'] += 1
        
        # 更新神经网络置信度平均值
        neural_conf = prediction['confidence_scores'].get('neural_avg', 0.0)
        total = self.performance_metrics['total_predictions']
        current_avg = self.performance_metrics['neural_confidence_avg']
        self.performance_metrics['neural_confidence_avg'] = (current_avg * (total - 1) + neural_conf) / total
        
        # 更新外部特征影响
        external_impact = prediction['confidence_scores'].get('external_impact', 0.0)
        current_ext_avg = self.performance_metrics['external_feature_impact']
        self.performance_metrics['external_feature_impact'] = (current_ext_avg * (total - 1) + external_impact) / total
    
    def get_super_system_status(self) -> Dict:
        """获取超级系统状态"""
        status = {
            'is_initialized': self.is_initialized,
            'model_version': self.model_version,
            'last_update': self.last_update,
            'components_status': {
                'enhanced_neural': self.enhanced_neural.is_trained,
                'external_features': True,
                'adaptive_system': len(self.adaptive_system.recent_data),
                'balanced_generator': True
            },
            'performance_metrics': self.performance_metrics.copy(),
            'prediction_count': len(self.prediction_history),
            'ensemble_weights': self.ensemble_weights.copy()
        }
        
        # 计算命中率
        if self.performance_metrics['total_predictions'] > 0:
            total = self.performance_metrics['total_predictions']
            status['hit_rates'] = {
                'red_odd_even': self.performance_metrics['red_odd_even_hits'] / total,
                'red_size': self.performance_metrics['red_size_hits'] / total,
                'blue_size': self.performance_metrics['blue_size_hits'] / total,
                'hit_2_plus_1': self.performance_metrics['hit_2_plus_1'] / total
            }
        
        return status
    
    def run_super_backtest(self, num_periods: int = 15) -> Dict:
        """
        运行超级回测
        
        Args:
            num_periods: 回测期数
            
        Returns:
            Dict: 超级回测结果
        """
        print(f"\n🧪 开始超级回测 ({num_periods} 期)...")
        
        data = load_data()
        if len(data) < num_periods + 15:
            raise ValueError("数据不足以进行回测")
        
        results = []
        
        for i in range(num_periods):
            try:
                print(f"\n--- 超级回测第 {i+1}/{num_periods} 期 ---")
                
                # 超级预测
                prediction = self.predict_next_period_super(i)
                
                # 获取实际结果
                actual_row = data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)
                
                # 评估预测
                evaluation = self._evaluate_super_prediction(
                    prediction, actual_red, actual_blue
                )
                
                results.append({
                    'period_index': i,
                    'period_num': actual_row['期号'],
                    'prediction': prediction,
                    'actual': (actual_red, actual_blue),
                    'evaluation': evaluation
                })
                
                # 更新系统
                self.update_with_actual_result_super(actual_row['期号'], actual_red, actual_blue)
                
            except Exception as e:
                print(f"超级回测第 {i+1} 期失败: {e}")
                continue
        
        # 计算总体统计
        overall_stats = self._calculate_super_backtest_stats(results)
        
        print(f"\n✅ 超级回测完成!")
        return {
            'results': results,
            'overall_stats': overall_stats,
            'system_status': self.get_super_system_status()
        }
    
    def _evaluate_super_prediction(self, prediction: Dict, actual_red: List[int], 
                                 actual_blue: List[int]) -> Dict:
        """评估超级预测"""
        evaluation = {}
        
        # 状态预测评估
        actual_odd = sum(1 for x in actual_red if x % 2 == 1)
        actual_red_odd_even = f"{actual_odd}:{5-actual_odd}"
        evaluation['red_odd_even_hit'] = prediction['red_odd_even_prediction'][0] == actual_red_odd_even
        
        actual_small = sum(1 for x in actual_red if x <= 18)
        actual_red_size = f"{actual_small}:{5-actual_small}"
        evaluation['red_size_hit'] = prediction['red_size_prediction'][0] == actual_red_size
        
        actual_blue_small = sum(1 for x in actual_blue if x <= 6)
        actual_blue_size = f"{actual_blue_small}:{2-actual_blue_small}"
        evaluation['blue_size_hit'] = prediction['blue_size_prediction'][0] == actual_blue_size
        
        # 号码预测评估
        pred_red, pred_blue = prediction['generated_numbers']
        red_hits = len(set(pred_red) & set(actual_red))
        blue_hits = len(set(pred_blue) & set(actual_blue))
        
        evaluation['red_hits'] = red_hits
        evaluation['blue_hits'] = blue_hits
        evaluation['hit_2_plus_1'] = check_hit_2_plus_1((pred_red, pred_blue), (actual_red, actual_blue))
        
        # 置信度评估
        evaluation['neural_confidence'] = prediction['confidence_scores'].get('neural_avg', 0.0)
        evaluation['external_impact'] = prediction['confidence_scores'].get('external_impact', 0.0)
        
        return evaluation
    
    def _calculate_super_backtest_stats(self, results: List[Dict]) -> Dict:
        """计算超级回测统计"""
        if not results:
            return {}
        
        total = len(results)
        
        stats = {
            'total_periods': total,
            'red_odd_even_rate': sum(r['evaluation']['red_odd_even_hit'] for r in results) / total,
            'red_size_rate': sum(r['evaluation']['red_size_hit'] for r in results) / total,
            'blue_size_rate': sum(r['evaluation']['blue_size_hit'] for r in results) / total,
            'hit_2_plus_1_rate': sum(r['evaluation']['hit_2_plus_1'] for r in results) / total,
            'avg_red_hits': np.mean([r['evaluation']['red_hits'] for r in results]),
            'avg_blue_hits': np.mean([r['evaluation']['blue_hits'] for r in results]),
            'avg_neural_confidence': np.mean([r['evaluation']['neural_confidence'] for r in results]),
            'avg_external_impact': np.mean([r['evaluation']['external_impact'] for r in results])
        }
        
        # 综合命中率
        stats['overall_rate'] = (stats['red_odd_even_rate'] + stats['red_size_rate'] + stats['blue_size_rate']) / 3
        
        return stats


def test_super_system():
    """测试超级预测系统"""
    system = SuperPredictionSystem()
    
    # 初始化系统
    if not system.initialize_system(retrain_neural=True, neural_epochs=50):
        print("超级系统初始化失败")
        return
    
    # 运行超级回测
    try:
        backtest_results = system.run_super_backtest(num_periods=10)
        
        print("\n" + "=" * 60)
        print("📊 超级回测结果")
        print("=" * 60)
        
        stats = backtest_results['overall_stats']
        for key, value in stats.items():
            if 'rate' in key or 'avg' in key:
                if 'rate' in key:
                    print(f"  {key}: {value:.1%}")
                else:
                    print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"超级回测失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_super_system()
