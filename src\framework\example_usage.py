"""
统一回测框架使用示例
展示如何使用新的回测框架
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from framework import BacktestFramework, BacktestConfig
from framework.predictor_adapter import create_predictor_adapter
from framework.result_display import ResultDisplayer


def example_lottery_predictor():
    """使用LotteryPredictor的示例"""
    print("🎯 统一回测框架示例 - LotteryPredictor")
    print("=" * 60)
    
    try:
        # 导入原始预测器
        from systems.main import LotteryPredictor
        
        # 创建原始预测器
        print("📊 创建原始预测器...")
        original_predictor = LotteryPredictor()
        
        # 创建适配器
        print("🔧 创建预测器适配器...")
        predictor_adapter = create_predictor_adapter('lottery', original_predictor)
        
        # 创建回测框架
        print("🏗️ 创建回测框架...")
        framework = BacktestFramework(original_predictor.data)
        
        # 配置回测参数
        config = BacktestConfig(
            num_periods=10,           # 回测10期
            min_train_periods=50,     # 最少50期训练数据
            display_periods=5,        # 显示最近5期
            enable_detailed_output=True,
            enable_statistics=True,
            reverse_display=True
        )
        
        print(f"⚙️ 回测配置：{config.num_periods}期回测，最少{config.min_train_periods}期训练")
        
        # 运行回测
        print("\n🚀 开始运行回测...")
        result = framework.run_backtest(predictor_adapter, config)
        
        # 显示结果
        print("\n📋 显示回测结果...")
        displayer = ResultDisplayer()
        displayer.display_backtest_result(result)
        
        return result
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def example_advanced_system():
    """使用AdvancedProbabilisticSystem的示例"""
    print("\n🎯 统一回测框架示例 - AdvancedProbabilisticSystem")
    print("=" * 60)
    
    try:
        # 导入高级系统
        sys.path.append(str(project_root))
        from advanced_probabilistic_system import AdvancedProbabilisticSystem
        
        # 创建原始系统
        print("📊 创建高级概率系统...")
        original_system = AdvancedProbabilisticSystem()
        
        if not original_system.load_data():
            print("❌ 数据加载失败")
            return None
        
        original_system.initialize_system()
        
        # 创建适配器
        print("🔧 创建系统适配器...")
        system_adapter = create_predictor_adapter('advanced', original_system)
        
        # 创建回测框架
        print("🏗️ 创建回测框架...")
        framework = BacktestFramework(original_system.data)
        
        # 配置回测参数（专注于杀号测试）
        config = BacktestConfig(
            num_periods=10,
            min_train_periods=30,
            display_periods=5,
            metrics=['red_kill_success', 'blue_kill_success'],  # 只关注杀号指标
            enable_detailed_output=True,
            enable_statistics=True
        )
        
        # 运行回测
        print("\n🚀 开始运行杀号回测...")
        result = framework.run_backtest(system_adapter, config)
        
        # 显示结果
        print("\n📋 显示杀号回测结果...")
        displayer = ResultDisplayer()
        displayer.display_backtest_result(result)
        
        return result
        
    except Exception as e:
        print(f"❌ 高级系统示例运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def compare_predictors():
    """比较不同预测器的示例"""
    print("\n🎯 预测器性能比较")
    print("=" * 60)
    
    results = {}
    
    # 测试LotteryPredictor
    print("1️⃣ 测试 LotteryPredictor...")
    lottery_result = example_lottery_predictor()
    if lottery_result:
        results['LotteryPredictor'] = lottery_result
    
    # 测试AdvancedProbabilisticSystem
    print("\n2️⃣ 测试 AdvancedProbabilisticSystem...")
    advanced_result = example_advanced_system()
    if advanced_result:
        results['AdvancedProbabilisticSystem'] = advanced_result
    
    # 比较结果
    if len(results) > 1:
        print("\n📊 性能比较")
        print("=" * 60)
        
        for name, result in results.items():
            stats = result.statistics
            print(f"\n{name}:")
            print(f"  总体成功率: {result.get_success_rate():.1%}")
            print(f"  2+1命中率: {stats.hit_2_plus_1_rate:.1%}")
            
            # 杀号成功率
            kill_rates = list(stats.kill_success_rates.values())
            if kill_rates:
                avg_kill_rate = sum(kill_rates) / len(kill_rates)
                print(f"  杀号成功率: {avg_kill_rate:.1%}")
            
            print(f"  耗时: {result.total_duration:.2f}秒")
    
    return results


def demonstrate_key_features():
    """演示关键特性"""
    print("\n🎯 统一回测框架关键特性演示")
    print("=" * 60)
    
    print("✅ 1. 期号只作为标志，不作为循环条件")
    print("   - 循环基于数据索引：for data_index in range(min_train, max_backtest)")
    print("   - 期号获取：period_number = data.iloc[data_index]['期号']")
    print("   - 支持非连续期号数据")
    
    print("\n✅ 2. 统一的预测器接口")
    print("   - PredictorInterface.predict_for_period(data_index, data)")
    print("   - 标准化的输入输出格式")
    print("   - 适配器模式支持现有预测器")
    
    print("\n✅ 3. 标准化的回测流程")
    print("   - BacktestConfig: 统一的配置管理")
    print("   - BacktestResult: 标准化的结果格式")
    print("   - 可配置的评估指标和显示选项")
    
    print("\n✅ 4. 可扩展的架构")
    print("   - 支持添加新的预测器")
    print("   - 支持自定义评估器")
    print("   - 支持不同的显示格式")
    
    print("\n✅ 5. 高内聚低耦合设计")
    print("   - 回测框架独立于具体预测算法")
    print("   - 预测器通过接口与框架交互")
    print("   - 结果显示与计算逻辑分离")


def main():
    """主函数"""
    print("🎯 统一回测框架完整示例")
    print("=" * 80)
    
    # 演示关键特性
    demonstrate_key_features()
    
    # 运行单个预测器示例
    lottery_result = example_lottery_predictor()
    
    # 比较多个预测器（如果需要）
    # compare_results = compare_predictors()
    
    print("\n🎉 示例运行完成！")
    print("\n💡 使用说明：")
    print("1. 创建预测器适配器：adapter = create_predictor_adapter('lottery', predictor)")
    print("2. 创建回测框架：framework = BacktestFramework(data)")
    print("3. 配置回测参数：config = BacktestConfig(num_periods=10, ...)")
    print("4. 运行回测：result = framework.run_backtest(adapter, config)")
    print("5. 显示结果：displayer.display_backtest_result(result)")
    
    return lottery_result


if __name__ == "__main__":
    main()
