#!/usr/bin/env python3
"""
大乐透预测系统主运行脚本
优化后的统一入口程序
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import setup_logging, get_settings
from config.logging_config import get_logger
from src.utils.data_utils import load_data, validate_data, get_data_summary


def setup_environment():
    """设置运行环境"""
    # 设置日志
    setup_logging()
    logger = get_logger('main')
    
    # 加载配置
    settings = get_settings()
    
    logger.info(f"启动 {settings.project_name} v{settings.version}")
    logger.info(f"调试模式: {'开启' if settings.debug else '关闭'}")
    
    return settings, logger


def validate_environment(settings, logger):
    """验证运行环境"""
    logger.info("验证运行环境...")
    
    # 验证数据文件
    try:
        data = load_data()
        is_valid, errors = validate_data(data)
        
        if not is_valid:
            logger.error("数据验证失败:")
            for error in errors:
                logger.error(f"  - {error}")
            return False
        
        # 显示数据摘要
        summary = get_data_summary(data)
        logger.info(f"数据摘要: {summary}")
        
        return True
        
    except Exception as e:
        logger.error(f"环境验证失败: {e}")
        return False


def run_basic_system(settings, logger):
    """运行基础预测系统"""
    logger.info("启动基础预测系统...")
    
    try:
        # 导入原有的主程序
        from main import LotteryPredictor
        
        predictor = LotteryPredictor()
        predictor.run_backtest(
            num_periods=settings.backtest.default_periods,
            display_periods=settings.backtest.display_periods
        )
        
        logger.info("基础预测系统运行完成")
        return True
        
    except Exception as e:
        logger.error(f"基础预测系统运行失败: {e}")
        if settings.debug:
            import traceback
            logger.error(traceback.format_exc())
        return False


def run_advanced_system(settings, logger):
    """运行高级预测系统"""
    logger.info("启动高级预测系统...")
    
    try:
        # 尝试导入高级系统
        try:
            from advanced_prediction_system import AdvancedPredictionSystem
            system = AdvancedPredictionSystem()
        except ImportError:
            logger.warning("高级预测系统不可用，回退到基础系统")
            return run_basic_system(settings, logger)
        
        # 运行高级系统
        system.run_comprehensive_backtest()
        
        logger.info("高级预测系统运行完成")
        return True
        
    except Exception as e:
        logger.error(f"高级预测系统运行失败: {e}")
        if settings.debug:
            import traceback
            logger.error(traceback.format_exc())
        return False


def run_super_system(settings, logger):
    """运行超级预测系统"""
    logger.info("启动超级预测系统...")
    
    try:
        # 尝试导入超级系统
        try:
            from super_prediction_system import SuperPredictionSystem
            system = SuperPredictionSystem()
        except ImportError:
            logger.warning("超级预测系统不可用，回退到高级系统")
            return run_advanced_system(settings, logger)
        
        # 运行超级系统
        system.run_super_backtest()
        
        logger.info("超级预测系统运行完成")
        return True
        
    except Exception as e:
        logger.error(f"超级预测系统运行失败: {e}")
        if settings.debug:
            import traceback
            logger.error(traceback.format_exc())
        return False


def run_ultimate_system(settings, logger):
    """运行终极预测系统"""
    logger.info("启动终极预测系统...")
    
    try:
        # 尝试导入终极系统
        try:
            from ultimate_prediction_system import UltimatePredictionSystem
            system = UltimatePredictionSystem()
        except ImportError:
            logger.warning("终极预测系统不可用，回退到超级系统")
            return run_super_system(settings, logger)
        
        # 运行终极系统
        system.run_ultimate_backtest()
        
        logger.info("终极预测系统运行完成")
        return True
        
    except Exception as e:
        logger.error(f"终极预测系统运行失败: {e}")
        if settings.debug:
            import traceback
            logger.error(traceback.format_exc())
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='大乐透预测系统')
    parser.add_argument(
        '--mode', 
        choices=['basic', 'advanced', 'super', 'ultimate', 'auto'],
        default='auto',
        help='运行模式 (默认: auto)'
    )
    parser.add_argument(
        '--periods',
        type=int,
        help='回测期数'
    )
    parser.add_argument(
        '--display',
        type=int,
        help='显示期数'
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    parser.add_argument(
        '--validate-only',
        action='store_true',
        help='仅验证环境，不运行预测'
    )
    
    args = parser.parse_args()
    
    # 设置环境
    settings, logger = setup_environment()
    
    # 应用命令行参数
    if args.debug:
        settings.debug = True
    
    if args.periods:
        settings.backtest.default_periods = args.periods
    
    if args.display:
        settings.backtest.display_periods = args.display
    
    # 验证环境
    if not validate_environment(settings, logger):
        logger.error("环境验证失败，程序退出")
        sys.exit(1)
    
    if args.validate_only:
        logger.info("环境验证完成，程序退出")
        sys.exit(0)
    
    # 选择运行模式
    mode = args.mode
    success = False
    
    if mode == 'auto':
        # 自动选择最高可用的系统
        for system_runner in [run_ultimate_system, run_super_system, run_advanced_system, run_basic_system]:
            try:
                success = system_runner(settings, logger)
                if success:
                    break
            except Exception as e:
                logger.warning(f"系统运行失败，尝试下一个: {e}")
                continue
    else:
        # 运行指定模式
        system_runners = {
            'basic': run_basic_system,
            'advanced': run_advanced_system,
            'super': run_super_system,
            'ultimate': run_ultimate_system
        }
        
        runner = system_runners.get(mode)
        if runner:
            success = runner(settings, logger)
        else:
            logger.error(f"未知的运行模式: {mode}")
            sys.exit(1)
    
    # 退出程序
    if success:
        logger.info("程序运行完成")
        sys.exit(0)
    else:
        logger.error("程序运行失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
