#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断杀号一致性问题
检查为什么主程序和advanced_probabilistic_system中的杀号结果不一致
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.utils import load_data, parse_numbers
from advanced_probabilistic_system import AdvancedProbabilisticSystem
from advanced_red_kill_algorithm import AdvancedRedKillAlgorithm
from advanced_blue_kill_algorithm import AdvancedBlueKillAlgorithm


def test_consistency():
    """测试杀号一致性"""
    print("🔍 杀号一致性诊断")
    print("=" * 60)
    
    # 加载数据
    data = load_data('dlt_data.csv')
    print(f"✅ 加载数据: {len(data)} 期")
    
    # 固定使用前200期作为训练数据
    train_data = data.head(200)
    print(f"📊 训练数据: {len(train_data)} 期")
    
    # 构建固定的period_data
    period_data = {
        'current': train_data.iloc[0],
        'last': train_data.iloc[1],
        'prev2': train_data.iloc[2],
        'prev3': train_data.iloc[3],
        'prev4': train_data.iloc[4]
    }
    
    print(f"🎯 测试期号: {period_data['current']['期号']}")
    print()
    
    # 方法1：直接使用AdvancedProbabilisticSystem
    print("📋 方法1：使用AdvancedProbabilisticSystem")
    print("-" * 40)
    
    advanced_system = AdvancedProbabilisticSystem()
    advanced_system.data = train_data
    advanced_system.initialize_system()
    
    red_kills_1 = advanced_system.predict_red_kills(period_data, target_count=4)
    blue_kills_1 = advanced_system.predict_blue_kills(period_data, target_count=1)
    
    print(f"红球杀号: {sorted(red_kills_1)}")
    print(f"蓝球杀号: {blue_kills_1}")
    print()
    
    # 方法2：直接使用算法类
    print("📋 方法2：直接使用算法类")
    print("-" * 40)
    
    red_killer = AdvancedRedKillAlgorithm(train_data)
    blue_killer = AdvancedBlueKillAlgorithm(train_data)
    
    red_kills_2 = red_killer.predict_red_kills(period_data, target_count=4)
    blue_kills_2 = blue_killer.predict_blue_kills(period_data, target_count=1)
    
    print(f"红球杀号: {sorted(red_kills_2)}")
    print(f"蓝球杀号: {blue_kills_2}")
    print()
    
    # 方法3：再次使用AdvancedProbabilisticSystem（测试重复性）
    print("📋 方法3：再次使用AdvancedProbabilisticSystem")
    print("-" * 40)
    
    advanced_system_2 = AdvancedProbabilisticSystem()
    advanced_system_2.data = train_data
    advanced_system_2.initialize_system()
    
    red_kills_3 = advanced_system_2.predict_red_kills(period_data, target_count=4)
    blue_kills_3 = advanced_system_2.predict_blue_kills(period_data, target_count=1)
    
    print(f"红球杀号: {sorted(red_kills_3)}")
    print(f"蓝球杀号: {blue_kills_3}")
    print()
    
    # 一致性检查
    print("🔍 一致性检查")
    print("=" * 60)
    
    red_consistent_12 = sorted(red_kills_1) == sorted(red_kills_2)
    red_consistent_13 = sorted(red_kills_1) == sorted(red_kills_3)
    red_consistent_23 = sorted(red_kills_2) == sorted(red_kills_3)
    
    blue_consistent_12 = blue_kills_1 == blue_kills_2
    blue_consistent_13 = blue_kills_1 == blue_kills_3
    blue_consistent_23 = blue_kills_2 == blue_kills_3
    
    print(f"红球杀号一致性:")
    print(f"  方法1 vs 方法2: {'✅' if red_consistent_12 else '❌'}")
    print(f"  方法1 vs 方法3: {'✅' if red_consistent_13 else '❌'}")
    print(f"  方法2 vs 方法3: {'✅' if red_consistent_23 else '❌'}")
    print()
    
    print(f"蓝球杀号一致性:")
    print(f"  方法1 vs 方法2: {'✅' if blue_consistent_12 else '❌'}")
    print(f"  方法1 vs 方法3: {'✅' if blue_consistent_13 else '❌'}")
    print(f"  方法2 vs 方法3: {'✅' if blue_consistent_23 else '❌'}")
    print()
    
    # 参数检查
    print("🔧 参数检查")
    print("=" * 60)
    
    print(f"红球算法参数:")
    print(f"  贝叶斯权重: {red_killer.strategies['bayesian']}")
    print(f"  马尔可夫权重: {red_killer.strategies['markov']}")
    print(f"  贝叶斯历史期数: {red_killer.bayesian_history_periods}")
    print(f"  马尔可夫历史期数: {red_killer.markov_history_periods}")
    print(f"  相似度阈值: {red_killer.similarity_threshold}")
    print()
    
    print(f"蓝球算法参数:")
    print(f"  贝叶斯权重: {blue_killer.strategies['bayesian']}")
    print(f"  马尔可夫权重: {blue_killer.strategies['markov']}")
    print(f"  贝叶斯历史期数: {blue_killer.bayesian_history_periods}")
    print(f"  马尔可夫历史期数: {blue_killer.markov_history_periods}")
    print(f"  相似度阈值: {blue_killer.similarity_threshold}")
    print()
    
    # 如果不一致，分析原因
    if not (red_consistent_12 and red_consistent_13 and red_consistent_23):
        print("❌ 红球杀号不一致，分析原因...")
        analyze_red_differences(red_kills_1, red_kills_2, red_kills_3)
    
    if not (blue_consistent_12 and blue_consistent_13 and blue_consistent_23):
        print("❌ 蓝球杀号不一致，分析原因...")
        analyze_blue_differences(blue_kills_1, blue_kills_2, blue_kills_3)
    
    if (red_consistent_12 and red_consistent_13 and red_consistent_23 and
        blue_consistent_12 and blue_consistent_13 and blue_consistent_23):
        print("✅ 所有方法的杀号结果完全一致！")


def analyze_red_differences(kills1, kills2, kills3):
    """分析红球杀号差异"""
    print("🔍 红球杀号差异分析:")
    print(f"  方法1: {sorted(kills1)}")
    print(f"  方法2: {sorted(kills2)}")
    print(f"  方法3: {sorted(kills3)}")
    
    all_kills = set(kills1) | set(kills2) | set(kills3)
    print(f"  所有出现的杀号: {sorted(all_kills)}")
    
    common_kills = set(kills1) & set(kills2) & set(kills3)
    print(f"  共同杀号: {sorted(common_kills)}")
    
    unique_1 = set(kills1) - set(kills2) - set(kills3)
    unique_2 = set(kills2) - set(kills1) - set(kills3)
    unique_3 = set(kills3) - set(kills1) - set(kills2)
    
    if unique_1:
        print(f"  方法1独有: {sorted(unique_1)}")
    if unique_2:
        print(f"  方法2独有: {sorted(unique_2)}")
    if unique_3:
        print(f"  方法3独有: {sorted(unique_3)}")


def analyze_blue_differences(kills1, kills2, kills3):
    """分析蓝球杀号差异"""
    print("🔍 蓝球杀号差异分析:")
    print(f"  方法1: {kills1}")
    print(f"  方法2: {kills2}")
    print(f"  方法3: {kills3}")


def test_with_different_data():
    """测试使用不同数据时的结果"""
    print("\n🔄 测试不同数据窗口的影响")
    print("=" * 60)
    
    data = load_data('dlt_data.csv')
    
    # 测试不同的数据窗口
    windows = [
        (0, 200),   # 前200期
        (1, 201),   # 第2-201期
        (10, 210),  # 第11-211期
    ]
    
    results = []
    
    for i, (start, end) in enumerate(windows):
        print(f"📊 数据窗口 {i+1}: 第{start+1}-{end}期")
        
        train_data = data.iloc[start:end]
        period_data = {
            'current': train_data.iloc[0],
            'last': train_data.iloc[1],
            'prev2': train_data.iloc[2],
            'prev3': train_data.iloc[3],
            'prev4': train_data.iloc[4]
        }
        
        advanced_system = AdvancedProbabilisticSystem()
        advanced_system.data = train_data
        advanced_system.initialize_system()
        
        red_kills = advanced_system.predict_red_kills(period_data, target_count=4)
        blue_kills = advanced_system.predict_blue_kills(period_data, target_count=1)
        
        results.append((red_kills, blue_kills))
        print(f"  红球杀号: {sorted(red_kills)}")
        print(f"  蓝球杀号: {blue_kills}")
        print()
    
    # 检查不同数据窗口的结果是否一致
    print("🔍 不同数据窗口结果对比:")
    for i in range(len(results)):
        for j in range(i+1, len(results)):
            red_same = sorted(results[i][0]) == sorted(results[j][0])
            blue_same = results[i][1] == results[j][1]
            print(f"  窗口{i+1} vs 窗口{j+1}: 红球{'✅' if red_same else '❌'} 蓝球{'✅' if blue_same else '❌'}")


if __name__ == "__main__":
    test_consistency()
    test_with_different_data()
