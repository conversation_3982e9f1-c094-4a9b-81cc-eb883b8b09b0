#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化的红球杀号算法
"""

from src.systems.main import LotteryPredictor
import pandas as pd

def test_optimized_red_killer():
    """测试优化的红球杀号算法"""
    print("🎯 测试优化的红球杀号算法")
    print("=" * 60)
    
    # 初始化预测系统
    predictor = LotteryPredictor()
    
    # 测试前5期的预测
    for i in range(5):
        print(f"\n📅 测试第 {i+1} 期预测:")
        
        try:
            # 进行预测
            prediction = predictor.predict_next_period(i)
            
            # 显示杀号结果
            kill_numbers = prediction['kill_numbers']
            
            print(f"  期号: {prediction['period']}")
            print(f"  红球杀号: {kill_numbers['red_universal']} (共{len(kill_numbers['red_universal'])}个) [目标:13个]")
            print(f"  蓝球杀号: {kill_numbers['blue_universal']} (共{len(kill_numbers['blue_universal'])}个) [目标:5个]")
            print(f"  杀号成功率: {prediction['kill_success_rate']:.1%}")
            
            # 显示预测的号码组合
            enhanced_selection = prediction['enhanced_selection']
            print(f"  推荐号码: 红球{enhanced_selection[0]} 蓝球{enhanced_selection[1]}")
            
            # 分析红球杀号的分布
            red_kills = kill_numbers['red_universal']
            if red_kills:
                print(f"  红球杀号分析:")
                print(f"    最小号码: {min(red_kills)}")
                print(f"    最大号码: {max(red_kills)}")
                print(f"    号码范围: {max(red_kills) - min(red_kills)}")
                
                # 分析奇偶分布
                odd_count = sum(1 for num in red_kills if num % 2 == 1)
                even_count = len(red_kills) - odd_count
                print(f"    奇偶分布: 奇数{odd_count}个, 偶数{even_count}个")
                
                # 分析大小分布
                small_count = sum(1 for num in red_kills if num <= 17)
                large_count = len(red_kills) - small_count
                print(f"    大小分布: 小数{small_count}个, 大数{large_count}个")
            
        except Exception as e:
            print(f"  ❌ 预测失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 测试完成！")

def compare_algorithms():
    """对比优化前后的算法"""
    print("\n🔍 对比优化前后的红球杀号算法")
    print("=" * 60)
    
    try:
        # 加载数据
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 获取最近几期数据进行测试
        recent_periods = []
        for i in range(6):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                red_balls, _ = parse_numbers(data.iloc[i])
                recent_periods.append(red_balls)
        
        print(f"\n📊 使用最近{len(recent_periods)}期数据进行对比:")
        for i, period in enumerate(recent_periods):
            print(f"  第{i+1}期: {sorted(period)}")
        
        # 测试优化算法
        print(f"\n🚀 优化算法结果:")
        try:
            from optimized_red_ball_killer import OptimizedRedBallKiller
            optimized_killer = OptimizedRedBallKiller(data)
            optimized_kills = optimized_killer.calculate_optimized_red_kills(recent_periods, 13)
            print(f"  优化杀号: {sorted(optimized_kills)} (共{len(optimized_kills)}个)")
            
            # 分析优化杀号特征
            if optimized_kills:
                odd_count = sum(1 for num in optimized_kills if num % 2 == 1)
                small_count = sum(1 for num in optimized_kills if num <= 17)
                print(f"  特征分析: 奇数{odd_count}个, 偶数{13-odd_count}个, 小数{small_count}个, 大数{13-small_count}个")
                print(f"  号码范围: {min(optimized_kills)}-{max(optimized_kills)}")
        except Exception as e:
            print(f"  ❌ 优化算法失败: {e}")
        
        # 测试原始算法
        print(f"\n📈 原始算法结果:")
        try:
            from bayesian_markov_killer import BayesianMarkovKiller
            original_killer = BayesianMarkovKiller(data)
            
            # 构建期数据
            period_data = {}
            if len(recent_periods) >= 1:
                period_data['last'] = recent_periods[0]
            if len(recent_periods) >= 2:
                period_data['prev2'] = recent_periods[1]
            
            # 获取原始算法的各模型预测
            bayesian_kills = original_killer.red_bayesian.predict_kill_numbers(period_data, 13)
            markov1_kills = original_killer.red_markov1.predict_kill_numbers(period_data, 13)
            markov2_kills = original_killer.red_markov2.predict_kill_numbers(period_data, 13)
            
            print(f"  贝叶斯杀号: {sorted(bayesian_kills[:13])}")
            print(f"  马尔科夫1杀号: {sorted(markov1_kills[:13])}")
            print(f"  马尔科夫2杀号: {sorted(markov2_kills[:13])}")
            
            # 集成结果
            ensemble_kills = original_killer._ensemble_vote(
                bayesian_kills, markov1_kills, markov2_kills,
                original_killer.red_weights, 13
            )
            print(f"  集成杀号: {sorted(ensemble_kills)} (共{len(ensemble_kills)}个)")
            
        except Exception as e:
            print(f"  ❌ 原始算法失败: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_red_killer()
    compare_algorithms()
