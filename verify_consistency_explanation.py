#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证杀号一致性问题的解释
证明不同训练数据窗口会导致不同的杀号结果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.utils import load_data, parse_numbers
from advanced_probabilistic_system import AdvancedProbabilisticSystem


def test_different_training_windows():
    """测试不同训练数据窗口对杀号结果的影响"""
    print("🔍 验证不同训练数据窗口对杀号结果的影响")
    print("=" * 60)
    
    # 加载数据
    data = load_data('dlt_data.csv')
    print(f"✅ 加载数据: {len(data)} 期")
    print()
    
    # 测试3个不同的训练数据窗口
    windows = [
        (1, 201),   # 第2-201期 (模拟处理第1期时的训练数据)
        (2, 202),   # 第3-202期 (模拟处理第2期时的训练数据)
        (3, 203),   # 第4-203期 (模拟处理第3期时的训练数据)
    ]
    
    results = []
    
    for i, (start, end) in enumerate(windows):
        print(f"📊 测试窗口 {i+1}: 第{start+1}-{end}期 (共200期)")
        
        # 使用不同的训练数据
        train_data = data.iloc[start:end]
        
        # 构建period_data (使用训练数据的前5期)
        period_data = {
            'current': train_data.iloc[0],
            'last': train_data.iloc[1],
            'prev2': train_data.iloc[2],
            'prev3': train_data.iloc[3],
            'prev4': train_data.iloc[4]
        }
        
        # 创建系统实例
        advanced_system = AdvancedProbabilisticSystem()
        advanced_system.data = train_data
        advanced_system.initialize_system()
        
        # 预测杀号
        red_kills = advanced_system.predict_red_kills(period_data, target_count=5)
        blue_kills = advanced_system.predict_blue_kills(period_data, target_count=1)
        
        results.append((red_kills, blue_kills))
        
        print(f"  预测期号: {period_data['current']['期号']}")
        print(f"  红球杀号: {sorted(red_kills)}")
        print(f"  蓝球杀号: {blue_kills}")
        print()
    
    # 分析结果差异
    print("🔍 结果差异分析:")
    print("=" * 60)
    
    for i in range(len(results)):
        for j in range(i+1, len(results)):
            red_same = sorted(results[i][0]) == sorted(results[j][0])
            blue_same = results[i][1] == results[j][1]
            
            print(f"窗口{i+1} vs 窗口{j+1}:")
            print(f"  红球杀号: {'✅ 相同' if red_same else '❌ 不同'}")
            print(f"  蓝球杀号: {'✅ 相同' if blue_same else '❌ 不同'}")
            
            if not red_same:
                red1, red2 = sorted(results[i][0]), sorted(results[j][0])
                common = set(red1) & set(red2)
                diff1 = set(red1) - set(red2)
                diff2 = set(red2) - set(red1)
                print(f"    共同杀号: {sorted(common)}")
                print(f"    窗口{i+1}独有: {sorted(diff1)}")
                print(f"    窗口{j+1}独有: {sorted(diff2)}")
            
            if not blue_same:
                print(f"    窗口{i+1}蓝球: {results[i][1]}")
                print(f"    窗口{j+1}蓝球: {results[j][1]}")
            
            print()
    
    # 解释原因
    print("💡 原因解释:")
    print("=" * 60)
    print("1. **训练数据不同**：每个窗口使用不同的200期历史数据")
    print("2. **学习模式不同**：算法从不同的历史数据中学习到不同的模式")
    print("3. **概率分布变化**：贝叶斯先验概率和条件概率会发生变化")
    print("4. **状态转移变化**：马尔可夫状态转移矩阵会因数据不同而变化")
    print()
    print("✅ **这是正确的行为**：")
    print("   - 避免数据泄露：不使用未来数据进行预测")
    print("   - 动态适应：能够学习到最新的数据模式")
    print("   - 时间序列特性：最近的数据更有参考价值")
    print()
    print("🔧 **如需一致结果**：")
    print("   - 使用相同的训练数据窗口")
    print("   - 在同一时间点进行预测")
    print("   - 缓存算法实例避免重复初始化")


def test_same_training_window():
    """测试相同训练数据窗口的一致性"""
    print("\n🔍 验证相同训练数据窗口的一致性")
    print("=" * 60)
    
    # 加载数据
    data = load_data('dlt_data.csv')
    
    # 固定使用前200期作为训练数据
    train_data = data.head(200)
    
    # 构建固定的period_data
    period_data = {
        'current': train_data.iloc[0],
        'last': train_data.iloc[1],
        'prev2': train_data.iloc[2],
        'prev3': train_data.iloc[3],
        'prev4': train_data.iloc[4]
    }
    
    print(f"📊 固定训练数据: 前200期")
    print(f"🎯 预测期号: {period_data['current']['期号']}")
    print()
    
    # 进行3次独立预测
    results = []
    for i in range(3):
        print(f"第{i+1}次预测:")
        
        # 创建新的系统实例
        advanced_system = AdvancedProbabilisticSystem()
        advanced_system.data = train_data
        advanced_system.initialize_system()
        
        # 预测杀号
        red_kills = advanced_system.predict_red_kills(period_data, target_count=5)
        blue_kills = advanced_system.predict_blue_kills(period_data, target_count=1)
        
        results.append((red_kills, blue_kills))
        
        print(f"  红球杀号: {sorted(red_kills)}")
        print(f"  蓝球杀号: {blue_kills}")
        print()
    
    # 检查一致性
    print("🔍 一致性检查:")
    print("=" * 30)
    
    all_red_same = all(sorted(results[0][0]) == sorted(r[0]) for r in results[1:])
    all_blue_same = all(results[0][1] == r[1] for r in results[1:])
    
    print(f"红球杀号一致性: {'✅ 完全一致' if all_red_same else '❌ 存在差异'}")
    print(f"蓝球杀号一致性: {'✅ 完全一致' if all_blue_same else '❌ 存在差异'}")
    
    if all_red_same and all_blue_same:
        print("\n✅ 结论：使用相同训练数据时，杀号结果完全一致！")
    else:
        print("\n⚠️ 注意：即使使用相同训练数据，结果仍有差异")
        print("   可能原因：算法中存在随机性成分")


if __name__ == "__main__":
    test_different_training_windows()
    test_same_training_window()
