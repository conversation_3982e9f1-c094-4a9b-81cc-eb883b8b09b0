"""
日志管理器
将调试信息输出到logs文件夹，主要预测结果输出到控制台
"""

import os
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any


class LotteryLogger:
    """彩票预测日志管理器"""

    def __init__(self):
        """初始化日志管理器"""
        # 创建logs目录
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)

        # 设置日志文件路径
        timestamp = datetime.now().strftime("%Y%m%d")
        self.debug_log_file = self.logs_dir / f"debug_{timestamp}.txt"
        self.prediction_log_file = self.logs_dir / f"prediction_{timestamp}.txt"
        self.kill_analysis_log_file = self.logs_dir / f"kill_analysis_{timestamp}.txt"

        # 配置调试日志
        self.debug_logger = logging.getLogger('debug')
        self.debug_logger.setLevel(logging.DEBUG)

        # 清除现有处理器
        self.debug_logger.handlers.clear()

        # 添加文件处理器
        debug_handler = logging.FileHandler(self.debug_log_file, encoding='utf-8')
        debug_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        debug_handler.setFormatter(debug_formatter)
        self.debug_logger.addHandler(debug_handler)

        print(f"📝 日志系统初始化完成")
        print(f"   调试日志: {self.debug_log_file}")
        print(f"   预测日志: {self.prediction_log_file}")
        print(f"   杀号分析: {self.kill_analysis_log_file}")

    def log_debug(self, message: str):
        """记录调试信息到文件"""
        self.debug_logger.debug(message)

    def log_prediction_details(self, period: str, prediction_data: Dict):
        """记录详细预测信息到文件"""
        with open(self.prediction_log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"期号 {period} 详细预测信息\n")
            f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*60}\n")

            # 记录训练数据信息
            if 'training_info' in prediction_data:
                info = prediction_data['training_info']
                f.write(f"训练数据: {info.get('range', 'N/A')}, 共{info.get('count', 0)}期\n")

            # 记录算法详情
            if 'algorithm_details' in prediction_data:
                details = prediction_data['algorithm_details']
                f.write(f"算法详情:\n")
                for key, value in details.items():
                    f.write(f"  {key}: {value}\n")

            # 记录置信度信息
            if 'confidence_scores' in prediction_data:
                scores = prediction_data['confidence_scores']
                f.write(f"置信度评分:\n")
                for key, score in scores.items():
                    f.write(f"  {key}: {score:.3f}\n")

            f.write(f"\n")

    def log_kill_analysis(self, period: str, kill_data: Dict):
        """记录杀号分析信息到文件"""
        with open(self.kill_analysis_log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*50}\n")
            f.write(f"期号 {period} 杀号分析\n")
            f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*50}\n")

            # 记录杀号策略
            if 'strategy' in kill_data:
                f.write(f"杀号策略: {kill_data['strategy']}\n")

            # 记录候选杀号
            if 'candidates' in kill_data:
                f.write(f"候选杀号:\n")
                for ball_type, candidates in kill_data['candidates'].items():
                    f.write(f"  {ball_type}: {candidates}\n")

            # 记录评分详情
            if 'scores' in kill_data:
                f.write(f"评分详情:\n")
                for ball_type, scores in kill_data['scores'].items():
                    f.write(f"  {ball_type}:\n")
                    for num, score in scores.items():
                        f.write(f"    {num:02d}: {score:.2f}\n")

            # 记录最终选择
            if 'final_kills' in kill_data:
                f.write(f"最终杀号:\n")
                for ball_type, kills in kill_data['final_kills'].items():
                    f.write(f"  {ball_type}: {kills}\n")

            f.write(f"\n")

    def print_formatted_prediction(self, base_period: str, target_period: str,
                                 prediction: Dict, actual: Dict = None):
        """打印格式化的预测结果"""
        print(f"基于第{base_period}期预测第{target_period}期:")
        print()
        print("红球")

        # 红球奇偶比
        try:
            pred_odd_even = prediction['predictions']['red_odd_even'][0]  # 取第一个预测
            odd_even_str, odd_even_prob = pred_odd_even
            if actual and 'red_odd_even' in actual:
                actual_odd_even = actual['red_odd_even']
                hit_status = "命中" if odd_even_str == actual_odd_even else "未中"
                print(f"奇偶比: 预测[{odd_even_str}({odd_even_prob:.3f})] -> 实际[{actual_odd_even}] ({hit_status})")
            else:
                print(f"奇偶比: 预测[{odd_even_str}({odd_even_prob:.3f})] -> 实际[待开奖] (待验证)")
        except (KeyError, IndexError, TypeError):
            print("奇偶比: 预测数据不可用")

        # 红球大小比
        try:
            pred_size = prediction['predictions']['red_size'][0]  # 取第一个预测
            size_str, size_prob = pred_size
            if actual and 'red_size' in actual:
                actual_size = actual['red_size']
                hit_status = "命中" if size_str == actual_size else "未中"
                print(f"大小比: 预测[{size_str}({size_prob:.3f})] -> 实际[{actual_size}] ({hit_status})")
            else:
                print(f"大小比: 预测[{size_str}({size_prob:.3f})] -> 实际[待开奖] (待验证)")
        except (KeyError, IndexError, TypeError):
            print("大小比: 预测数据不可用")

        print()
        print("蓝球")

        # 蓝球大小比
        try:
            pred_blue_size = prediction['predictions']['blue_size'][0]  # 取第一个预测
            blue_size_str, blue_size_prob = pred_blue_size
            if actual and 'blue_size' in actual:
                actual_blue_size = actual['blue_size']
                hit_status = "命中" if blue_size_str == actual_blue_size else "未中"
                print(f"大小比: 预测[{blue_size_str}({blue_size_prob:.3f})] -> 实际[{actual_blue_size}] ({hit_status})")
            else:
                print(f"大小比: 预测[{blue_size_str}({blue_size_prob:.3f})] -> 实际[待开奖] (待验证)")
        except (KeyError, IndexError, TypeError):
            print("大小比: 预测数据不可用")

        print()

    def print_formatted_kill_info(self, kill_numbers: Dict, actual_numbers: Tuple = None,
                                success_rate: float = None):
        """打印格式化的杀号信息"""
        if actual_numbers:
            actual_red, actual_blue = actual_numbers

            # 检查杀号成功情况
            red_kills = kill_numbers.get('red_universal', [])
            blue_kills = kill_numbers.get('blue_universal', [])

            kill_parts = []

            # 红球杀号
            if red_kills:
                red_success = not any(k in actual_red for k in red_kills)
                status = "✅" if red_success else "❌"
                kill_str = f"红球({','.join([f'{k:02d}' for k in red_kills])}){status}"
                kill_parts.append(kill_str)

            # 蓝球杀号
            if blue_kills:
                blue_success = not any(k in actual_blue for k in blue_kills)
                status = "✅" if blue_success else "❌"
                kill_str = f"蓝球({','.join([f'{k:02d}' for k in blue_kills])}){status}"
                kill_parts.append(kill_str)

            if kill_parts:
                print(f"杀号：{', '.join(kill_parts)}")
                if success_rate is not None:
                    print(f"成功率：{success_rate:.0%}")
            else:
                print("杀号：无")
        else:
            # 下一期预测格式
            red_kills = kill_numbers.get('red_universal', [])
            blue_kills = kill_numbers.get('blue_universal', [])

            kill_parts = []
            if red_kills:
                kill_str = f"红球({','.join([f'{k:02d}' for k in red_kills])})"
                kill_parts.append(kill_str)
            if blue_kills:
                kill_str = f"蓝球({','.join([f'{k:02d}' for k in blue_kills])})"
                kill_parts.append(kill_str)

            if kill_parts:
                print(f"杀号：{', '.join(kill_parts)}")
                if success_rate is not None:
                    print(f"预期成功率：{success_rate:.0%}")
            else:
                print("杀号：无")

        print()

    def print_formatted_numbers(self, predicted_numbers: List[Tuple[List[int], List[int]]],
                              actual_numbers: Tuple[List[int], List[int]] = None):
        """打印格式化的号码信息"""
        if predicted_numbers:
            # 显示最佳预测组合
            best_red, best_blue = predicted_numbers[0]
            pred_str = f"{','.join([f'{n:02d}' for n in best_red])}——{','.join([f'{n:02d}' for n in best_blue])}"
            print(f"预测号码：{pred_str}")

        if actual_numbers:
            actual_red, actual_blue = actual_numbers
            actual_str = f"{','.join([f'{n:02d}' for n in actual_red])}——{','.join([f'{n:02d}' for n in actual_blue])}"
            print(f"实际开奖号码：{actual_str}")
        else:
            print("实际开奖号码：待开奖——待开奖")


# 全局日志管理器实例
lottery_logger = LotteryLogger()
