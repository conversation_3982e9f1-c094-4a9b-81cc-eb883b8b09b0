"""
蓝球专用杀号算法
专门针对蓝球（1-12）的杀号策略
"""

import pandas as pd
from typing import List, Dict, Tuple
from collections import Counter
import numpy as np


class BlueKiller:
    """蓝球专用杀号器"""
    
    def __init__(self):
        self.blue_range = list(range(1, 13))  # 蓝球范围1-12
        self.historical_stats = {}
    
    def calculate_blue_kill_numbers(self, prev_two_periods: List[List[int]],
                                   historical_data: pd.DataFrame = None) -> List[int]:
        """
        计算蓝球杀号 - 精准打击策略（目标：2个杀号，成功率≥80%）

        Args:
            prev_two_periods: 上二期的蓝球号码 [[上一期], [上二期]]
            historical_data: 历史数据用于统计分析

        Returns:
            List[int]: 蓝球杀号列表 (2个高精度杀号)
        """
        if len(prev_two_periods) < 2:
            return []

        period1 = prev_two_periods[0]  # 上一期蓝球
        period2 = prev_two_periods[1]  # 上二期蓝球

        # 更新历史统计数据
        if historical_data is not None:
            self._update_blue_historical_stats(historical_data)

        # 收集所有算法的杀号建议
        algorithm_votes = {}  # {号码: [支持的算法列表]}

        # 算法1: 蓝球相加法
        blue_sum_kill = self._calculate_blue_sum_kill(period1, period2)
        for num in blue_sum_kill:
            if num not in algorithm_votes:
                algorithm_votes[num] = []
            algorithm_votes[num].append('sum')

        # 算法2: 蓝球相减法
        blue_diff_kill = self._calculate_blue_diff_kill(period1, period2)
        for num in blue_diff_kill:
            if num not in algorithm_votes:
                algorithm_votes[num] = []
            algorithm_votes[num].append('diff')

        # 算法3: 蓝球倍数法
        blue_multiple_kill = self._calculate_blue_multiple_kill(period1, period2)
        for num in blue_multiple_kill:
            if num not in algorithm_votes:
                algorithm_votes[num] = []
            algorithm_votes[num].append('multiple')

        # 算法4: 蓝球奇偶法
        blue_odd_even_kill = self._calculate_blue_odd_even_kill(period1, period2)
        for num in blue_odd_even_kill:
            if num not in algorithm_votes:
                algorithm_votes[num] = []
            algorithm_votes[num].append('odd_even')

        # 精准筛选：计算每个候选蓝球的安全性评分
        safe_kills = []
        for num, algorithms in algorithm_votes.items():
            safety_score = self._calculate_blue_kill_safety_score(num, algorithms, period1, period2, historical_data)
            if safety_score >= 60:  # 只有高分才能通过
                safe_kills.append((num, safety_score))

        # 按安全性评分排序，选择前2个
        safe_kills.sort(key=lambda x: x[1], reverse=True)
        final_kills = [num for num, score in safe_kills[:2]]

        return final_kills

    def _calculate_blue_kill_safety_score(self, num: int, algorithms: List[str], period1: List[int], period2: List[int],
                                         historical_data: pd.DataFrame = None) -> float:
        """
        计算蓝球杀号的安全性评分 (0-100分)

        Args:
            num: 候选蓝球杀号
            algorithms: 支持该杀号的算法列表
            period1: 上一期蓝球
            period2: 上二期蓝球
            historical_data: 历史数据

        Returns:
            float: 安全性评分 (0-100)
        """
        score = 0.0

        # 1. 算法一致性评分 (0-30分)
        algorithm_score = len(algorithms) * 7.5  # 每个算法支持+7.5分
        score += min(algorithm_score, 30)

        # 2. 热门度安全性评分 (0-25分)
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            score -= 50  # 严重扣分，基本不可能通过
        else:
            score += 25

        # 3. 历史频率安全性评分 (0-25分)
        if historical_data is not None and self.historical_stats:
            freq = self.historical_stats['blue_freq'].get(num, 0)
            if freq <= 1:
                score += 25
            elif freq <= 3:
                score += 15
            elif freq <= 5:
                score += 5
            else:
                score -= 10

        # 4. 最近出现安全性评分 (0-20分)
        all_recent = set(period1 + period2)
        if num in all_recent:
            score -= 30  # 最近出现过，严重扣分
        else:
            score += 20

        # 5. 边界位置安全性评分 (0-10分)
        if num in [1, 12]:
            score -= 15  # 极端边界扣分
        elif num in [2, 11]:
            score -= 5   # 边界扣分
        else:
            score += 10

        # 6. 距离安全性评分 (0-10分)
        if all_recent:
            min_distance = min(abs(num - recent) for recent in all_recent)
            if min_distance >= 4:
                score += 10
            elif min_distance >= 2:
                score += 5
            elif min_distance >= 1:
                score += 2

        return max(0, score)  # 确保评分不为负

    def _calculate_blue_sum_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """算法1: 蓝球相加法 - 两期蓝球相加"""
        kills = []

        if period1 and period2:
            # 两期蓝球直接相加
            blue_sum = period1[0] + period2[0]
            if blue_sum > 12:
                blue_sum = blue_sum % 12
                if blue_sum == 0:
                    blue_sum = 12
            if 1 <= blue_sum <= 12:
                kills.append(blue_sum)

            # 两期蓝球相加再除以2
            blue_avg = (period1[0] + period2[0]) // 2
            if 1 <= blue_avg <= 12:
                kills.append(blue_avg)

        return kills

    def _calculate_blue_diff_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """算法2: 蓝球相减法 - 两期蓝球相减"""
        kills = []

        if period1 and period2:
            # 两期蓝球相减的绝对值
            blue_diff = abs(period1[0] - period2[0])
            if blue_diff == 0:
                blue_diff = 6  # 如果相等，取中间值
            if 1 <= blue_diff <= 12:
                kills.append(blue_diff)

            # 相减后加6
            blue_diff_plus = blue_diff + 6
            if blue_diff_plus > 12:
                blue_diff_plus = blue_diff_plus % 12
                if blue_diff_plus == 0:
                    blue_diff_plus = 12
            if 1 <= blue_diff_plus <= 12:
                kills.append(blue_diff_plus)

        return kills

    def _calculate_blue_multiple_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """算法3: 蓝球倍数法 - 基于倍数关系计算"""
        kills = []

        if period1 and period2:
            blue1, blue2 = period1[0], period2[0]

            # 两期蓝球的乘积模12
            blue_product = (blue1 * blue2) % 12
            if blue_product == 0:
                blue_product = 12
            if 1 <= blue_product <= 12:
                kills.append(blue_product)

            # 较大数除以较小数的余数
            if blue1 != blue2:
                larger = max(blue1, blue2)
                smaller = min(blue1, blue2)
                remainder = larger % smaller
                if remainder == 0:
                    remainder = smaller
                if 1 <= remainder <= 12:
                    kills.append(remainder)

        return kills

    def _calculate_blue_odd_even_kill(self, period1: List[int], period2: List[int]) -> List[int]:
        """算法4: 蓝球奇偶法 - 基于奇偶性计算"""
        kills = []

        if period1 and period2:
            blue1, blue2 = period1[0], period2[0]

            # 如果两期都是奇数，杀偶数
            if blue1 % 2 == 1 and blue2 % 2 == 1:
                # 杀最小的偶数
                kills.append(2)

            # 如果两期都是偶数，杀奇数
            elif blue1 % 2 == 0 and blue2 % 2 == 0:
                # 杀最小的奇数
                kills.append(1)

            # 如果一奇一偶，基于位置计算
            else:
                # 奇偶位置差值
                position_diff = abs(blue1 - blue2)
                if position_diff % 2 == 0:
                    # 差值为偶数，杀中间偶数
                    middle_even = 6
                    kills.append(middle_even)
                else:
                    # 差值为奇数，杀中间奇数
                    middle_odd = 7
                    kills.append(middle_odd)

        return kills

    def _get_blue_historical_verified_kills(self, period1: List[int], period2: List[int],
                                           historical_data: pd.DataFrame = None, exclude: set = None) -> List[int]:
        """获取蓝球历史验证的杀号"""
        if exclude is None:
            exclude = set()

        kills = []
        all_recent = set(period1 + period2)

        # 基于历史频率的蓝球杀号
        if historical_data is not None and self.historical_stats:
            blue_freq = self.historical_stats['blue_freq']

            # 选择频率最低的蓝球
            freq_sorted = sorted(range(1, 13), key=lambda x: blue_freq.get(x, 0))

            for num in freq_sorted:
                if (num not in exclude and
                    num not in all_recent and
                    len(kills) < 5):  # 最多补充5个
                    kills.append(num)

        return kills

    def _is_ultra_safe_blue_kill(self, num: int, period1: List[int], period2: List[int],
                                historical_data: pd.DataFrame = None) -> bool:
        """检查蓝球是否超安全杀除 - 最严格版本"""
        # 绝对不杀除超级热门蓝球
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            return False

        # 绝对不杀除极端边界号码
        if num in [1, 12]:
            return False

        # 检查历史频率 - 只杀除真正的冷号
        if historical_data is not None and self.historical_stats:
            freq = self.historical_stats['blue_freq'].get(num, 0)
            if freq > 3:  # 频率太高，不安全
                return False

        return True

    def _get_ultra_precise_blue_math_kills(self, period1: List[int], period2: List[int],
                                          historical_data: pd.DataFrame = None) -> List[int]:
        """获取超精准的蓝球数学公式杀号"""
        math_kills = []
        all_recent = set(period1 + period2)

        try:
            # 公式1: 超保守蓝球和值模运算
            sum_mod = (sum(period1) + sum(period2)) % 12 + 1
            if 1 <= sum_mod <= 12 and sum_mod not in all_recent:
                if self._is_ultra_safe_blue_math_result(sum_mod, period1, period2, historical_data):
                    math_kills.append(sum_mod)

            # 公式2: 超保守蓝球差值运算
            if period1 and period2:
                diff_result = abs(period1[0] - period2[0]) % 12 + 1
                if 1 <= diff_result <= 12 and diff_result not in all_recent:
                    if self._is_ultra_safe_blue_math_result(diff_result, period1, period2, historical_data):
                        math_kills.append(diff_result)

        except Exception:
            pass

        return list(set(math_kills))

    def _get_ultra_cold_blue_kills(self, period1: List[int], period2: List[int],
                                  historical_data: pd.DataFrame) -> List[int]:
        """获取超冷蓝球杀号"""
        if not self.historical_stats:
            return []

        blue_freq = self.historical_stats['blue_freq']
        all_recent = set(period1 + period2)

        # 选择频率极低且最近没出现的蓝球
        ultra_cold_candidates = []
        for num in self.blue_range:
            if num not in all_recent:
                freq = blue_freq.get(num, 0)
                # 超严格条件：频率≤1的才考虑
                if freq <= 1:
                    # 额外安全检查
                    if self._is_ultra_safe_blue_cold_number(num, period1, period2, historical_data):
                        ultra_cold_candidates.append((num, freq))

        # 按频率排序，选择最冷的
        ultra_cold_candidates.sort(key=lambda x: x[1])
        return [num for num, freq in ultra_cold_candidates[:2]]  # 返回前2个最冷的

    def _is_ultra_safe_blue_math_result(self, num: int, period1: List[int], period2: List[int],
                                       historical_data: pd.DataFrame = None) -> bool:
        """检查蓝球数学公式结果是否超安全"""
        # 避免超级热门蓝球
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            return False

        # 避免极端边界
        if num in [1, 12]:
            return False

        # 检查历史频率
        if historical_data is not None and self.historical_stats:
            freq = self.historical_stats['blue_freq'].get(num, 0)
            if freq > 3:  # 频率太高
                return False

        return True

    def _is_ultra_safe_blue_cold_number(self, num: int, period1: List[int], period2: List[int],
                                       historical_data: pd.DataFrame = None) -> bool:
        """检查蓝球冷号是否超安全杀除"""
        # 避免超级热门蓝球
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            return False

        # 避免极端边界
        if num in [1, 12]:
            return False

        return True

    def _get_ultra_safe_additional_blue_kills(self, period1: List[int], period2: List[int],
                                             historical_data: pd.DataFrame = None, exclude: set = None) -> List[int]:
        """获取超安全的额外蓝球杀号以补充到3个"""
        if exclude is None:
            exclude = set()

        all_recent = set(period1 + period2)
        additional_kills = []

        # 超保守策略：只选择距离远且频率低的蓝球
        for num in range(2, 12):  # 避开极端边界1和12
            if num not in exclude and num not in all_recent:
                # 检查历史频率
                if historical_data is not None and self.historical_stats:
                    freq = self.historical_stats['blue_freq'].get(num, 0)
                    if freq > 2:  # 频率太高，跳过
                        continue

                # 计算与最近蓝球的最小距离
                if all_recent:
                    min_distance = min(abs(num - recent) for recent in all_recent)
                    if min_distance >= 4:  # 距离要求更严格
                        additional_kills.append((num, min_distance, freq))

        # 按距离和频率综合排序
        additional_kills.sort(key=lambda x: (x[1], -x[2]), reverse=True)

        return [num for num, distance, freq in additional_kills]

    def _calculate_blue_safety_score_v2(self, num: int, period1: List[int], period2: List[int],
                                       historical_data: pd.DataFrame = None) -> float:
        """计算蓝球的超精准安全性评分"""
        score = 0.0

        # 1. 基础安全性 (权重更高)
        all_recent = set(period1 + period2)
        if num not in all_recent:
            score += 50.0

        # 2. 历史频率安全性 (更严格)
        if historical_data is not None and self.historical_stats:
            freq = self.historical_stats['blue_freq'].get(num, 0)
            if freq == 0:
                score += 40.0
            elif freq <= 1:
                score += 35.0
            elif freq <= 2:
                score += 25.0
            elif freq <= 3:
                score += 15.0
            else:
                score -= 20.0  # 频率高的严重扣分

        # 3. 距离安全性 (更严格)
        if all_recent:
            min_distance = min(abs(num - recent) for recent in all_recent)
            if min_distance >= 5:
                score += 30.0
            elif min_distance >= 3:
                score += 20.0
            elif min_distance >= 2:
                score += 10.0
            else:
                score -= 10.0

        # 4. 位置安全性 (更严格的边界惩罚)
        if num in [1, 12]:
            score -= 30.0  # 极端边界严重扣分
        elif num in [2, 11]:
            score -= 15.0

        # 5. 超级热门蓝球严重惩罚
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            score -= 40.0  # 严重扣分

        return score

    def _ultra_blue_safety_check(self, num: int, period1: List[int], period2: List[int],
                                historical_data: pd.DataFrame = None) -> bool:
        """蓝球超严格的安全性检查"""
        # 1. 基本范围检查
        if not (1 <= num <= 12):
            return False

        # 2. 最近出现检查
        all_recent = set(period1 + period2)
        if num in all_recent:
            return False

        # 3. 超严格安全性评分检查
        safety_score = self._calculate_blue_safety_score_v2(num, period1, period2, historical_data)
        if safety_score < 15.0:  # 大幅提高安全性要求
            return False

        # 4. 超级热门蓝球绝对不杀
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            return False

        return True

    def _update_blue_historical_stats(self, historical_data: pd.DataFrame) -> None:
        """更新蓝球历史统计数据"""
        if historical_data.empty:
            return

        blue_freq = Counter()

        # 统计蓝球频率 - 使用parse_numbers函数
        from src.utils.utils import parse_numbers

        for _, row in historical_data.iterrows():
            try:
                red_balls, blue_balls = parse_numbers(row)
                for blue_num in blue_balls:
                    if 1 <= blue_num <= 12:
                        blue_freq[blue_num] += 1
            except Exception:
                continue

        self.historical_stats = {
            'blue_freq': blue_freq,
            'total_periods': len(historical_data)
        }
    
    def _is_safe_blue_kill(self, num: int, period1: List[int], period2: List[int], 
                          historical_data: pd.DataFrame = None) -> bool:
        """检查蓝球是否安全杀除"""
        # 避免杀除超级热门蓝球
        ultra_hot_blues = [1, 3, 7, 10]  # 历史超级热门蓝球
        if num in ultra_hot_blues:
            return False
        
        # 避免杀除极端边界号码
        if num in [1, 12]:
            return False
        
        return True
    
    def _get_blue_math_kills(self, period1: List[int], period2: List[int]) -> List[int]:
        """获取蓝球数学公式杀号"""
        math_kills = []
        all_recent = set(period1 + period2)
        
        try:
            # 公式1: 蓝球和值模运算
            sum_mod = (sum(period1) + sum(period2)) % 12 + 1
            if 1 <= sum_mod <= 12 and sum_mod not in all_recent:
                math_kills.append(sum_mod)
            
            # 公式2: 蓝球差值运算
            if period1 and period2:
                diff_result = abs(period1[0] - period2[0]) % 12 + 1
                if 1 <= diff_result <= 12 and diff_result not in all_recent:
                    math_kills.append(diff_result)
            
            # 公式3: 蓝球奇偶运算
            odd_count = sum(1 for num in period1 + period2 if num % 2 == 1)
            even_count = len(period1 + period2) - odd_count
            if odd_count > 0 and even_count > 0:
                ratio_result = (odd_count * even_count) % 12 + 1
                if ratio_result not in all_recent:
                    math_kills.append(ratio_result)
        
        except Exception:
            pass
        
        return list(set(math_kills))
    
    def _get_blue_cold_kills(self, period1: List[int], period2: List[int], 
                            historical_data: pd.DataFrame) -> List[int]:
        """获取蓝球冷号杀号"""
        if not self.historical_stats:
            return []
        
        blue_freq = self.historical_stats['blue_freq']
        all_recent = set(period1 + period2)
        
        # 选择频率较低且最近没出现的蓝球
        cold_candidates = []
        for num in self.blue_range:
            if num not in all_recent:
                freq = blue_freq.get(num, 0)
                # 频率≤2的蓝球考虑杀除
                if freq <= 2:
                    cold_candidates.append((num, freq))
        
        # 按频率排序，选择最冷的
        cold_candidates.sort(key=lambda x: x[1])
        return [num for num, freq in cold_candidates[:2]]  # 返回前2个最冷的
    
    def _get_blue_boundary_kills(self, period1: List[int], period2: List[int]) -> List[int]:
        """获取蓝球边界杀号"""
        boundary_kills = []
        all_recent = set(period1 + period2)
        
        # 检查小号蓝球（1-4）
        small_blues = [n for n in all_recent if n <= 4]
        if len(small_blues) == 0:  # 最近两期没有小号蓝球
            for num in [2, 3]:  # 避免杀1（太热门）
                if num not in all_recent:
                    boundary_kills.append(num)
        
        # 检查大号蓝球（9-12）
        big_blues = [n for n in all_recent if n >= 9]
        if len(big_blues) == 0:  # 最近两期没有大号蓝球
            for num in [9, 11]:  # 避免杀10和12
                if num not in all_recent:
                    boundary_kills.append(num)
        
        return boundary_kills
    
    def _get_additional_blue_kills(self, period1: List[int], period2: List[int], 
                                  historical_data: pd.DataFrame = None, exclude: set = None) -> List[int]:
        """获取额外的蓝球杀号以补充到3个"""
        if exclude is None:
            exclude = set()
        
        all_recent = set(period1 + period2)
        additional_kills = []
        
        # 选择距离最近蓝球较远的号码
        for num in self.blue_range:
            if num not in exclude and num not in all_recent:
                # 计算与最近蓝球的最小距离
                if all_recent:
                    min_distance = min(abs(num - recent) for recent in all_recent)
                    if min_distance >= 3:  # 距离足够远
                        additional_kills.append((num, min_distance))
        
        # 按距离排序
        additional_kills.sort(key=lambda x: x[1], reverse=True)
        
        # 如果还不够，选择一些相对安全的蓝球
        safe_blues = [4, 5, 6, 8, 9, 11]  # 避开超热门的1,3,7,10和边界的2,12
        for num in safe_blues:
            if num not in exclude and num not in all_recent:
                if len(additional_kills) < 5:  # 限制数量
                    additional_kills.append((num, 2))  # 给一个中等距离分数
        
        return [num for num, distance in additional_kills]
    
    def _calculate_blue_safety_score(self, num: int, period1: List[int], period2: List[int],
                                    historical_data: pd.DataFrame = None) -> float:
        """计算蓝球的安全性评分"""
        score = 0.0
        
        # 1. 基础安全性
        all_recent = set(period1 + period2)
        if num not in all_recent:
            score += 40.0
        
        # 2. 历史频率安全性
        if historical_data is not None and self.historical_stats:
            freq = self.historical_stats['blue_freq'].get(num, 0)
            if freq <= 1:
                score += 30.0
            elif freq <= 3:
                score += 20.0
            elif freq <= 5:
                score += 10.0
            else:
                score -= 5.0
        
        # 3. 距离安全性
        if all_recent:
            min_distance = min(abs(num - recent) for recent in all_recent)
            if min_distance >= 4:
                score += 25.0
            elif min_distance >= 2:
                score += 15.0
            else:
                score -= 5.0
        
        # 4. 位置安全性
        if num in [1, 12]:
            score -= 15.0  # 边界惩罚
        elif num in [2, 11]:
            score -= 5.0
        
        # 5. 超级热门蓝球惩罚
        ultra_hot_blues = [1, 3, 7, 10]
        if num in ultra_hot_blues:
            score -= 30.0
        
        return score
    
    def _blue_safety_check(self, num: int, period1: List[int], period2: List[int],
                          historical_data: pd.DataFrame = None) -> bool:
        """蓝球安全性检查"""
        # 1. 基本范围检查
        if not (1 <= num <= 12):
            return False
        
        # 2. 最近出现检查
        all_recent = set(period1 + period2)
        if num in all_recent:
            return False
        
        # 3. 安全性评分检查
        safety_score = self._calculate_blue_safety_score(num, period1, period2, historical_data)
        if safety_score < -5.0:  # 蓝球安全性要求相对宽松
            return False
        
        return True
