"""
扩展数据管理器
支持更大规模的历史数据训练和数据增强
"""

import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Optional
from collections import Counter, defaultdict
import time
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import parse_numbers


class ExtendedDataManager:
    """扩展数据管理器"""
    
    def __init__(self):
        """初始化数据管理器"""
        self.base_data = None
        self.extended_data = None
        self.synthetic_data = None
        
    def load_extended_dataset(self, base_file: str = "大乐透历史数据.csv") -> pd.DataFrame:
        """
        加载扩展数据集
        
        Args:
            base_file: 基础数据文件
            
        Returns:
            pd.DataFrame: 扩展后的数据集
        """
        # 加载基础数据
        try:
            self.base_data = pd.read_csv(base_file)
            print(f"加载基础数据: {len(self.base_data)} 期")
        except:
            print("无法加载基础数据文件")
            return pd.DataFrame()
        
        # 尝试获取更多历史数据
        additional_data = self._fetch_additional_data()
        
        # 生成合成数据
        synthetic_data = self._generate_synthetic_data()
        
        # 合并所有数据
        all_data = []
        
        if self.base_data is not None:
            all_data.append(self.base_data)
        
        if additional_data is not None and len(additional_data) > 0:
            all_data.append(additional_data)
            
        if synthetic_data is not None and len(synthetic_data) > 0:
            all_data.append(synthetic_data)
        
        if all_data:
            self.extended_data = pd.concat(all_data, ignore_index=True)
            # 按期号排序（从新到旧）
            self.extended_data = self.extended_data.sort_values('期号', ascending=False).reset_index(drop=True)
            print(f"扩展数据集总计: {len(self.extended_data)} 期")
        else:
            self.extended_data = self.base_data
        
        return self.extended_data
    
    def _fetch_additional_data(self) -> Optional[pd.DataFrame]:
        """
        获取额外的历史数据
        这里模拟从其他数据源获取更多历史数据
        """
        print("尝试获取额外历史数据...")
        
        # 模拟生成更多历史数据（实际应用中可以从API或其他数据源获取）
        additional_periods = []
        
        if self.base_data is not None and len(self.base_data) > 0:
            # 获取最早期号
            min_period = self.base_data['期号'].min()
            
            # 生成更早的期号数据（模拟）
            for i in range(100):  # 模拟增加100期历史数据
                period_num = min_period - i - 1
                if period_num <= 0:
                    break
                
                # 使用统计方法生成合理的号码
                red_balls, blue_balls = self._generate_realistic_numbers(seed=period_num)
                
                additional_periods.append({
                    '期号': period_num,
                    '红球1': red_balls[0],
                    '红球2': red_balls[1], 
                    '红球3': red_balls[2],
                    '红球4': red_balls[3],
                    '红球5': red_balls[4],
                    '蓝球1': blue_balls[0],
                    '蓝球2': blue_balls[1]
                })
        
        if additional_periods:
            additional_df = pd.DataFrame(additional_periods)
            print(f"生成额外历史数据: {len(additional_df)} 期")
            return additional_df
        
        return None
    
    def _generate_synthetic_data(self) -> Optional[pd.DataFrame]:
        """
        生成合成训练数据
        基于真实数据的统计特征生成更多训练样本
        """
        if self.base_data is None or len(self.base_data) < 10:
            return None
        
        print("生成合成训练数据...")
        
        # 分析真实数据的统计特征
        red_freq = Counter()
        blue_freq = Counter()
        red_patterns = []
        blue_patterns = []
        
        for _, row in self.base_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            red_freq.update(red_balls)
            blue_freq.update(blue_balls)
            
            # 记录模式
            red_patterns.append({
                'sum': sum(red_balls),
                'span': max(red_balls) - min(red_balls),
                'odd_count': sum(1 for x in red_balls if x % 2 == 1),
                'small_count': sum(1 for x in red_balls if x <= 18)
            })
            
            blue_patterns.append({
                'sum': sum(blue_balls),
                'gap': abs(blue_balls[1] - blue_balls[0]) if len(blue_balls) == 2 else 0,
                'small_count': sum(1 for x in blue_balls if x <= 6)
            })
        
        # 计算统计特征
        red_stats = {
            'sum_mean': np.mean([p['sum'] for p in red_patterns]),
            'sum_std': np.std([p['sum'] for p in red_patterns]),
            'span_mean': np.mean([p['span'] for p in red_patterns]),
            'span_std': np.std([p['span'] for p in red_patterns])
        }
        
        blue_stats = {
            'sum_mean': np.mean([p['sum'] for p in blue_patterns]),
            'sum_std': np.std([p['sum'] for p in blue_patterns]),
            'gap_mean': np.mean([p['gap'] for p in blue_patterns]),
            'gap_std': np.std([p['gap'] for p in blue_patterns])
        }
        
        # 生成合成数据
        synthetic_periods = []
        base_period = 30000  # 使用不同的期号范围避免冲突
        
        for i in range(200):  # 生成200期合成数据
            period_num = base_period + i
            
            # 基于统计特征生成号码
            red_balls, blue_balls = self._generate_statistical_numbers(
                red_freq, blue_freq, red_stats, blue_stats, seed=period_num
            )
            
            synthetic_periods.append({
                '期号': period_num,
                '红球1': red_balls[0],
                '红球2': red_balls[1],
                '红球3': red_balls[2], 
                '红球4': red_balls[3],
                '红球5': red_balls[4],
                '蓝球1': blue_balls[0],
                '蓝球2': blue_balls[1]
            })
        
        synthetic_df = pd.DataFrame(synthetic_periods)
        print(f"生成合成数据: {len(synthetic_df)} 期")
        return synthetic_df
    
    def _generate_realistic_numbers(self, seed: int) -> Tuple[List[int], List[int]]:
        """生成符合真实分布的号码"""
        np.random.seed(seed)
        
        # 基于真实数据的概率分布生成
        if self.base_data is not None:
            # 分析真实数据的频率分布
            red_freq = Counter()
            blue_freq = Counter()
            
            for _, row in self.base_data.iterrows():
                red_balls, blue_balls = parse_numbers(row)
                red_freq.update(red_balls)
                blue_freq.update(blue_balls)
            
            # 计算概率分布
            red_probs = np.array([red_freq.get(i, 1) for i in range(1, 36)])
            red_probs = red_probs / red_probs.sum()
            
            blue_probs = np.array([blue_freq.get(i, 1) for i in range(1, 13)])
            blue_probs = blue_probs / blue_probs.sum()
            
            # 基于概率分布采样
            red_balls = list(np.random.choice(range(1, 36), 5, replace=False, p=red_probs))
            blue_balls = list(np.random.choice(range(1, 13), 2, replace=False, p=blue_probs))
        else:
            # 备选方案：均匀分布
            red_balls = list(np.random.choice(range(1, 36), 5, replace=False))
            blue_balls = list(np.random.choice(range(1, 13), 2, replace=False))
        
        return sorted(red_balls), sorted(blue_balls)
    
    def _generate_statistical_numbers(self, red_freq: Counter, blue_freq: Counter,
                                    red_stats: Dict, blue_stats: Dict, seed: int) -> Tuple[List[int], List[int]]:
        """基于统计特征生成号码"""
        np.random.seed(seed)
        
        # 目标统计特征
        target_red_sum = np.random.normal(red_stats['sum_mean'], red_stats['sum_std'])
        target_red_span = np.random.normal(red_stats['span_mean'], red_stats['span_std'])
        
        target_blue_sum = np.random.normal(blue_stats['sum_mean'], blue_stats['sum_std'])
        target_blue_gap = np.random.normal(blue_stats['gap_mean'], blue_stats['gap_std'])
        
        # 生成满足统计特征的号码
        red_balls = self._generate_constrained_red(red_freq, target_red_sum, target_red_span, seed)
        blue_balls = self._generate_constrained_blue(blue_freq, target_blue_sum, target_blue_gap, seed)
        
        return red_balls, blue_balls
    
    def _generate_constrained_red(self, freq: Counter, target_sum: float, 
                                target_span: float, seed: int) -> List[int]:
        """生成满足约束的红球"""
        np.random.seed(seed)
        
        # 多次尝试生成满足条件的组合
        for attempt in range(100):
            # 基于频率分布采样
            red_probs = np.array([freq.get(i, 1) for i in range(1, 36)])
            red_probs = red_probs / red_probs.sum()
            
            candidates = list(np.random.choice(range(1, 36), 5, replace=False, p=red_probs))
            candidates.sort()
            
            # 检查是否满足约束
            actual_sum = sum(candidates)
            actual_span = max(candidates) - min(candidates)
            
            if (abs(actual_sum - target_sum) < 20 and 
                abs(actual_span - target_span) < 10):
                return candidates
        
        # 如果无法满足约束，返回基于频率的采样
        red_probs = np.array([freq.get(i, 1) for i in range(1, 36)])
        red_probs = red_probs / red_probs.sum()
        return sorted(list(np.random.choice(range(1, 36), 5, replace=False, p=red_probs)))
    
    def _generate_constrained_blue(self, freq: Counter, target_sum: float,
                                 target_gap: float, seed: int) -> List[int]:
        """生成满足约束的蓝球"""
        np.random.seed(seed)
        
        # 多次尝试生成满足条件的组合
        for attempt in range(50):
            blue_probs = np.array([freq.get(i, 1) for i in range(1, 13)])
            blue_probs = blue_probs / blue_probs.sum()
            
            candidates = list(np.random.choice(range(1, 13), 2, replace=False, p=blue_probs))
            candidates.sort()
            
            actual_sum = sum(candidates)
            actual_gap = abs(candidates[1] - candidates[0])
            
            if (abs(actual_sum - target_sum) < 5 and 
                abs(actual_gap - target_gap) < 3):
                return candidates
        
        # 备选方案
        blue_probs = np.array([freq.get(i, 1) for i in range(1, 13)])
        blue_probs = blue_probs / blue_probs.sum()
        return sorted(list(np.random.choice(range(1, 13), 2, replace=False, p=blue_probs)))
    
    def get_training_data(self, train_ratio: float = 0.8) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        获取训练和验证数据
        
        Args:
            train_ratio: 训练数据比例
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (训练数据, 验证数据)
        """
        if self.extended_data is None:
            return pd.DataFrame(), pd.DataFrame()
        
        # 按时间顺序分割（保持时间序列特性）
        split_idx = int(len(self.extended_data) * train_ratio)
        
        # 训练数据：较早的数据
        train_data = self.extended_data.iloc[split_idx:].copy()
        
        # 验证数据：较新的数据
        val_data = self.extended_data.iloc[:split_idx].copy()
        
        print(f"训练数据: {len(train_data)} 期")
        print(f"验证数据: {len(val_data)} 期")
        
        return train_data, val_data
    
    def get_data_statistics(self) -> Dict:
        """获取数据集统计信息"""
        if self.extended_data is None:
            return {}
        
        stats = {
            'total_periods': len(self.extended_data),
            'period_range': (self.extended_data['期号'].min(), self.extended_data['期号'].max()),
            'data_sources': []
        }
        
        if self.base_data is not None:
            stats['data_sources'].append(f"基础数据: {len(self.base_data)} 期")
        
        if hasattr(self, 'additional_data') and self.additional_data is not None:
            stats['data_sources'].append(f"额外数据: {len(self.additional_data)} 期")
            
        if self.synthetic_data is not None:
            stats['data_sources'].append(f"合成数据: {len(self.synthetic_data)} 期")
        
        return stats


def test_extended_data_manager():
    """测试扩展数据管理器"""
    manager = ExtendedDataManager()
    
    print("测试扩展数据管理器...")
    
    # 加载扩展数据集
    extended_data = manager.load_extended_dataset()
    
    # 获取统计信息
    stats = manager.get_data_statistics()
    print(f"\n数据集统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 获取训练和验证数据
    train_data, val_data = manager.get_training_data()
    
    print(f"\n数据分割结果:")
    print(f"  训练数据: {len(train_data)} 期")
    print(f"  验证数据: {len(val_data)} 期")


if __name__ == "__main__":
    test_extended_data_manager()
