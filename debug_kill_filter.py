#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试杀号过滤逻辑
"""

from src.systems.main import LotteryPredictor
from src.generators.precision_generator import PrecisionGenerator

def debug_kill_filter():
    """调试杀号过滤"""
    print("🔍 调试杀号过滤逻辑")
    print("=" * 80)
    
    # 初始化预测器
    predictor = LotteryPredictor()
    
    # 执行预测获取杀号
    prediction = predictor.predict_next_period(0)
    kill_numbers_raw = prediction['kill_numbers']
    
    # 转换杀号格式
    kill_numbers = {
        'red': [kill_numbers_raw.get('red_universal', [])],
        'blue': [kill_numbers_raw.get('blue_universal', [])]
    }
    
    print(f"📊 杀号数据:")
    print(f"  原始格式: {kill_numbers_raw}")
    print(f"  转换格式: {kill_numbers}")
    
    # 测试精准生成器的杀号过滤
    print(f"\n🧪 测试精准生成器杀号过滤:")
    
    precision_gen = PrecisionGenerator()
    
    # 测试多次生成
    for i in range(5):
        red, blue = precision_gen.generate_precision_numbers(
            "3:2", "2:3", "1:1", kill_numbers, seed=i
        )
        
        print(f"\n第{i+1}次生成:")
        print(f"  红球: {red}")
        print(f"  蓝球: {blue}")
        
        # 检查是否包含被杀号码
        red_kills = kill_numbers['red'][0] if kill_numbers['red'] else []
        blue_kills = kill_numbers['blue'][0] if kill_numbers['blue'] else []
        
        red_violations = [num for num in red if num in red_kills]
        blue_violations = [num for num in blue if num in blue_kills]
        
        print(f"  红球杀号: {red_kills}")
        print(f"  蓝球杀号: {blue_kills}")
        print(f"  红球违规: {red_violations} {'❌' if red_violations else '✅'}")
        print(f"  蓝球违规: {blue_violations} {'❌' if blue_violations else '✅'}")

def test_kill_filter_directly():
    """直接测试杀号过滤函数"""
    print(f"\n🔧 直接测试杀号过滤函数:")
    print("-" * 60)
    
    precision_gen = PrecisionGenerator()
    
    # 测试数据
    candidates = list(range(1, 36))  # 红球候选
    kill_numbers = {
        'red': [[13, 14, 35, 33, 18, 15, 24, 23]],  # 实际杀号
        'blue': [[2]]
    }
    
    print(f"原始候选: {candidates[:10]}...{candidates[-5:]}")
    print(f"杀号数据: {kill_numbers}")
    
    # 应用杀号过滤
    filtered = precision_gen._apply_kill_filter(candidates, kill_numbers, 'red')
    
    print(f"过滤后候选: {filtered[:10]}...{filtered[-5:]}")
    print(f"过滤前数量: {len(candidates)}")
    print(f"过滤后数量: {len(filtered)}")
    
    # 检查被杀号码是否还在
    killed_numbers = kill_numbers['red'][0]
    remaining_killed = [num for num in killed_numbers if num in filtered]
    
    print(f"被杀号码: {killed_numbers}")
    print(f"仍在候选中的被杀号码: {remaining_killed}")
    
    if remaining_killed:
        print(f"❌ 杀号过滤失败！")
    else:
        print(f"✅ 杀号过滤成功")

def check_generator_kill_logic():
    """检查生成器杀号逻辑"""
    print(f"\n📋 检查生成器杀号逻辑:")
    print("-" * 60)
    
    # 查看精准生成器的_apply_kill_filter方法
    precision_gen = PrecisionGenerator()
    
    # 检查方法是否存在
    if hasattr(precision_gen, '_apply_kill_filter'):
        print(f"✅ _apply_kill_filter方法存在")
        
        # 测试方法
        candidates = [1, 2, 3, 15, 16, 17]  # 包含被杀号码15
        kill_numbers = {
            'red': [[15, 24]],
            'blue': [[2]]
        }
        
        filtered = precision_gen._apply_kill_filter(candidates, kill_numbers, 'red')
        print(f"测试过滤: {candidates} -> {filtered}")
        
        if 15 in filtered:
            print(f"❌ 杀号过滤逻辑有问题")
        else:
            print(f"✅ 杀号过滤逻辑正常")
    else:
        print(f"❌ _apply_kill_filter方法不存在")
    
    # 检查其他生成器
    from src.generators.dynamic_generator import DynamicGenerator
    dynamic_gen = DynamicGenerator()
    
    if hasattr(dynamic_gen, '_apply_kill_filter'):
        print(f"✅ DynamicGenerator有_apply_kill_filter方法")
    else:
        print(f"❌ DynamicGenerator没有_apply_kill_filter方法")

def trace_generation_process():
    """追踪生成过程"""
    print(f"\n🔄 追踪号码生成过程:")
    print("-" * 60)
    
    # 初始化预测器
    predictor = LotteryPredictor()
    
    # 获取杀号
    prediction = predictor.predict_next_period(0)
    kill_numbers_raw = prediction['kill_numbers']
    
    # 转换杀号格式
    kill_numbers = {
        'red': [kill_numbers_raw.get('red_universal', [])],
        'blue': [kill_numbers_raw.get('blue_universal', [])]
    }
    
    print(f"杀号数据: {kill_numbers}")
    
    # 手动调用生成器
    precision_gen = PrecisionGenerator()
    
    print(f"\n手动调用精准生成器:")
    try:
        red, blue = precision_gen.generate_precision_numbers(
            "3:2", "2:3", "1:1", kill_numbers, seed=42
        )
        
        print(f"生成结果: 红球{red}, 蓝球{blue}")
        
        # 检查违规
        red_kills = kill_numbers['red'][0] if kill_numbers['red'] else []
        blue_kills = kill_numbers['blue'][0] if kill_numbers['blue'] else []
        
        red_violations = [num for num in red if num in red_kills]
        blue_violations = [num for num in blue if num in blue_kills]
        
        print(f"红球违规: {red_violations}")
        print(f"蓝球违规: {blue_violations}")
        
        if red_violations or blue_violations:
            print(f"❌ 发现违规，需要检查生成器内部逻辑")
        else:
            print(f"✅ 无违规，杀号过滤正常")
            
    except Exception as e:
        print(f"❌ 生成器调用失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        debug_kill_filter()
        test_kill_filter_directly()
        check_generator_kill_logic()
        trace_generation_process()
        
        print(f"\n🎯 调试总结:")
        print("=" * 80)
        print("需要检查的问题:")
        print("1. 杀号格式转换是否正确")
        print("2. 生成器是否正确应用杀号过滤")
        print("3. _apply_kill_filter方法是否正常工作")
        print("4. 是否有生成器绕过了杀号过滤")
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
