#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试_select_kills_by_state方法为什么输出[1,2,3,4,5]
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from advanced_probabilistic_system import MarkovChainKillAlgorithm

def debug_select_kills_by_state():
    """调试_select_kills_by_state方法"""
    print("🔍 调试_select_kills_by_state方法")
    print("=" * 60)
    
    # 创建一个简单的数据集用于测试
    import pandas as pd
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '期号': ['25067', '25066', '25065'],
        '红球1': ['06', '15', '07'],
        '红球2': ['10', '18', '25'],
        '红球3': ['12', '27', '32'],
        '红球4': ['21', '28', '33'],
        '红球5': ['22', '34', '35'],
        '蓝球1': ['01', '03', '04'],
        '蓝球2': ['06', '06', '09']
    })
    
    # 创建马尔可夫链2算法
    markov2 = MarkovChainKillAlgorithm(test_data, order=2)
    
    # 测试默认状态
    default_state = ('high', 'low', 'low', 'high', 'low', 'unbalanced', 'high', 'low')
    print(f"📊 测试状态: {default_state}")
    
    # 解包状态特征
    (odd_ratio, large_ratio, sum_range, consecutive_level,
     span_level, zone_balance, tail_level, ac_level) = default_state
    
    print(f"\n🔧 状态特征分析:")
    print(f"  奇偶比: {odd_ratio}")
    print(f"  大小比: {large_ratio}")
    print(f"  和值范围: {sum_range}")
    print(f"  连号级别: {consecutive_level}")
    print(f"  跨度级别: {span_level}")
    print(f"  区间平衡: {zone_balance}")
    print(f"  尾数级别: {tail_level}")
    print(f"  AC值级别: {ac_level}")
    
    print(f"\n🎯 杀号策略分析:")
    
    kill_candidates = []
    
    # 策略1：基于奇偶分布
    print(f"1️⃣ 奇偶策略 (odd_ratio={odd_ratio}):")
    if odd_ratio == "high":
        even_candidates = [2, 4, 6, 8, 10, 12, 16, 18, 20, 22, 26, 28, 30, 32, 34]
        print(f"   奇数多，杀偶数: {even_candidates[:3]}")
        kill_candidates.extend(even_candidates[:3])
    elif odd_ratio == "low":
        odd_candidates = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35]
        print(f"   偶数多，杀奇数: {odd_candidates[:3]}")
        kill_candidates.extend(odd_candidates[:3])
    else:
        mixed_candidates = [14, 24, 25, 26]
        print(f"   平衡，杀混合: {mixed_candidates[:2]}")
        kill_candidates.extend(mixed_candidates[:2])
    
    # 策略2：基于大小分布
    print(f"2️⃣ 大小策略 (large_ratio={large_ratio}):")
    if large_ratio == "high":
        small_candidates = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]
        print(f"   大数多，杀小数: {small_candidates[:2]}")
        kill_candidates.extend(small_candidates[:2])
    elif large_ratio == "low":
        large_candidates = [18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35]
        print(f"   小数多，杀大数: {large_candidates[:2]}")
        kill_candidates.extend(large_candidates[:2])
    else:
        print(f"   大小平衡，不杀")
    
    # 策略3：连号特征
    print(f"3️⃣ 连号策略 (consecutive_level={consecutive_level}):")
    if consecutive_level == "high":
        isolated_candidates = [1, 5, 9, 13, 17, 21, 25, 29, 33]
        print(f"   连号多，杀孤立: {isolated_candidates[:2]}")
        kill_candidates.extend(isolated_candidates[:2])
    else:
        print(f"   连号不多，不杀")
    
    # 策略4：跨度特征
    print(f"4️⃣ 跨度策略 (span_level={span_level}):")
    if span_level == "low":
        edge_candidates = [1, 2, 34, 35]
        print(f"   跨度小，杀边缘: {edge_candidates[:2]}")
        kill_candidates.extend(edge_candidates[:2])
    else:
        print(f"   跨度不小，不杀")
    
    # 策略5：区间平衡
    print(f"5️⃣ 区间策略 (zone_balance={zone_balance}):")
    if zone_balance == "unbalanced":
        middle_candidates = [15, 16, 17, 18, 19, 20, 21]
        print(f"   区间不平衡，杀中间: {middle_candidates[:2]}")
        kill_candidates.extend(middle_candidates[:2])
    else:
        print(f"   区间平衡，不杀")
    
    # 策略6：冷号
    print(f"6️⃣ 冷号策略:")
    cold_numbers = [num for num in range(1, 36) if num > 20]  # 简化实现
    print(f"   杀冷号: {cold_numbers[:2]}")
    kill_candidates.extend(cold_numbers[:2])
    
    print(f"\n📋 杀号候选汇总:")
    print(f"  原始候选: {kill_candidates}")
    
    # 去重
    unique_kills = list(set(kill_candidates))
    print(f"  去重后: {unique_kills}")
    
    # 安全性检查
    dangerous_numbers = {3, 6, 7, 10, 11, 12, 15, 20, 21, 22, 29, 31, 34}
    boundary_numbers = {1, 2, 34, 35}
    
    safe_kills = []
    for num in unique_kills:
        if num in dangerous_numbers:
            print(f"  ❌ 过滤危险号码: {num}")
            continue
        if num in boundary_numbers:
            print(f"  ❌ 过滤边界号码: {num}")
            continue
        safe_kills.append(num)
    
    print(f"  安全检查后: {safe_kills}")
    
    # 如果数量不够，补充
    target_count = 5
    if len(safe_kills) < target_count:
        remaining_candidates = [num for num in range(1, 36) if num not in safe_kills]
        needed = target_count - len(safe_kills)
        
        # 优先选择冷号
        cold_remaining = [num for num in remaining_candidates if num > 20]
        additional = cold_remaining[:needed]
        safe_kills.extend(additional)
        print(f"  补充冷号: {additional}")
        
        # 如果还不够，随机补充
        if len(safe_kills) < target_count:
            still_remaining = [num for num in remaining_candidates if num not in additional]
            still_needed = target_count - len(safe_kills)
            final_additional = still_remaining[:still_needed]
            safe_kills.extend(final_additional)
            print(f"  最终补充: {final_additional}")
    
    final_result = safe_kills[:target_count]
    print(f"\n🎯 最终杀号: {final_result}")
    
    # 分析为什么是[1,2,3,4,5]
    print(f"\n🔍 分析结果:")
    if final_result == [1, 2, 3, 4, 5]:
        print(f"✅ 确认输出[1,2,3,4,5]")
        print(f"📝 原因分析:")
        print(f"  1. 大部分杀号候选被安全检查过滤掉")
        print(f"  2. 剩余候选数量不足5个")
        print(f"  3. 系统按顺序补充了1,2,3,4,5")
        print(f"  4. 这表明当前状态下，算法无法找到足够的安全杀号")
    else:
        print(f"❓ 输出不是[1,2,3,4,5]: {final_result}")
    
    print(f"\n" + "=" * 60)
    print("🎉 _select_kills_by_state调试完成！")

if __name__ == "__main__":
    debug_select_kills_by_state()
