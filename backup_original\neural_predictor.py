"""
神经网络预测器
使用深度学习模型处理复杂非线性关系
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from collections import Counter
import pickle
import os
from utils import parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, calculate_size_ratio_blue, ratio_to_state


class SimpleNeuralNetwork:
    """简单的神经网络实现（不依赖外部库）"""
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int):
        """
        初始化神经网络
        
        Args:
            input_size: 输入层大小
            hidden_sizes: 隐藏层大小列表
            output_size: 输出层大小
        """
        self.layers = []
        
        # 构建网络层
        prev_size = input_size
        for hidden_size in hidden_sizes:
            self.layers.append({
                'weights': np.random.randn(prev_size, hidden_size) * 0.1,
                'biases': np.zeros(hidden_size),
                'type': 'hidden'
            })
            prev_size = hidden_size
        
        # 输出层
        self.layers.append({
            'weights': np.random.randn(prev_size, output_size) * 0.1,
            'biases': np.zeros(output_size),
            'type': 'output'
        })
        
        self.learning_rate = 0.01
        self.is_trained = False
    
    def _sigmoid(self, x):
        """Sigmoid激活函数"""
        return 1 / (1 + np.exp(-np.clip(x, -500, 500)))
    
    def _sigmoid_derivative(self, x):
        """Sigmoid导数"""
        return x * (1 - x)
    
    def _softmax(self, x):
        """Softmax激活函数"""
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    def forward(self, X):
        """前向传播"""
        activations = [X]
        
        for i, layer in enumerate(self.layers):
            z = np.dot(activations[-1], layer['weights']) + layer['biases']
            
            if layer['type'] == 'output':
                # 输出层使用softmax
                a = self._softmax(z)
            else:
                # 隐藏层使用sigmoid
                a = self._sigmoid(z)
            
            activations.append(a)
        
        return activations
    
    def train(self, X, y, epochs=100, batch_size=32):
        """训练神经网络"""
        n_samples = X.shape[0]
        
        for epoch in range(epochs):
            # 随机打乱数据
            indices = np.random.permutation(n_samples)
            X_shuffled = X[indices]
            y_shuffled = y[indices]
            
            total_loss = 0
            
            # 批量训练
            for i in range(0, n_samples, batch_size):
                batch_X = X_shuffled[i:i+batch_size]
                batch_y = y_shuffled[i:i+batch_size]
                
                # 前向传播
                activations = self.forward(batch_X)
                
                # 计算损失
                loss = self._cross_entropy_loss(activations[-1], batch_y)
                total_loss += loss
                
                # 反向传播
                self._backward(activations, batch_y)
            
            if epoch % 20 == 0:
                avg_loss = total_loss / (n_samples // batch_size + 1)
                print(f"Epoch {epoch}, Loss: {avg_loss:.4f}")
        
        self.is_trained = True
    
    def _cross_entropy_loss(self, predictions, targets):
        """交叉熵损失"""
        epsilon = 1e-15
        predictions = np.clip(predictions, epsilon, 1 - epsilon)
        return -np.mean(np.sum(targets * np.log(predictions), axis=1))
    
    def _backward(self, activations, targets):
        """反向传播"""
        batch_size = activations[0].shape[0]
        
        # 计算输出层误差
        output_error = activations[-1] - targets
        
        # 从输出层向前传播误差
        errors = [output_error]
        
        for i in range(len(self.layers) - 2, -1, -1):
            error = np.dot(errors[0], self.layers[i + 1]['weights'].T) * \
                   self._sigmoid_derivative(activations[i + 1])
            errors.insert(0, error)
        
        # 更新权重和偏置
        for i, layer in enumerate(self.layers):
            layer['weights'] -= self.learning_rate * np.dot(activations[i].T, errors[i]) / batch_size
            layer['biases'] -= self.learning_rate * np.mean(errors[i], axis=0)
    
    def predict(self, X):
        """预测"""
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        activations = self.forward(X)
        return activations[-1]


class NeuralPredictor:
    """神经网络预测器"""
    
    def __init__(self):
        """初始化预测器"""
        self.feature_extractors = {}
        self.models = {}
        self.label_encoders = {}
        self.is_trained = False
        
        # 网络架构配置
        self.network_configs = {
            'red_odd_even': {'hidden_sizes': [64, 32], 'output_size': 6},  # 6种奇偶比状态
            'red_size': {'hidden_sizes': [64, 32], 'output_size': 6},      # 6种大小比状态
            'blue_size': {'hidden_sizes': [32, 16], 'output_size': 3},     # 3种蓝球大小比状态
            'number_prediction': {'hidden_sizes': [128, 64, 32], 'output_size': 47}  # 35红球+12蓝球
        }
    
    def extract_features(self, data: pd.DataFrame, lookback: int = 10) -> np.ndarray:
        """
        提取特征
        
        Args:
            data: 历史数据
            lookback: 回看期数
            
        Returns:
            np.ndarray: 特征矩阵
        """
        features = []
        
        for i in range(lookback, len(data)):
            period_features = []
            
            # 提取最近lookback期的特征
            for j in range(i - lookback, i):
                row = data.iloc[j]
                red_balls, blue_balls = parse_numbers(row)
                
                # 基础特征
                period_features.extend([
                    # 红球特征
                    *red_balls,  # 5个红球号码
                    sum(red_balls),  # 红球和值
                    max(red_balls) - min(red_balls),  # 红球跨度
                    sum(1 for x in red_balls if x % 2 == 1),  # 奇数个数
                    sum(1 for x in red_balls if x <= 18),  # 小号个数
                    
                    # 蓝球特征
                    *blue_balls,  # 2个蓝球号码
                    sum(blue_balls),  # 蓝球和值
                    abs(blue_balls[1] - blue_balls[0]) if len(blue_balls) == 2 else 0,  # 蓝球间距
                    sum(1 for x in blue_balls if x <= 6),  # 蓝球小号个数
                ])
                
                # 高级特征
                # AC值
                ac_value = self._calculate_ac_value(red_balls)
                period_features.append(ac_value)
                
                # 连号个数
                consecutive = self._count_consecutive(red_balls)
                period_features.append(consecutive)
                
                # 重复号码个数（与前一期比较）
                if j > 0:
                    prev_row = data.iloc[j - 1]
                    prev_red, prev_blue = parse_numbers(prev_row)
                    red_repeat = len(set(red_balls) & set(prev_red))
                    blue_repeat = len(set(blue_balls) & set(prev_blue))
                else:
                    red_repeat = 0
                    blue_repeat = 0
                
                period_features.extend([red_repeat, blue_repeat])
            
            features.append(period_features)
        
        return np.array(features)
    
    def extract_labels(self, data: pd.DataFrame, lookback: int = 10) -> Dict[str, np.ndarray]:
        """
        提取标签
        
        Args:
            data: 历史数据
            lookback: 回看期数
            
        Returns:
            Dict[str, np.ndarray]: 各种标签
        """
        labels = {
            'red_odd_even': [],
            'red_size': [],
            'blue_size': [],
            'red_numbers': [],
            'blue_numbers': []
        }
        
        for i in range(lookback, len(data)):
            row = data.iloc[i]
            red_balls, blue_balls = parse_numbers(row)
            
            # 状态标签
            red_odd, red_even = calculate_odd_even_ratio(red_balls)
            red_odd_even_state = ratio_to_state((red_odd, red_even))
            labels['red_odd_even'].append(red_odd_even_state)
            
            red_small, red_big = calculate_size_ratio_red(red_balls)
            red_size_state = ratio_to_state((red_small, red_big))
            labels['red_size'].append(red_size_state)
            
            blue_small, blue_big = calculate_size_ratio_blue(blue_balls)
            blue_size_state = ratio_to_state((blue_small, blue_big))
            labels['blue_size'].append(blue_size_state)
            
            # 号码标签（one-hot编码）
            red_onehot = np.zeros(35)
            for num in red_balls:
                red_onehot[num - 1] = 1
            labels['red_numbers'].append(red_onehot)
            
            blue_onehot = np.zeros(12)
            for num in blue_balls:
                blue_onehot[num - 1] = 1
            labels['blue_numbers'].append(blue_onehot)
        
        # 转换为numpy数组
        for key in labels:
            if key in ['red_numbers', 'blue_numbers']:
                labels[key] = np.array(labels[key])
            else:
                labels[key] = np.array(labels[key])
        
        return labels
    
    def train_models(self, data: pd.DataFrame, lookback: int = 10, epochs: int = 100):
        """
        训练神经网络模型
        
        Args:
            data: 训练数据
            lookback: 回看期数
            epochs: 训练轮数
        """
        print("开始训练神经网络模型...")
        
        # 提取特征和标签
        X = self.extract_features(data, lookback)
        labels = self.extract_labels(data, lookback)
        
        if len(X) == 0:
            print("没有足够的数据进行训练")
            return
        
        print(f"特征维度: {X.shape}")
        
        # 特征标准化
        self.feature_mean = np.mean(X, axis=0)
        self.feature_std = np.std(X, axis=0) + 1e-8
        X_normalized = (X - self.feature_mean) / self.feature_std
        
        input_size = X.shape[1]
        
        # 训练状态预测模型
        for task in ['red_odd_even', 'red_size', 'blue_size']:
            print(f"\n训练 {task} 模型...")
            
            # 编码标签
            unique_labels = list(set(labels[task]))
            self.label_encoders[task] = {label: i for i, label in enumerate(unique_labels)}
            reverse_encoder = {i: label for label, i in self.label_encoders[task].items()}
            
            y_encoded = np.array([self.label_encoders[task][label] for label in labels[task]])
            y_onehot = np.eye(len(unique_labels))[y_encoded]
            
            # 创建和训练模型
            config = self.network_configs[task]
            model = SimpleNeuralNetwork(input_size, config['hidden_sizes'], len(unique_labels))
            model.train(X_normalized, y_onehot, epochs=epochs)
            
            self.models[task] = model
        
        self.is_trained = True
        print("\n神经网络模型训练完成!")
    
    def predict(self, recent_data: pd.DataFrame, lookback: int = 10) -> Dict[str, Tuple[str, float]]:
        """
        使用神经网络进行预测
        
        Args:
            recent_data: 最近的数据
            lookback: 回看期数
            
        Returns:
            Dict[str, Tuple[str, float]]: 预测结果
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")
        
        if len(recent_data) < lookback:
            raise ValueError(f"数据不足，需要至少 {lookback} 期数据")
        
        # 提取最近的特征
        features = self.extract_features(recent_data.tail(lookback + 1), lookback)
        if len(features) == 0:
            raise ValueError("无法提取特征")
        
        # 使用最后一个特征向量进行预测
        X = features[-1:] 
        X_normalized = (X - self.feature_mean) / self.feature_std
        
        predictions = {}
        
        for task in ['red_odd_even', 'red_size', 'blue_size']:
            if task in self.models:
                # 预测
                pred_probs = self.models[task].predict(X_normalized)[0]
                
                # 找到最大概率的类别
                pred_class_idx = np.argmax(pred_probs)
                pred_prob = pred_probs[pred_class_idx]
                
                # 解码标签
                reverse_encoder = {i: label for label, i in self.label_encoders[task].items()}
                pred_label = reverse_encoder[pred_class_idx]
                
                predictions[task] = (pred_label, float(pred_prob))
        
        return predictions
    
    def save_models(self, filepath: str):
        """保存模型"""
        model_data = {
            'models': self.models,
            'label_encoders': self.label_encoders,
            'feature_mean': getattr(self, 'feature_mean', None),
            'feature_std': getattr(self, 'feature_std', None),
            'is_trained': self.is_trained
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"模型已保存到 {filepath}")
    
    def load_models(self, filepath: str):
        """加载模型"""
        if not os.path.exists(filepath):
            print(f"模型文件 {filepath} 不存在")
            return False
        
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.models = model_data['models']
            self.label_encoders = model_data['label_encoders']
            self.feature_mean = model_data['feature_mean']
            self.feature_std = model_data['feature_std']
            self.is_trained = model_data['is_trained']
            
            print(f"模型已从 {filepath} 加载")
            return True
        except Exception as e:
            print(f"加载模型失败: {e}")
            return False
    
    def _calculate_ac_value(self, numbers: List[int]) -> int:
        """计算AC值"""
        if len(numbers) < 2:
            return 0
        
        differences = set()
        sorted_nums = sorted(numbers)
        
        for i in range(len(sorted_nums)):
            for j in range(i + 1, len(sorted_nums)):
                differences.add(abs(sorted_nums[j] - sorted_nums[i]))
        
        return len(differences) - (len(numbers) - 1)
    
    def _count_consecutive(self, numbers: List[int]) -> int:
        """计算连号个数"""
        sorted_nums = sorted(numbers)
        consecutive = 0
        
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i + 1] - sorted_nums[i] == 1:
                consecutive += 1
        
        return consecutive


def test_neural_predictor():
    """测试神经网络预测器"""
    from utils import load_data
    
    predictor = NeuralPredictor()
    
    print("测试神经网络预测器...")
    
    # 加载数据
    data = load_data()
    if len(data) < 50:
        print("数据不足，无法测试")
        return
    
    # 训练模型
    train_data = data.iloc[10:]  # 使用后面的数据训练
    predictor.train_models(train_data, lookback=5, epochs=50)
    
    # 测试预测
    test_data = data.head(20)  # 使用前面的数据测试
    try:
        predictions = predictor.predict(test_data, lookback=5)
        
        print("\n预测结果:")
        for task, (pred_label, prob) in predictions.items():
            print(f"  {task}: {pred_label} (概率: {prob:.3f})")
    except Exception as e:
        print(f"预测失败: {e}")


if __name__ == "__main__":
    test_neural_predictor()
