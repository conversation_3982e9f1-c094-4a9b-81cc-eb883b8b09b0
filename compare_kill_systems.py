#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
对比主程序杀号和kill_count_analysis_system杀号的差异
"""

import pandas as pd
from src.systems.main import LotteryPredictor
from advanced_probabilistic_system import EnsembleKillSystem

def compare_kill_systems():
    """对比两个杀号系统"""
    print("🔍 对比主程序杀号和kill_count_analysis_system杀号")
    print("=" * 80)
    
    # 初始化主程序预测器
    main_predictor = LotteryPredictor()
    
    # 初始化kill_count_analysis_system
    ensemble_system = EnsembleKillSystem(main_predictor.data)
    ensemble_system.weights = {'bayesian': 0.5, 'markov1': 0.3, 'markov2': 0.2}
    
    print(f"📊 数据信息:")
    print(f"  总期数: {len(main_predictor.data)}")
    print(f"  最新期号: {main_predictor.data.iloc[0]['期号']}")
    print()
    
    # 对比前5期的杀号结果
    print("🔄 对比前5期的杀号结果:")
    print("=" * 80)
    
    for i in range(5):
        print(f"\n第{i+1}期对比 (期号: {main_predictor.data.iloc[i]['期号']}):")
        
        # 1. 主程序杀号
        print(f"  🎯 主程序杀号:")
        try:
            # 获取训练数据（模拟主程序逻辑）
            max_train_periods = 200
            train_start = i + 1
            train_end = min(len(main_predictor.data), train_start + max_train_periods)
            train_data = main_predictor.data.iloc[train_start:train_end].copy()
            
            # 调用主程序的杀号方法
            main_kill_result = main_predictor._universal_kill_prediction(train_data)
            
            print(f"    红球杀号: {main_kill_result.get('red_universal', [])} (共{len(main_kill_result.get('red_universal', []))}个)")
            print(f"    蓝球杀号: {main_kill_result.get('blue_universal', [])} (共{len(main_kill_result.get('blue_universal', []))}个)")
            
        except Exception as e:
            print(f"    ❌ 主程序杀号失败: {e}")
            main_kill_result = {'red_universal': [], 'blue_universal': []}
        
        # 2. kill_count_analysis_system杀号
        print(f"  🔬 kill_count_analysis_system杀号:")
        try:
            # 构造period_data（模拟kill_count_analysis_system的数据格式）
            if i + 5 < len(main_predictor.data):
                period_data = {
                    'current': main_predictor.data.iloc[i],
                    'last': main_predictor.data.iloc[i + 1],
                    'prev2': main_predictor.data.iloc[i + 2],
                    'prev3': main_predictor.data.iloc[i + 3],
                    'prev4': main_predictor.data.iloc[i + 4],
                    'prev5': main_predictor.data.iloc[i + 5]
                }
                
                # 调用ensemble系统的杀号方法（只支持红球）
                ensemble_red_kills = ensemble_system.predict_ensemble_kills(period_data, target_count=8)
                # ensemble系统不支持蓝球杀号，使用空列表
                ensemble_blue_kills = []
                
                print(f"    红球杀号: {ensemble_red_kills} (共{len(ensemble_red_kills)}个)")
                print(f"    蓝球杀号: {ensemble_blue_kills} (共{len(ensemble_blue_kills)}个)")
                
            else:
                print(f"    ⚠️  数据不足，跳过")
                ensemble_red_kills = []
                ensemble_blue_kills = []
                
        except Exception as e:
            print(f"    ❌ ensemble系统杀号失败: {e}")
            ensemble_red_kills = []
            ensemble_blue_kills = []
        
        # 3. 对比分析
        print(f"  📊 对比分析:")
        
        # 红球对比
        main_red = set(main_kill_result.get('red_universal', []))
        ensemble_red = set(ensemble_red_kills)
        
        red_common = main_red & ensemble_red
        red_main_only = main_red - ensemble_red
        red_ensemble_only = ensemble_red - main_red
        
        print(f"    红球共同杀号: {sorted(list(red_common))} (共{len(red_common)}个)")
        print(f"    主程序独有: {sorted(list(red_main_only))} (共{len(red_main_only)}个)")
        print(f"    ensemble独有: {sorted(list(red_ensemble_only))} (共{len(red_ensemble_only)}个)")
        
        # 蓝球对比
        main_blue = set(main_kill_result.get('blue_universal', []))
        ensemble_blue = set(ensemble_blue_kills)
        
        blue_common = main_blue & ensemble_blue
        blue_main_only = main_blue - ensemble_blue
        blue_ensemble_only = ensemble_blue - main_blue
        
        print(f"    蓝球共同杀号: {sorted(list(blue_common))} (共{len(blue_common)}个)")
        print(f"    主程序独有: {sorted(list(blue_main_only))} (共{len(blue_main_only)}个)")
        print(f"    ensemble独有: {sorted(list(blue_ensemble_only))} (共{len(blue_ensemble_only)}个)")
        
        # 相似度计算
        if main_red or ensemble_red:
            red_similarity = len(red_common) / len(main_red | ensemble_red) if (main_red | ensemble_red) else 0
            print(f"    红球相似度: {red_similarity:.1%}")
        
        if main_blue or ensemble_blue:
            blue_similarity = len(blue_common) / len(main_blue | ensemble_blue) if (main_blue | ensemble_blue) else 0
            print(f"    蓝球相似度: {blue_similarity:.1%}")

def analyze_algorithm_differences():
    """分析算法差异"""
    print(f"\n🔬 分析算法差异:")
    print("=" * 80)
    
    print("📋 主程序杀号算法特点:")
    print("  1. 使用BayesianMarkovKiller类")
    print("  2. 基于贝叶斯+马尔科夫链算法")
    print("  3. 使用最近6期数据")
    print("  4. 红球杀8个，蓝球杀1个")
    print("  5. 训练数据：200期")
    print()
    
    print("📋 kill_count_analysis_system算法特点:")
    print("  1. 使用EnsembleKillSystem类")
    print("  2. 基于集成学习（贝叶斯+马尔科夫1+马尔科夫2）")
    print("  3. 权重分配：贝叶斯50%，马尔科夫1 30%，马尔科夫2 20%")
    print("  4. 使用6期历史数据（current到prev5）")
    print("  5. 可配置杀号数量")
    print("  6. 训练数据：全部数据")
    print()
    
    print("🔍 关键差异分析:")
    print("  1. 算法架构:")
    print("     - 主程序：单一贝叶斯+马尔科夫算法")
    print("     - ensemble：多算法集成")
    print()
    
    print("  2. 数据使用:")
    print("     - 主程序：200期训练数据")
    print("     - ensemble：全部历史数据")
    print()
    
    print("  3. 参数配置:")
    print("     - 主程序：固定参数")
    print("     - ensemble：可调权重")
    print()
    
    print("  4. 数据格式:")
    print("     - 主程序：DataFrame格式")
    print("     - ensemble：period_data字典格式")

def check_data_consistency():
    """检查数据一致性"""
    print(f"\n📊 检查数据一致性:")
    print("=" * 80)
    
    # 初始化系统
    main_predictor = LotteryPredictor()
    
    print("🔍 数据格式对比:")
    
    # 检查第一期数据
    first_period = main_predictor.data.iloc[0]
    print(f"  第一期数据 (期号: {first_period['期号']}):")
    print(f"    DataFrame格式: {dict(first_period)}")
    
    # 模拟period_data格式
    if len(main_predictor.data) >= 6:
        period_data = {
            'current': main_predictor.data.iloc[0],
            'last': main_predictor.data.iloc[1],
            'prev2': main_predictor.data.iloc[2],
            'prev3': main_predictor.data.iloc[3],
            'prev4': main_predictor.data.iloc[4],
            'prev5': main_predictor.data.iloc[5]
        }
        
        print(f"  period_data格式:")
        for key, value in period_data.items():
            print(f"    {key}: 期号{value['期号']}")
    
    # 检查号码解析
    from test_kill_algorithm import parse_numbers
    
    red_balls, blue_balls = parse_numbers(first_period)
    print(f"\n  号码解析结果:")
    print(f"    红球: {red_balls}")
    print(f"    蓝球: {blue_balls}")

def main():
    """主函数"""
    try:
        compare_kill_systems()
        analyze_algorithm_differences()
        check_data_consistency()
        
        print(f"\n🎯 差异原因总结:")
        print("=" * 80)
        print("1. 算法架构不同:")
        print("   - 主程序使用单一算法")
        print("   - ensemble使用多算法集成")
        print()
        print("2. 训练数据范围不同:")
        print("   - 主程序限制200期")
        print("   - ensemble使用全部数据")
        print()
        print("3. 参数配置不同:")
        print("   - 权重分配不同")
        print("   - 目标杀号数量可能不同")
        print()
        print("4. 数据处理方式不同:")
        print("   - 数据格式和传递方式不同")
        print("   - 可能导致不同的计算结果")
        
    except Exception as e:
        print(f"❌ 对比过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
