#!/usr/bin/env python3
"""
代码迁移脚本
将现有的代码重构到新的项目结构中
"""

import sys
import shutil
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import setup_logging
from config.logging_config import get_logger

# 设置日志
setup_logging()
logger = get_logger('migrate')


class CodeMigrator:
    """代码迁移器"""
    
    def __init__(self):
        self.project_root = project_root
        self.migration_plan = self._create_migration_plan()
    
    def _create_migration_plan(self) -> Dict[str, Dict[str, str]]:
        """创建迁移计划"""
        return {
            # 核心模块迁移
            'core': {
                'analyzer.py': 'src/core/analyzer.py',
                'utils.py': 'src/utils/data_utils.py'  # 部分迁移
            },
            
            # 模型迁移
            'models': {
                'markov_model.py': 'src/models/markov/base_markov.py',
                'bayes_selector.py': 'src/models/bayes/bayes_selector.py',
                'neural_predictor.py': 'src/models/neural/base_neural.py',
                'enhanced_neural_predictor.py': 'src/models/neural/enhanced_neural.py',
                'ensemble_predictor.py': 'src/models/ensemble/ensemble_predictor.py'
            },
            
            # 生成器迁移
            'generators': {
                'generator.py': 'src/generators/base_generator.py',
                'advanced_generator.py': 'src/generators/advanced_generator.py',
                'precision_generator.py': 'src/generators/precision_generator.py',
                'dynamic_generator.py': 'src/generators/dynamic_generator.py',
                'diversified_generator.py': 'src/generators/diversified_generator.py',
                'insight_based_generator.py': 'src/generators/insight_generator.py'
            },
            
            # 特征工程迁移
            'features': {
                'feature_selector.py': 'src/features/selector.py',
                'external_features.py': 'src/features/extractor.py',
                'data_augmentation.py': 'src/features/engineering.py'
            },
            
            # 系统迁移
            'systems': {
                'main.py': 'src/systems/basic_system.py',
                'advanced_prediction_system.py': 'src/systems/advanced_system.py',
                'super_prediction_system.py': 'src/systems/super_system.py',
                'ultimate_prediction_system.py': 'src/systems/ultimate_system.py'
            },
            
            # 测试迁移
            'tests': {
                'test_analyzer.py': 'tests/unit/test_analyzer.py',
                'test_main_simple.py': 'tests/unit/test_basic_system.py',
                'test_advanced_system.py': 'tests/integration/test_advanced_system.py',
                'test_final.py': 'tests/integration/test_systems.py'
            }
        }
    
    def backup_original_files(self) -> None:
        """备份原始文件"""
        logger.info("备份原始文件...")
        
        backup_dir = self.project_root / 'backup_original'
        backup_dir.mkdir(exist_ok=True)
        
        # 备份所有Python文件
        for py_file in self.project_root.glob('*.py'):
            if py_file.name not in ['setup.py']:  # 排除一些文件
                backup_path = backup_dir / py_file.name
                shutil.copy2(py_file, backup_path)
                logger.debug(f"备份文件: {py_file.name}")
        
        logger.info(f"备份完成，文件保存在: {backup_dir}")
    
    def migrate_file(self, source_path: Path, target_path: Path) -> bool:
        """迁移单个文件"""
        try:
            if not source_path.exists():
                logger.warning(f"源文件不存在: {source_path}")
                return False
            
            # 确保目标目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            logger.info(f"迁移文件: {source_path.name} -> {target_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"迁移文件失败 {source_path} -> {target_path}: {e}")
            return False
    
    def update_imports_in_file(self, file_path: Path) -> None:
        """更新文件中的导入语句"""
        try:
            if not file_path.exists() or file_path.suffix != '.py':
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新导入语句的映射
            import_mappings = {
                'from utils import': 'from src.utils.data_utils import',
                'from analyzer import': 'from src.core.analyzer import',
                'from markov_model import': 'from src.models.markov.base_markov import',
                'from bayes_selector import': 'from src.models.bayes.bayes_selector import',
                'from generator import': 'from src.generators.base_generator import',
                'import utils': 'import src.utils.data_utils as utils',
                'import analyzer': 'import src.core.analyzer as analyzer'
            }
            
            # 应用映射
            updated = False
            for old_import, new_import in import_mappings.items():
                if old_import in content:
                    content = content.replace(old_import, new_import)
                    updated = True
            
            # 如果有更新，写回文件
            if updated:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.debug(f"更新导入语句: {file_path}")
                
        except Exception as e:
            logger.error(f"更新导入语句失败 {file_path}: {e}")
    
    def create_init_files(self) -> None:
        """创建__init__.py文件"""
        logger.info("创建__init__.py文件...")
        
        init_dirs = [
            'src/models/markov',
            'src/models/bayes', 
            'src/models/neural',
            'src/models/ensemble',
            'src/generators',
            'src/features',
            'src/systems',
            'tests/unit',
            'tests/integration',
            'tests/performance'
        ]
        
        for dir_path in init_dirs:
            full_path = self.project_root / dir_path
            if full_path.exists():
                init_file = full_path / '__init__.py'
                if not init_file.exists():
                    init_file.touch()
                    logger.debug(f"创建: {init_file}")
    
    def run_migration(self) -> None:
        """运行完整迁移"""
        logger.info("开始代码迁移...")
        
        # 1. 备份原始文件
        self.backup_original_files()
        
        # 2. 迁移文件
        total_files = 0
        migrated_files = 0
        
        for category, file_mappings in self.migration_plan.items():
            logger.info(f"迁移 {category} 模块...")
            
            for source_name, target_path in file_mappings.items():
                source_path = self.project_root / source_name
                target_full_path = self.project_root / target_path
                
                total_files += 1
                if self.migrate_file(source_path, target_full_path):
                    migrated_files += 1
        
        # 3. 创建__init__.py文件
        self.create_init_files()
        
        # 4. 更新导入语句
        logger.info("更新导入语句...")
        for py_file in self.project_root.rglob('src/**/*.py'):
            self.update_imports_in_file(py_file)
        
        # 5. 生成迁移报告
        logger.info("迁移完成!")
        logger.info(f"总文件数: {total_files}")
        logger.info(f"成功迁移: {migrated_files}")
        logger.info(f"失败文件: {total_files - migrated_files}")
        
        if migrated_files < total_files:
            logger.warning("部分文件迁移失败，请检查日志")
    
    def validate_migration(self) -> bool:
        """验证迁移结果"""
        logger.info("验证迁移结果...")
        
        # 检查关键文件是否存在
        key_files = [
            'src/core/base.py',
            'src/utils/data_utils.py',
            'config/settings.py',
            'scripts/main.py'
        ]
        
        missing_files = []
        for file_path in key_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
        
        if missing_files:
            logger.error(f"关键文件缺失: {missing_files}")
            return False
        
        # 检查目录结构
        required_dirs = [
            'src/core',
            'src/models',
            'src/generators',
            'src/utils',
            'config',
            'tests',
            'scripts',
            'docs'
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            logger.error(f"必要目录缺失: {missing_dirs}")
            return False
        
        logger.info("迁移验证通过!")
        return True


def main():
    """主函数"""
    migrator = CodeMigrator()
    
    try:
        # 运行迁移
        migrator.run_migration()
        
        # 验证迁移
        if migrator.validate_migration():
            logger.info("代码迁移成功完成!")
            print("\n✅ 代码迁移成功!")
            print("📁 新的项目结构已创建")
            print("🔧 请运行 'python scripts/main.py --validate-only' 验证环境")
        else:
            logger.error("迁移验证失败!")
            print("\n❌ 迁移验证失败，请检查日志")
            
    except Exception as e:
        logger.error(f"迁移过程出错: {e}")
        print(f"\n❌ 迁移失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
