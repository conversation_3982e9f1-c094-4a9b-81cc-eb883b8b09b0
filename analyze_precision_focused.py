#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析precision_focused组合中各算法的表现，优化组合
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict

# 导入现有的杀号算法
from test_kill_algorithm import KillAlgorithmTester, parse_numbers

class PrecisionFocusedAnalyzer:
    def __init__(self):
        self.data = None
        self.base_tester = KillAlgorithmTester()
        
        # precision_focused组合的原始算法
        self.original_algorithms = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
            'energy_kill', 'abundant_kill'
        ]
        
        # 备选的高成功率算法（基于之前测试的实际表现）
        self.candidate_algorithms = [
            'last_period_reverse',  # 90.2% - 上期反向杀号法
            'symmetry_kill',        # 90.0% - 对称杀号法
            'modular_kill',         # 90.0% - 模运算杀号法
            'prime_kill',           # 89.5% - 质数杀号法
            'prev2_period_half',    # 89.1% - 上上期÷2杀号法
            'month_kill',           # 89.1% - 月份维度杀号法
            'zodiac_kill',          # 89.1% - 生肖维度杀号法
            'weather_kill',         # 88.7% - 天气维度杀号法
            'season_kill',          # 88.2% - 季节维度杀号法
            'prev2_period_reverse', # 88.2% - 上上期反向杀号法
            'prev2_period_diff_kill', # 88.2% - 上上期差值杀号法
            'lunar_kill',           # 88.0% - 农历维度杀号法
            'weekday_kill',         # 87.9% - 星期维度杀号法
            'element_kill'          # 86.5% - 五行维度杀号法
        ]

    def load_data(self) -> bool:
        """加载数据"""
        if self.base_tester.load_data():
            self.data = self.base_tester.data
            return True
        return False

    def analyze_individual_algorithms(self, algorithms: List[str], test_periods: int = 50) -> Dict:
        """分析各个算法的个体表现"""
        print(f"\n🔍 分析各算法个体表现 (最近{test_periods}期)")
        print("=" * 80)
        
        algorithm_stats = {}
        
        for algo in algorithms:
            stats = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'failed_periods': [],
                'details': []
            }
            
            # 逐期测试
            for i in range(test_periods):
                if i + 2 >= len(self.data):
                    break
                    
                # 获取当前期和前两期数据
                current_period = self.data.iloc[i]
                period1_data = self.data.iloc[i + 1]  # 上一期
                period2_data = self.data.iloc[i + 2]  # 上二期
                
                # 解析号码
                current_red, _ = parse_numbers(current_period)
                period1_red, _ = parse_numbers(period1_data)
                period2_red, _ = parse_numbers(period2_data)
                
                # 获取算法杀号
                kills = self.base_tester._get_single_kill_number(algo, period1_red, period2_red, current_period)
                
                if kills and len(kills) > 0:
                    single_kill = kills[0]
                    if 1 <= single_kill <= 35 and single_kill not in (period1_red + period2_red):
                        is_successful = single_kill not in current_red
                        
                        stats['total_kills'] += 1
                        if is_successful:
                            stats['successful_kills'] += 1
                        else:
                            stats['failed_periods'].append({
                                'period': current_period['期号'],
                                'kill': single_kill,
                                'actual': current_red
                            })
                        
                        stats['details'].append({
                            'period': current_period['期号'],
                            'kill': single_kill,
                            'successful': is_successful,
                            'actual': current_red
                        })
            
            # 计算成功率
            if stats['total_kills'] > 0:
                stats['success_rate'] = stats['successful_kills'] / stats['total_kills']
            
            algorithm_stats[algo] = stats
        
        return algorithm_stats

    def find_worst_performers(self, algorithm_stats: Dict, num_worst: int = 3) -> List[str]:
        """找出表现最差的算法"""
        # 按成功率排序，找出最差的
        sorted_algos = sorted(algorithm_stats.items(), key=lambda x: x[1]['success_rate'])
        worst_performers = [algo for algo, _ in sorted_algos[:num_worst]]
        
        print(f"\n❌ 表现最差的{num_worst}个算法:")
        for i, (algo, stats) in enumerate(sorted_algos[:num_worst], 1):
            print(f"  {i}. {algo:25} 成功率:{stats['success_rate']:6.1%} "
                  f"({stats['successful_kills']}/{stats['total_kills']}) "
                  f"失败{len(stats['failed_periods'])}次")
            
            # 显示失败详情
            if stats['failed_periods']:
                print(f"     失败详情:")
                for fail in stats['failed_periods'][:3]:  # 只显示前3次失败
                    actual_str = ','.join(map(str, fail['actual']))
                    print(f"       {fail['period']}: 杀号[{fail['kill']}] 实际[{actual_str}]")
        
        return worst_performers

    def test_optimized_combination(self, original_algos: List[str], replacements: Dict[str, str], test_periods: int = 50) -> Dict:
        """测试优化后的组合"""
        # 创建新的算法列表
        optimized_algos = []
        for algo in original_algos:
            if algo in replacements:
                optimized_algos.append(replacements[algo])
            else:
                optimized_algos.append(algo)
        
        print(f"\n🔄 测试优化后的组合:")
        print(f"原始组合: {', '.join(original_algos)}")
        print(f"优化组合: {', '.join(optimized_algos)}")
        print(f"替换详情: {replacements}")
        print("=" * 80)
        
        # 使用组合测试器测试
        from test_algorithm_combinations import AlgorithmCombinationTester
        combo_tester = AlgorithmCombinationTester()
        combo_tester.data = self.data
        
        result = combo_tester.test_combination(optimized_algos, test_periods)
        
        return result, optimized_algos

    def print_optimization_results(self, original_result: Dict, optimized_result: Dict, replacements: Dict):
        """打印优化结果对比"""
        print(f"\n📊 优化结果对比")
        print("=" * 80)
        
        print(f"原始precision_focused组合:")
        print(f"  期成功率: {original_result.get('success_rate', 0):.1%}")
        print(f"  杀号成功率: {original_result.get('kill_success_rate', 0):.1%}")
        print(f"  平均每期杀号数: {original_result.get('total_kills', 0)/original_result.get('total_periods', 1):.1f}")
        
        print(f"\n优化后组合:")
        print(f"  期成功率: {optimized_result['success_rate']:.1%}")
        print(f"  杀号成功率: {optimized_result['kill_success_rate']:.1%}")
        print(f"  平均每期杀号数: {optimized_result['total_kills']/optimized_result['total_periods']:.1f}")
        
        print(f"\n改进情况:")
        period_improvement = optimized_result['success_rate'] - original_result.get('success_rate', 0)
        kill_improvement = optimized_result['kill_success_rate'] - original_result.get('kill_success_rate', 0)
        print(f"  期成功率改进: {period_improvement:+.1%}")
        print(f"  杀号成功率改进: {kill_improvement:+.1%}")
        
        print(f"\n替换详情:")
        for old_algo, new_algo in replacements.items():
            print(f"  {old_algo} → {new_algo}")
        
        # 显示最近几期的详细情况
        print(f"\n最近5期详情:")
        for detail in optimized_result['details'][:5]:
            kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
            actual_str = ','.join(map(str, detail['actual_red']))
            status = "✅" if detail['period_success'] else "❌"
            print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                  f"成功{detail['successful_kills']}/{detail['total_kills']} {status}")

def main():
    """主函数"""
    print("🎯 precision_focused组合优化分析系统")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = PrecisionFocusedAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 分析原始组合中各算法的表现
    algorithm_stats = analyzer.analyze_individual_algorithms(analyzer.original_algorithms, test_periods=50)
    
    # 打印各算法表现
    print(f"\n📊 precision_focused组合中各算法表现:")
    sorted_stats = sorted(algorithm_stats.items(), key=lambda x: x[1]['success_rate'], reverse=True)
    for i, (algo, stats) in enumerate(sorted_stats, 1):
        status = "🎯" if stats['success_rate'] >= 0.9 else "⚠️" if stats['success_rate'] >= 0.8 else "❌"
        print(f"  {i}. {algo:25} 成功率:{stats['success_rate']:6.1%} "
              f"({stats['successful_kills']}/{stats['total_kills']}) {status}")
    
    # 找出表现最差的算法
    worst_performers = analyzer.find_worst_performers(algorithm_stats, num_worst=3)
    
    # 分析候选算法的表现
    print(f"\n🔍 分析候选替换算法表现:")
    candidate_stats = analyzer.analyze_individual_algorithms(analyzer.candidate_algorithms, test_periods=50)
    
    # 找出最佳候选算法（只选择有有效杀号的算法）
    valid_candidates = [(algo, stats) for algo, stats in candidate_stats.items() if stats['total_kills'] > 0]
    best_candidates = sorted(valid_candidates, key=lambda x: x[1]['success_rate'], reverse=True)[:5]

    print(f"\n✅ 最佳候选算法 (有效杀号):")
    for i, (algo, stats) in enumerate(best_candidates, 1):
        print(f"  {i}. {algo:25} 成功率:{stats['success_rate']:6.1%} "
              f"({stats['successful_kills']}/{stats['total_kills']}) 🎯")

    # 创建替换方案 - 只替换表现最差的算法
    replacements = {}

    # 只替换abundant_kill（表现最差的）
    if len(best_candidates) > 0 and 'abundant_kill' in worst_performers:
        # 找一个不在原始组合中的最佳候选算法
        for candidate_algo, candidate_stats in best_candidates:
            if candidate_algo not in analyzer.original_algorithms:
                replacements['abundant_kill'] = candidate_algo
                break

    # 如果还有其他明显表现不佳的算法（成功率<90%），也考虑替换
    for algo, stats in algorithm_stats.items():
        if stats['success_rate'] < 0.90 and algo != 'abundant_kill' and len(replacements) < 2:
            # 找一个不在原始组合中且不在已替换中的候选算法
            for candidate_algo, candidate_stats in best_candidates:
                if (candidate_algo not in analyzer.original_algorithms and
                    candidate_algo not in replacements.values()):
                    replacements[algo] = candidate_algo
                    break
    
    print(f"\n🔄 建议的替换方案:")
    for old_algo, new_algo in replacements.items():
        old_rate = algorithm_stats[old_algo]['success_rate']
        new_rate = candidate_stats[new_algo]['success_rate']
        improvement = new_rate - old_rate
        print(f"  {old_algo} ({old_rate:.1%}) → {new_algo} ({new_rate:.1%}) 改进{improvement:+.1%}")
    
    # 测试优化后的组合
    optimized_result, optimized_algos = analyzer.test_optimized_combination(
        analyzer.original_algorithms, replacements, test_periods=50
    )
    
    # 原始结果（从之前的测试中获得）
    original_result = {
        'success_rate': 1.0,  # 100%期成功率
        'kill_success_rate': 0.902,  # 90.2%杀号成功率
        'total_kills': 234,
        'total_periods': 50
    }
    
    # 打印优化结果
    analyzer.print_optimization_results(original_result, optimized_result, replacements)
    
    print(f"\n🎉 precision_focused组合优化分析完成！")

if __name__ == "__main__":
    main()
