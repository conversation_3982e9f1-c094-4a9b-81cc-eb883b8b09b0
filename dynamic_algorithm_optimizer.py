#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
动态算法优化系统
根据历史表现动态调整算法组合，替换表现较差的算法
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple
from collections import defaultdict, deque
import json

# 导入现有的杀号算法
from test_kill_algorithm import KillAlgorithmTester, parse_numbers
from test_algorithm_combinations import AlgorithmCombinationTester

class DynamicAlgorithmOptimizer:
    def __init__(self):
        self.data = None
        self.base_tester = KillAlgorithmTester()
        self.combo_tester = AlgorithmCombinationTester()
        
        # 所有可用算法池
        self.algorithm_pool = [
            # 顶级算法 (95%+)
            'factorial_kill', 'pentagonal_kill', 'abundant_kill',
            
            # 优秀算法 (90%+)
            'last_period_plus', 'catalan_kill', 'prev2_period_pattern_kill',
            'direction_kill', 'psychology_kill', 'span', 'odd_even',
            'interval_kill', 'last_period_reverse', 'symmetry_kill', 'modular_kill',
            
            # 良好算法 (85%+)
            'prime_kill', 'prev2_period_half', 'month_kill', 'zodiac_kill',
            'weather_kill', 'season_kill', 'prev2_period_reverse', 'prev2_period_diff_kill',
            'lunar_kill', 'weekday_kill', 'element_kill', 'energy_kill',
            
            # 时间维度算法
            'last_period_diff_kill', 'last_period_minus', 'last_period_pattern_kill',
            'last_period_avg_kill', 'last_period_median_kill', 'prev2_period_plus',
            'prev2_period_minus', 'prev2_period_avg_kill', 'prev2_period_median_kill'
        ]
        
        # 当前最佳组合
        self.current_best_combo = [
            'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
            'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'last_period_reverse',
            'symmetry_kill', 'odd_even'
        ]
        
        # 算法表现历史
        self.algorithm_history = defaultdict(lambda: deque(maxlen=20))  # 保留最近20期表现
        self.combo_history = deque(maxlen=10)  # 保留最近10次组合测试结果

    def load_data(self) -> bool:
        """加载数据"""
        if self.base_tester.load_data():
            self.data = self.base_tester.data
            self.combo_tester.data = self.data
            return True
        return False

    def analyze_individual_algorithm_performance(self, algorithms: List[str], test_periods: int = 30) -> Dict:
        """分析各个算法的最新表现"""
        print(f"\n🔍 分析各算法最新表现 (最近{test_periods}期)")
        print("=" * 80)
        
        algorithm_stats = {}
        
        for algo in algorithms:
            stats = {
                'total_kills': 0,
                'successful_kills': 0,
                'success_rate': 0.0,
                'recent_performance': [],
                'trend': 'stable'
            }
            
            # 逐期测试
            for i in range(test_periods):
                if i + 2 >= len(self.data):
                    break
                    
                # 获取当前期和前两期数据
                current_period = self.data.iloc[i]
                period1_data = self.data.iloc[i + 1]
                period2_data = self.data.iloc[i + 2]
                
                # 解析号码
                current_red, _ = parse_numbers(current_period)
                period1_red, _ = parse_numbers(period1_data)
                period2_red, _ = parse_numbers(period2_data)
                
                # 获取算法杀号
                kills = self.base_tester._get_single_kill_number(algo, period1_red, period2_red, current_period)
                
                if kills and len(kills) > 0:
                    single_kill = kills[0]
                    if 1 <= single_kill <= 35 and single_kill not in (period1_red + period2_red):
                        is_successful = single_kill not in current_red
                        
                        stats['total_kills'] += 1
                        if is_successful:
                            stats['successful_kills'] += 1
                        
                        # 记录最近表现
                        stats['recent_performance'].append(1 if is_successful else 0)
                        
                        # 更新历史记录
                        self.algorithm_history[algo].append(1 if is_successful else 0)
            
            # 计算成功率
            if stats['total_kills'] > 0:
                stats['success_rate'] = stats['successful_kills'] / stats['total_kills']
            
            # 分析趋势
            if len(stats['recent_performance']) >= 10:
                recent_10 = stats['recent_performance'][-10:]
                earlier_10 = stats['recent_performance'][-20:-10] if len(stats['recent_performance']) >= 20 else []
                
                if earlier_10:
                    recent_rate = sum(recent_10) / len(recent_10)
                    earlier_rate = sum(earlier_10) / len(earlier_10)
                    
                    if recent_rate > earlier_rate + 0.1:
                        stats['trend'] = 'improving'
                    elif recent_rate < earlier_rate - 0.1:
                        stats['trend'] = 'declining'
                    else:
                        stats['trend'] = 'stable'
            
            algorithm_stats[algo] = stats
        
        return algorithm_stats

    def identify_underperforming_algorithms(self, algorithm_stats: Dict, threshold: float = 0.85) -> List[str]:
        """识别表现不佳的算法"""
        underperforming = []
        
        for algo, stats in algorithm_stats.items():
            if (stats['success_rate'] < threshold or 
                stats['trend'] == 'declining' or
                (stats['total_kills'] > 0 and stats['success_rate'] < 0.80)):
                underperforming.append(algo)
        
        return underperforming

    def find_replacement_algorithms(self, current_combo: List[str], underperforming: List[str], current_stats: Dict, test_periods: int = 20) -> Dict[str, str]:
        """为表现不佳的算法找到替换方案"""
        print(f"\n🔄 为表现不佳的算法寻找替换方案")
        print("=" * 60)

        # 候选算法（不在当前组合中的算法）
        candidates = [algo for algo in self.algorithm_pool if algo not in current_combo]

        # 测试候选算法的表现
        candidate_stats = self.analyze_individual_algorithm_performance(candidates, test_periods)

        # 按成功率排序候选算法
        sorted_candidates = sorted(
            [(algo, stats) for algo, stats in candidate_stats.items() if stats['total_kills'] > 0],
            key=lambda x: (x[1]['success_rate'], x[1]['trend'] == 'improving'),
            reverse=True
        )

        print(f"🏆 最佳候选算法:")
        for i, (algo, stats) in enumerate(sorted_candidates[:10], 1):
            trend_icon = "📈" if stats['trend'] == 'improving' else "📉" if stats['trend'] == 'declining' else "➡️"
            print(f"  {i}. {algo:25} 成功率:{stats['success_rate']:6.1%} "
                  f"趋势:{stats['trend']} {trend_icon}")

        # 为每个表现不佳的算法找到最佳替换
        replacements = {}
        used_candidates = set()

        for underperform_algo in underperforming:
            for candidate_algo, candidate_stats in sorted_candidates:
                if candidate_algo not in used_candidates:
                    replacements[underperform_algo] = candidate_algo
                    used_candidates.add(candidate_algo)
                    old_rate = current_stats.get(underperform_algo, {}).get('success_rate', 0)
                    new_rate = candidate_stats['success_rate']
                    improvement = new_rate - old_rate
                    print(f"  替换方案: {underperform_algo} → {candidate_algo} "
                          f"(成功率提升: {improvement:+.1%})")
                    break

        return replacements

    def test_optimized_combo(self, original_combo: List[str], replacements: Dict[str, str], test_periods: int = 50) -> Tuple[Dict, List[str]]:
        """测试优化后的组合"""
        # 创建新的组合
        optimized_combo = []
        for algo in original_combo:
            if algo in replacements:
                optimized_combo.append(replacements[algo])
            else:
                optimized_combo.append(algo)
        
        print(f"\n🧪 测试优化后的组合 (最近{test_periods}期)")
        print("=" * 60)
        print(f"原始组合: {', '.join(original_combo)}")
        print(f"优化组合: {', '.join(optimized_combo)}")
        print(f"替换详情: {replacements}")
        
        # 测试新组合
        result = self.combo_tester.test_combination(optimized_combo, test_periods)
        
        # 记录到历史
        self.combo_history.append({
            'combo': optimized_combo.copy(),
            'replacements': replacements.copy(),
            'result': result,
            'timestamp': pd.Timestamp.now()
        })
        
        return result, optimized_combo

    def dynamic_optimization_cycle(self, test_periods: int = 50) -> Dict:
        """执行一轮动态优化"""
        print(f"\n🚀 执行动态优化周期")
        print("=" * 80)
        
        # 1. 分析当前组合中各算法的表现
        current_stats = self.analyze_individual_algorithm_performance(self.current_best_combo, test_periods)
        
        # 2. 识别表现不佳的算法
        underperforming = self.identify_underperforming_algorithms(current_stats, threshold=0.85)
        
        print(f"\n❌ 识别出{len(underperforming)}个表现不佳的算法:")
        for algo in underperforming:
            stats = current_stats[algo]
            trend_icon = "📈" if stats['trend'] == 'improving' else "📉" if stats['trend'] == 'declining' else "➡️"
            print(f"  - {algo:25} 成功率:{stats['success_rate']:6.1%} "
                  f"趋势:{stats['trend']} {trend_icon}")
        
        if not underperforming:
            print("✅ 当前组合中所有算法表现良好，无需替换")
            # 仍然测试当前组合以获得最新表现
            result = self.combo_tester.test_combination(self.current_best_combo, test_periods)
            return {
                'optimization_needed': False,
                'current_result': result,
                'current_combo': self.current_best_combo
            }
        
        # 3. 寻找替换算法
        replacements = self.find_replacement_algorithms(self.current_best_combo, underperforming, current_stats, test_periods//2)
        
        if not replacements:
            print("⚠️ 未找到合适的替换算法")
            result = self.combo_tester.test_combination(self.current_best_combo, test_periods)
            return {
                'optimization_needed': False,
                'current_result': result,
                'current_combo': self.current_best_combo
            }
        
        # 4. 测试原始组合
        original_result = self.combo_tester.test_combination(self.current_best_combo, test_periods)
        
        # 5. 测试优化后的组合
        optimized_result, optimized_combo = self.test_optimized_combo(self.current_best_combo, replacements, test_periods)
        
        # 6. 比较结果并决定是否采用新组合
        improvement = optimized_result['success_rate'] - original_result['success_rate']
        kill_improvement = optimized_result['kill_success_rate'] - original_result['kill_success_rate']
        
        print(f"\n📊 优化结果对比:")
        print(f"原始组合:")
        print(f"  期成功率: {original_result['success_rate']:.1%} ({original_result['successful_periods']}/{original_result['total_periods']})")
        print(f"  杀号成功率: {original_result['kill_success_rate']:.1%}")
        print(f"  平均杀号数: {original_result['total_kills']/original_result['total_periods']:.1f}")
        
        print(f"\n优化组合:")
        print(f"  期成功率: {optimized_result['success_rate']:.1%} ({optimized_result['successful_periods']}/{optimized_result['total_periods']})")
        print(f"  杀号成功率: {optimized_result['kill_success_rate']:.1%}")
        print(f"  平均杀号数: {optimized_result['total_kills']/optimized_result['total_periods']:.1f}")
        
        print(f"\n改进效果:")
        print(f"  期成功率改进: {improvement:+.1%}")
        print(f"  杀号成功率改进: {kill_improvement:+.1%}")
        
        # 决定是否采用新组合
        if improvement > 0 or (improvement == 0 and kill_improvement > 0):
            print(f"\n✅ 采用优化后的组合 (有改进)")
            self.current_best_combo = optimized_combo
            adopted = True
        else:
            print(f"\n❌ 保持原始组合 (无明显改进)")
            adopted = False
        
        return {
            'optimization_needed': True,
            'original_result': original_result,
            'optimized_result': optimized_result,
            'optimized_combo': optimized_combo,
            'replacements': replacements,
            'improvement': improvement,
            'kill_improvement': kill_improvement,
            'adopted': adopted,
            'current_combo': self.current_best_combo
        }

def main():
    """主函数"""
    print("🎯 动态算法优化系统")
    print("=" * 60)
    
    # 初始化优化器
    optimizer = DynamicAlgorithmOptimizer()
    
    # 加载数据
    if not optimizer.load_data():
        return
    
    # 执行动态优化周期
    result = optimizer.dynamic_optimization_cycle(test_periods=50)
    
    # 打印最终结果
    print(f"\n🎉 动态优化周期完成!")
    print(f"当前最佳组合: {', '.join(result['current_combo'])}")
    
    if result['optimization_needed'] and result['adopted']:
        print(f"✅ 成功优化! 期成功率提升 {result['improvement']:+.1%}")
    elif result['optimization_needed'] and not result['adopted']:
        print(f"⚠️ 尝试优化但无明显改进，保持原组合")
    else:
        print(f"✅ 当前组合表现良好，无需优化")

if __name__ == "__main__":
    main()
