"""
超参数优化器
使用贝叶斯优化等方法进行超参数调优
"""

import numpy as np
from typing import List, Tuple, Dict, Optional, Callable
import json
from datetime import datetime


class BayesianOptimizer:
    """贝叶斯优化器（简化实现）"""
    
    def __init__(self, parameter_space: Dict[str, Tuple[float, float]], 
                 acquisition_function: str = "expected_improvement"):
        """
        初始化贝叶斯优化器
        
        Args:
            parameter_space: 参数空间，格式为 {param_name: (min_val, max_val)}
            acquisition_function: 采集函数类型
        """
        self.parameter_space = parameter_space
        self.acquisition_function = acquisition_function
        
        # 历史记录
        self.X_observed = []  # 观测到的参数组合
        self.y_observed = []  # 对应的目标函数值
        
        # 高斯过程参数（简化实现）
        self.gp_noise = 1e-6
        self.gp_length_scale = 1.0
        self.gp_signal_variance = 1.0
    
    def optimize(self, objective_function: Callable, n_iterations: int = 50, 
                n_initial_points: int = 5) -> Dict:
        """
        执行贝叶斯优化
        
        Args:
            objective_function: 目标函数
            n_iterations: 优化迭代次数
            n_initial_points: 初始随机点数量
            
        Returns:
            Dict: 优化结果
        """
        print(f"开始贝叶斯优化，参数空间: {self.parameter_space}")
        
        # 初始随机采样
        print(f"初始随机采样 {n_initial_points} 个点...")
        for i in range(n_initial_points):
            params = self._sample_random_parameters()
            score = objective_function(params)
            
            self.X_observed.append(list(params.values()))
            self.y_observed.append(score)
            
            print(f"  初始点 {i+1}: {params} -> {score:.4f}")
        
        # 贝叶斯优化迭代
        print(f"开始贝叶斯优化迭代...")
        for iteration in range(n_iterations):
            # 选择下一个采样点
            next_params = self._select_next_point()
            
            # 评估目标函数
            score = objective_function(next_params)
            
            # 更新观测数据
            self.X_observed.append(list(next_params.values()))
            self.y_observed.append(score)
            
            if iteration % 10 == 0:
                best_score = max(self.y_observed)
                print(f"  迭代 {iteration}: {next_params} -> {score:.4f} (最佳: {best_score:.4f})")
        
        # 返回最佳结果
        best_idx = np.argmax(self.y_observed)
        best_params_list = self.X_observed[best_idx]
        best_params = dict(zip(self.parameter_space.keys(), best_params_list))
        best_score = self.y_observed[best_idx]
        
        result = {
            'best_parameters': best_params,
            'best_score': best_score,
            'optimization_history': {
                'parameters': self.X_observed.copy(),
                'scores': self.y_observed.copy()
            },
            'n_evaluations': len(self.y_observed)
        }
        
        print(f"优化完成！最佳参数: {best_params}, 最佳分数: {best_score:.4f}")
        
        return result
    
    def _sample_random_parameters(self) -> Dict[str, float]:
        """随机采样参数"""
        params = {}
        for param_name, (min_val, max_val) in self.parameter_space.items():
            params[param_name] = np.random.uniform(min_val, max_val)
        return params
    
    def _select_next_point(self) -> Dict[str, float]:
        """选择下一个采样点"""
        # 简化实现：使用随机搜索 + 局部搜索
        
        # 生成候选点
        n_candidates = 100
        candidates = []
        acquisition_values = []
        
        for _ in range(n_candidates):
            # 80%概率随机采样，20%概率在最佳点附近采样
            if np.random.random() < 0.8:
                candidate = self._sample_random_parameters()
            else:
                candidate = self._sample_near_best()
            
            candidates.append(candidate)
            
            # 计算采集函数值
            acq_value = self._acquisition_function_value(candidate)
            acquisition_values.append(acq_value)
        
        # 选择采集函数值最大的点
        best_idx = np.argmax(acquisition_values)
        return candidates[best_idx]
    
    def _sample_near_best(self) -> Dict[str, float]:
        """在最佳点附近采样"""
        if not self.y_observed:
            return self._sample_random_parameters()
        
        best_idx = np.argmax(self.y_observed)
        best_params_list = self.X_observed[best_idx]
        best_params = dict(zip(self.parameter_space.keys(), best_params_list))
        
        # 在最佳点附近添加噪声
        params = {}
        for param_name, (min_val, max_val) in self.parameter_space.items():
            current_val = best_params[param_name]
            noise_scale = (max_val - min_val) * 0.1  # 10%的范围
            
            new_val = current_val + np.random.normal(0, noise_scale)
            new_val = np.clip(new_val, min_val, max_val)
            
            params[param_name] = new_val
        
        return params
    
    def _acquisition_function_value(self, params: Dict[str, float]) -> float:
        """计算采集函数值（简化实现）"""
        if not self.y_observed:
            return np.random.random()
        
        # 简化的期望改进
        params_list = list(params.values())
        
        # 计算与已观测点的距离
        distances = []
        for observed_params in self.X_observed:
            dist = np.linalg.norm(np.array(params_list) - np.array(observed_params))
            distances.append(dist)
        
        min_distance = min(distances)
        
        # 预测均值和方差（简化）
        if min_distance < 1e-6:
            # 如果太接近已观测点，返回低值
            return 0.0
        
        # 简化的高斯过程预测
        weights = np.exp(-np.array(distances) / self.gp_length_scale)
        weights /= np.sum(weights)
        
        predicted_mean = np.sum(weights * np.array(self.y_observed))
        predicted_var = self.gp_signal_variance * (1 - np.sum(weights**2))
        
        # 期望改进
        best_observed = max(self.y_observed)
        improvement = predicted_mean - best_observed
        
        if predicted_var > 0:
            z = improvement / np.sqrt(predicted_var)
            ei = improvement * self._normal_cdf(z) + np.sqrt(predicted_var) * self._normal_pdf(z)
        else:
            ei = 0.0
        
        return ei
    
    def _normal_cdf(self, x):
        """标准正态分布累积分布函数"""
        return 0.5 * (1 + np.tanh(x / np.sqrt(2)))
    
    def _normal_pdf(self, x):
        """标准正态分布概率密度函数"""
        return np.exp(-0.5 * x**2) / np.sqrt(2 * np.pi)


class GridSearchOptimizer:
    """网格搜索优化器"""
    
    def __init__(self, parameter_grid: Dict[str, List]):
        """
        初始化网格搜索优化器
        
        Args:
            parameter_grid: 参数网格，格式为 {param_name: [val1, val2, ...]}
        """
        self.parameter_grid = parameter_grid
        self.results = []
    
    def optimize(self, objective_function: Callable) -> Dict:
        """
        执行网格搜索
        
        Args:
            objective_function: 目标函数
            
        Returns:
            Dict: 优化结果
        """
        print(f"开始网格搜索，参数网格: {self.parameter_grid}")
        
        # 生成所有参数组合
        param_combinations = self._generate_combinations()
        
        print(f"总共 {len(param_combinations)} 个参数组合")
        
        best_score = -float('inf')
        best_params = None
        
        for i, params in enumerate(param_combinations):
            score = objective_function(params)
            
            self.results.append({
                'parameters': params.copy(),
                'score': score
            })
            
            if score > best_score:
                best_score = score
                best_params = params.copy()
            
            if i % 10 == 0:
                print(f"  进度: {i+1}/{len(param_combinations)}, 当前最佳: {best_score:.4f}")
        
        result = {
            'best_parameters': best_params,
            'best_score': best_score,
            'all_results': self.results.copy(),
            'n_evaluations': len(param_combinations)
        }
        
        print(f"网格搜索完成！最佳参数: {best_params}, 最佳分数: {best_score:.4f}")
        
        return result
    
    def _generate_combinations(self) -> List[Dict]:
        """生成所有参数组合"""
        param_names = list(self.parameter_grid.keys())
        param_values = list(self.parameter_grid.values())
        
        combinations = []
        
        def generate_recursive(index, current_params):
            if index == len(param_names):
                combinations.append(current_params.copy())
                return
            
            param_name = param_names[index]
            for value in param_values[index]:
                current_params[param_name] = value
                generate_recursive(index + 1, current_params)
                del current_params[param_name]
        
        generate_recursive(0, {})
        return combinations


class RandomSearchOptimizer:
    """随机搜索优化器"""
    
    def __init__(self, parameter_space: Dict[str, Tuple[float, float]]):
        """
        初始化随机搜索优化器
        
        Args:
            parameter_space: 参数空间，格式为 {param_name: (min_val, max_val)}
        """
        self.parameter_space = parameter_space
        self.results = []
    
    def optimize(self, objective_function: Callable, n_iterations: int = 100) -> Dict:
        """
        执行随机搜索
        
        Args:
            objective_function: 目标函数
            n_iterations: 搜索次数
            
        Returns:
            Dict: 优化结果
        """
        print(f"开始随机搜索，参数空间: {self.parameter_space}")
        
        best_score = -float('inf')
        best_params = None
        
        for i in range(n_iterations):
            # 随机采样参数
            params = {}
            for param_name, (min_val, max_val) in self.parameter_space.items():
                params[param_name] = np.random.uniform(min_val, max_val)
            
            # 评估目标函数
            score = objective_function(params)
            
            self.results.append({
                'parameters': params.copy(),
                'score': score
            })
            
            if score > best_score:
                best_score = score
                best_params = params.copy()
            
            if i % 20 == 0:
                print(f"  进度: {i+1}/{n_iterations}, 当前最佳: {best_score:.4f}")
        
        result = {
            'best_parameters': best_params,
            'best_score': best_score,
            'all_results': self.results.copy(),
            'n_evaluations': n_iterations
        }
        
        print(f"随机搜索完成！最佳参数: {best_params}, 最佳分数: {best_score:.4f}")
        
        return result


class HyperparameterOptimizer:
    """超参数优化器主类"""
    
    def __init__(self):
        """初始化超参数优化器"""
        self.optimization_history = []
    
    def optimize_neural_network(self, train_function: Callable, 
                               optimization_method: str = "bayesian",
                               n_iterations: int = 30) -> Dict:
        """
        优化神经网络超参数
        
        Args:
            train_function: 训练函数，接受参数字典，返回验证分数
            optimization_method: 优化方法 ("bayesian", "grid", "random")
            n_iterations: 迭代次数
            
        Returns:
            Dict: 优化结果
        """
        print(f"开始神经网络超参数优化，方法: {optimization_method}")
        
        # 定义超参数空间
        if optimization_method == "grid":
            parameter_grid = {
                'learning_rate': [0.001, 0.003, 0.01, 0.03],
                'batch_size': [16, 32, 64],
                'dropout_rate': [0.1, 0.2, 0.3, 0.4],
                'hidden_size_1': [64, 128, 256],
                'hidden_size_2': [32, 64, 128]
            }
            optimizer = GridSearchOptimizer(parameter_grid)
            result = optimizer.optimize(train_function)
        
        elif optimization_method == "random":
            parameter_space = {
                'learning_rate': (0.0001, 0.1),
                'batch_size': (8, 128),
                'dropout_rate': (0.0, 0.5),
                'hidden_size_1': (32, 512),
                'hidden_size_2': (16, 256)
            }
            optimizer = RandomSearchOptimizer(parameter_space)
            result = optimizer.optimize(train_function, n_iterations)
        
        else:  # bayesian
            parameter_space = {
                'learning_rate': (0.0001, 0.1),
                'batch_size': (8, 128),
                'dropout_rate': (0.0, 0.5),
                'hidden_size_1': (32, 512),
                'hidden_size_2': (16, 256)
            }
            optimizer = BayesianOptimizer(parameter_space)
            result = optimizer.optimize(train_function, n_iterations)
        
        # 记录优化历史
        optimization_record = {
            'timestamp': datetime.now().isoformat(),
            'method': optimization_method,
            'result': result
        }
        self.optimization_history.append(optimization_record)
        
        return result
    
    def optimize_ensemble_weights(self, evaluation_function: Callable,
                                n_models: int = 4) -> Dict:
        """
        优化集成权重
        
        Args:
            evaluation_function: 评估函数，接受权重列表，返回性能分数
            n_models: 模型数量
            
        Returns:
            Dict: 优化结果
        """
        print(f"优化 {n_models} 个模型的集成权重...")
        
        def objective_function(params):
            # 将参数转换为权重（确保和为1）
            raw_weights = [params[f'weight_{i}'] for i in range(n_models)]
            weights = np.array(raw_weights)
            weights = weights / np.sum(weights)  # 归一化
            
            return evaluation_function(weights.tolist())
        
        # 定义权重参数空间
        parameter_space = {}
        for i in range(n_models):
            parameter_space[f'weight_{i}'] = (0.01, 1.0)
        
        optimizer = BayesianOptimizer(parameter_space)
        result = optimizer.optimize(objective_function, n_iterations=20)
        
        # 转换最佳参数为权重
        best_params = result['best_parameters']
        raw_weights = [best_params[f'weight_{i}'] for i in range(n_models)]
        best_weights = np.array(raw_weights)
        best_weights = best_weights / np.sum(best_weights)
        
        result['best_weights'] = best_weights.tolist()
        
        print(f"最佳集成权重: {best_weights}")
        
        return result
    
    def save_optimization_history(self, filepath: str):
        """保存优化历史"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.optimization_history, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"优化历史已保存到 {filepath}")
    
    def load_optimization_history(self, filepath: str) -> bool:
        """加载优化历史"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.optimization_history = json.load(f)
            
            print(f"优化历史已从 {filepath} 加载")
            return True
        except Exception as e:
            print(f"加载优化历史失败: {e}")
            return False


def test_hyperparameter_optimization():
    """测试超参数优化"""
    
    # 模拟训练函数
    def mock_train_function(params):
        """模拟训练函数"""
        # 模拟一个有噪声的目标函数
        lr = params['learning_rate']
        batch_size = params['batch_size']
        dropout = params['dropout_rate']
        h1 = params['hidden_size_1']
        h2 = params['hidden_size_2']
        
        # 模拟性能分数（添加噪声）
        score = (
            0.8 - abs(lr - 0.01) * 10 +  # 最优学习率约为0.01
            0.1 * (1 - abs(batch_size - 32) / 32) +  # 最优批大小约为32
            0.1 * (1 - abs(dropout - 0.2) * 2) +  # 最优dropout约为0.2
            0.05 * (h1 / 512) +  # 更大的隐藏层更好
            0.05 * (h2 / 256) +
            np.random.normal(0, 0.05)  # 添加噪声
        )
        
        return max(0, min(1, score))  # 限制在[0,1]范围内
    
    # 测试不同优化方法
    optimizer = HyperparameterOptimizer()
    
    print("=" * 60)
    print("测试贝叶斯优化")
    print("=" * 60)
    
    bayesian_result = optimizer.optimize_neural_network(
        mock_train_function, "bayesian", n_iterations=20
    )
    
    print("\n" + "=" * 60)
    print("测试随机搜索")
    print("=" * 60)
    
    random_result = optimizer.optimize_neural_network(
        mock_train_function, "random", n_iterations=20
    )
    
    print("\n" + "=" * 60)
    print("结果比较")
    print("=" * 60)
    
    print(f"贝叶斯优化最佳分数: {bayesian_result['best_score']:.4f}")
    print(f"随机搜索最佳分数: {random_result['best_score']:.4f}")
    
    # 测试集成权重优化
    def mock_ensemble_evaluation(weights):
        """模拟集成评估函数"""
        # 假设最优权重为 [0.4, 0.3, 0.2, 0.1]
        optimal_weights = np.array([0.4, 0.3, 0.2, 0.1])
        weights = np.array(weights)
        
        # 计算与最优权重的距离
        distance = np.linalg.norm(weights - optimal_weights)
        score = 1.0 - distance + np.random.normal(0, 0.02)
        
        return max(0, min(1, score))
    
    print("\n" + "=" * 60)
    print("测试集成权重优化")
    print("=" * 60)
    
    ensemble_result = optimizer.optimize_ensemble_weights(mock_ensemble_evaluation, 4)
    print(f"最佳集成权重: {ensemble_result['best_weights']}")


if __name__ == "__main__":
    test_hyperparameter_optimization()
