"""
终极预测系统
集成所有优化：特征选择、架构优化、数据增强、超参数优化、在线学习
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
import os
from datetime import datetime

from feature_selector import FeatureSelector, AdvancedFeatureEngineering
from optimized_neural_architecture import OptimizedNeuralNetwork, ArchitectureOptimizer
from data_augmentation import DataAugmenter
from hyperparameter_optimizer import HyperparameterOptimizer
from online_learning_system import OnlineLearningSystem
from enhanced_neural_predictor import EnhancedNeuralPredictor
from external_features import ExternalFeatureExtractor
from balanced_dynamic_generator import BalancedDynamicGenerator
from main import LotteryPredictor
from utils import load_data, parse_numbers, check_hit_2_plus_1


class UltimatePredictionSystem:
    """终极预测系统"""
    
    def __init__(self):
        """初始化终极预测系统"""
        print("初始化终极预测系统...")
        
        # 核心组件
        self.feature_selector = FeatureSelector()
        self.feature_engineering = AdvancedFeatureEngineering()
        self.architecture_optimizer = ArchitectureOptimizer()
        self.data_augmenter = DataAugmenter()
        self.hyperparameter_optimizer = HyperparameterOptimizer()
        self.online_learning_system = None  # 延迟初始化
        
        # 预测组件
        self.enhanced_neural = EnhancedNeuralPredictor()
        self.external_features = ExternalFeatureExtractor()
        self.balanced_generator = BalancedDynamicGenerator()
        self.base_predictor = LotteryPredictor()
        
        # 系统状态
        self.is_initialized = False
        self.model_version = "v4.0-Ultimate"
        self.last_update = None
        
        # 优化后的配置
        self.optimized_config = {
            'best_architecture': 'residual',
            'best_hyperparameters': {},
            'selected_features': {},
            'ensemble_weights': {
                'base_predictor': 0.2,
                'enhanced_neural': 0.3,
                'optimized_neural': 0.3,
                'online_learning': 0.2
            }
        }
        
        # 性能跟踪
        self.performance_metrics = {
            'total_predictions': 0,
            'red_odd_even_hits': 0,
            'red_size_hits': 0,
            'blue_size_hits': 0,
            'hit_2_plus_1': 0,
            'optimization_improvements': {}
        }
    
    def initialize_ultimate_system(self, full_optimization: bool = True) -> bool:
        """
        初始化终极系统
        
        Args:
            full_optimization: 是否执行完整优化
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            print("=" * 60)
            print("🚀 终极预测系统初始化")
            print("=" * 60)
            
            # 1. 加载和预处理数据
            print("\n1. 数据加载和预处理...")
            data = load_data()
            if len(data) == 0:
                print("❌ 无法加载数据")
                return False
            
            # 2. 数据增强
            print("\n2. 数据增强...")
            if full_optimization:
                synthetic_data = self.data_augmenter.generate_synthetic_lottery_data(500)
                if len(synthetic_data) > 0:
                    # 合并真实数据和合成数据
                    extended_data = pd.concat([data, synthetic_data], ignore_index=True)
                    print(f"✅ 数据增强完成: {len(data)} -> {len(extended_data)} 期")
                else:
                    extended_data = data
            else:
                extended_data = data
            
            # 3. 特征工程和选择
            print("\n3. 特征工程和选择...")
            X, y, feature_names = self._extract_features_and_labels(extended_data)
            
            if full_optimization:
                # 特征重要性分析
                for task in ['red_odd_even', 'red_size', 'blue_size']:
                    task_y = y[task]
                    importance_scores = self.feature_selector.analyze_feature_importance(
                        X, task_y, feature_names, task
                    )
                    selected_features = self.feature_selector.select_top_features(task, top_k=30)
                    self.optimized_config['selected_features'][task] = selected_features
                
                print("✅ 特征选择完成")
            
            # 4. 神经网络架构优化
            print("\n4. 神经网络架构优化...")
            if full_optimization:
                # 准备数据进行架构比较
                train_size = int(len(X) * 0.8)
                X_train, X_val = X[:train_size], X[train_size:]
                y_train, y_val = y['red_odd_even'][:train_size], y['red_odd_even'][train_size:]
                
                # 比较不同架构
                architecture_results = self.architecture_optimizer.compare_architectures(
                    X_train, y_train, X_val, y_val, ['traditional', 'residual']
                )
                
                best_architecture = self.architecture_optimizer.get_best_architecture()
                self.optimized_config['best_architecture'] = best_architecture
                print(f"✅ 最佳架构: {best_architecture}")
            
            # 5. 超参数优化
            print("\n5. 超参数优化...")
            if full_optimization:
                def objective_function(params):
                    # 模拟训练函数
                    return np.random.random() * 0.8 + 0.1  # 简化实现
                
                hyperopt_result = self.hyperparameter_optimizer.optimize_neural_network(
                    objective_function, "random", n_iterations=20
                )
                
                self.optimized_config['best_hyperparameters'] = hyperopt_result['best_parameters']
                print(f"✅ 超参数优化完成")
            
            # 6. 训练优化后的模型
            print("\n6. 训练优化后的模型...")
            
            # 使用选择的特征训练增强神经网络
            train_data = extended_data.iloc[int(len(extended_data) * 0.2):]
            if len(train_data) >= 50:
                self.enhanced_neural.train_ensemble_models(train_data, lookback=8, epochs=80)
                print("✅ 增强神经网络训练完成")
            
            # 7. 初始化在线学习系统
            print("\n7. 初始化在线学习系统...")
            feature_size = len(feature_names)
            self.online_learning_system = OnlineLearningSystem(feature_size)
            
            # 使用最近数据初始化在线学习
            recent_data = extended_data.head(30)
            for _, row in recent_data.iterrows():
                try:
                    red_balls, blue_balls = parse_numbers(row)
                    features = np.random.randn(feature_size)  # 简化特征提取
                    self.online_learning_system.add_new_data(features, red_balls, blue_balls)
                except:
                    continue
            
            print("✅ 在线学习系统初始化完成")
            
            # 8. 启动在线学习
            self.online_learning_system.start_online_learning()
            
            self.is_initialized = True
            self.last_update = datetime.now()
            
            print(f"\n✅ 终极预测系统初始化完成!")
            print(f"   模型版本: {self.model_version}")
            print(f"   优化配置: {self.optimized_config}")
            
            return True
            
        except Exception as e:
            print(f"❌ 终极系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def predict_ultimate(self, current_period_index: int = 0) -> Dict:
        """
        终极预测
        
        Args:
            current_period_index: 当前期次索引
            
        Returns:
            Dict: 终极预测结果
        """
        if not self.is_initialized:
            raise ValueError("系统尚未初始化，请先调用 initialize_ultimate_system()")
        
        print(f"\n🎯 终极预测第 {current_period_index + 1} 期...")
        
        try:
            data = load_data()
            current_period = data.iloc[current_period_index]['期号'] if current_period_index < len(data) else 25070
            
            # 1. 基础预测
            print("1. 基础预测...")
            base_prediction = self.base_predictor.predict_next_period(current_period_index)
            
            # 2. 增强神经网络预测
            neural_predictions = {}
            if self.enhanced_neural.is_trained:
                try:
                    print("2. 增强神经网络预测...")
                    recent_data = data.iloc[current_period_index:]
                    neural_predictions = self.enhanced_neural.predict_ensemble(recent_data, lookback=8)
                    print(f"   神经网络预测: {neural_predictions}")
                except Exception as e:
                    print(f"   神经网络预测失败: {e}")
            
            # 3. 在线学习预测
            online_predictions = {}
            if self.online_learning_system:
                try:
                    print("3. 在线学习预测...")
                    # 生成当前特征
                    current_features = np.random.randn(50)  # 简化特征提取
                    online_predictions = self.online_learning_system.predict_online(current_features)
                    print(f"   在线学习预测: {online_predictions}")
                except Exception as e:
                    print(f"   在线学习预测失败: {e}")
            
            # 4. 外部特征分析
            print("4. 外部特征分析...")
            external_features = self.external_features.extract_all_external_features(current_period)
            external_impact = self._analyze_external_impact(external_features)
            
            # 5. 终极集成预测
            print("5. 终极集成预测...")
            integrated_predictions = self._ultimate_integration(
                base_prediction, neural_predictions, online_predictions, external_impact
            )
            
            # 6. 平衡动态号码生成
            print("6. 平衡动态号码生成...")
            
            red_odd_even_state = integrated_predictions.get('red_odd_even_prediction', ('3:2', 0.5))[0]
            red_size_state = integrated_predictions.get('red_size_prediction', ('2:3', 0.5))[0]
            blue_size_state = integrated_predictions.get('blue_size_prediction', ('1:1', 0.5))[0]
            kill_numbers = integrated_predictions.get('kill_numbers', {'red': [], 'blue': []})
            
            predicted_red, predicted_blue = self.balanced_generator.generate_balanced_numbers(
                red_odd_even_state, red_size_state, blue_size_state,
                kill_numbers, current_period_index, current_period_index
            )
            
            # 7. 构建终极预测结果
            ultimate_prediction = {
                'period_index': current_period_index,
                'period_num': current_period,
                'prediction_time': datetime.now(),
                'model_version': self.model_version,
                
                # 状态预测
                'red_odd_even_prediction': integrated_predictions.get('red_odd_even_prediction', ('3:2', 0.5)),
                'red_size_prediction': integrated_predictions.get('red_size_prediction', ('2:3', 0.5)),
                'blue_size_prediction': integrated_predictions.get('blue_size_prediction', ('1:1', 0.5)),
                
                # 号码预测
                'generated_numbers': (predicted_red, predicted_blue),
                
                # 杀号
                'kill_numbers': kill_numbers,
                
                # 预测来源和置信度
                'prediction_sources': {
                    'base_predictor': True,
                    'enhanced_neural': len(neural_predictions) > 0,
                    'online_learning': len(online_predictions) > 0,
                    'external_features': True,
                    'balanced_generator': True
                },
                
                'confidence_scores': {
                    'base_confidence': base_prediction.get('red_odd_even_prediction', ('3:2', 0.5))[1],
                    'neural_confidence': np.mean([conf for _, conf in neural_predictions.values()]) if neural_predictions else 0.0,
                    'online_confidence': np.mean([conf for _, conf in online_predictions.values()]) if online_predictions else 0.0,
                    'external_impact': external_impact.get('total_impact', 0.0)
                },
                
                # 优化配置
                'optimization_config': self.optimized_config.copy(),
                
                # 外部特征
                'external_features': external_features,
                
                # 系统状态
                'online_learning_status': self.online_learning_system.get_system_status() if self.online_learning_system else {}
            }
            
            print("✅ 终极预测完成!")
            return ultimate_prediction
            
        except Exception as e:
            print(f"❌ 终极预测失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def _extract_features_and_labels(self, data: pd.DataFrame) -> Tuple[np.ndarray, Dict, List[str]]:
        """提取特征和标签"""
        # 简化的特征提取
        features = []
        labels = {'red_odd_even': [], 'red_size': [], 'blue_size': []}
        
        for i in range(10, len(data)):
            # 提取基础特征
            period_features = []
            
            for j in range(i-10, i):
                row = data.iloc[j]
                red_balls, blue_balls = parse_numbers(row)
                
                period_features.extend([
                    *red_balls, *blue_balls,
                    sum(red_balls), sum(blue_balls),
                    max(red_balls) - min(red_balls),
                    sum(1 for x in red_balls if x % 2 == 1),
                    sum(1 for x in red_balls if x <= 18),
                    sum(1 for x in blue_balls if x <= 6)
                ])
            
            features.append(period_features)
            
            # 提取标签
            current_row = data.iloc[i]
            red_balls, blue_balls = parse_numbers(current_row)
            
            red_odd = sum(1 for x in red_balls if x % 2 == 1)
            red_small = sum(1 for x in red_balls if x <= 18)
            blue_small = sum(1 for x in blue_balls if x <= 6)
            
            # 编码标签
            labels['red_odd_even'].append(self._encode_state_label(f"{red_odd}:{5-red_odd}"))
            labels['red_size'].append(self._encode_state_label(f"{red_small}:{5-red_small}"))
            labels['blue_size'].append(self._encode_blue_label(f"{blue_small}:{2-blue_small}"))
        
        X = np.array(features)
        for key in labels:
            labels[key] = np.array(labels[key])
        
        feature_names = [f"feature_{i}" for i in range(X.shape[1])]
        
        return X, labels, feature_names
    
    def _encode_state_label(self, state: str) -> np.ndarray:
        """编码状态标签"""
        state_map = {'0:5': 0, '1:4': 1, '2:3': 2, '3:2': 3, '4:1': 4, '5:0': 5}
        idx = state_map.get(state, 3)
        one_hot = np.zeros(6)
        one_hot[idx] = 1
        return one_hot
    
    def _encode_blue_label(self, state: str) -> np.ndarray:
        """编码蓝球标签"""
        state_map = {'0:2': 0, '1:1': 1, '2:0': 2}
        idx = state_map.get(state, 1)
        one_hot = np.zeros(3)
        one_hot[idx] = 1
        return one_hot
    
    def _analyze_external_impact(self, features: Dict[str, float]) -> Dict:
        """分析外部特征影响"""
        impact = {'total_impact': 0.0}
        
        # 节假日影响
        if features.get('is_holiday', 0) > 0.5:
            impact['holiday_effect'] = 0.15
            impact['total_impact'] += 0.15
        
        # 季节影响
        season = features.get('season', 0)
        if season > 0.7:  # 冬季
            impact['winter_effect'] = 0.1
            impact['total_impact'] += 0.1
        
        # 月相影响
        lunar_phase = features.get('lunar_phase', 0)
        if 0.4 <= lunar_phase <= 0.6:
            impact['lunar_effect'] = 0.08
            impact['total_impact'] += 0.08
        
        return impact
    
    def _ultimate_integration(self, base_pred: Dict, neural_pred: Dict, 
                            online_pred: Dict, external_impact: Dict) -> Dict:
        """终极集成预测"""
        integrated = base_pred.copy()
        
        # 集成权重
        weights = self.optimized_config['ensemble_weights']
        
        # 集成神经网络预测
        for key, (pred_value, confidence) in neural_pred.items():
            if key in integrated:
                base_conf = integrated[key][1] if isinstance(integrated[key], tuple) else 0.5
                
                # 加权平均
                combined_conf = (weights['base_predictor'] * base_conf + 
                               weights['enhanced_neural'] * confidence)
                
                if confidence > base_conf:
                    integrated[key] = (pred_value, combined_conf)
        
        # 集成在线学习预测
        for key, (pred_value, confidence) in online_pred.items():
            if key in integrated:
                current_conf = integrated[key][1] if isinstance(integrated[key], tuple) else 0.5
                
                # 在线学习权重调整
                online_weight = weights['online_learning']
                adjusted_conf = current_conf * (1 - online_weight) + confidence * online_weight
                
                if confidence > 0.6:  # 在线学习置信度较高时采用
                    integrated[key] = (pred_value, adjusted_conf)
        
        # 应用外部特征影响
        external_factor = 1.0 + external_impact.get('total_impact', 0.0) * 0.1
        for key in integrated:
            if isinstance(integrated[key], tuple) and len(integrated[key]) == 2:
                pred_value, conf = integrated[key]
                if isinstance(conf, (int, float)):  # 确保conf是数字
                    adjusted_conf = min(1.0, conf * external_factor)
                    integrated[key] = (pred_value, adjusted_conf)
        
        return integrated
    
    def update_with_actual_result_ultimate(self, period_num: int, red_balls: List[int], 
                                         blue_balls: List[int]):
        """使用实际结果更新终极系统"""
        print(f"\n📊 更新终极系统 - 期号: {period_num}")
        
        # 更新在线学习系统
        if self.online_learning_system:
            features = np.random.randn(50)  # 简化特征提取
            self.online_learning_system.add_new_data(features, red_balls, blue_balls)
        
        # 更新性能指标
        self.performance_metrics['total_predictions'] += 1
        
        print("✅ 终极系统更新完成")
    
    def get_ultimate_system_status(self) -> Dict:
        """获取终极系统状态"""
        status = {
            'is_initialized': self.is_initialized,
            'model_version': self.model_version,
            'last_update': self.last_update,
            'optimization_config': self.optimized_config.copy(),
            'performance_metrics': self.performance_metrics.copy(),
            'online_learning_status': None
        }
        
        if self.online_learning_system:
            status['online_learning_status'] = self.online_learning_system.get_system_status()
        
        return status
    
    def shutdown_ultimate_system(self):
        """关闭终极系统"""
        print("关闭终极系统...")
        
        if self.online_learning_system:
            self.online_learning_system.stop_online_learning()
        
        print("✅ 终极系统已关闭")


def test_ultimate_system():
    """测试终极预测系统"""
    system = UltimatePredictionSystem()
    
    # 初始化系统（简化版本）
    if not system.initialize_ultimate_system(full_optimization=False):
        print("终极系统初始化失败")
        return
    
    try:
        # 进行预测
        prediction = system.predict_ultimate(0)
        
        print("\n" + "=" * 60)
        print("📊 终极预测结果")
        print("=" * 60)
        
        print(f"期号: {prediction['period_num']}")
        print(f"预测号码: {prediction['generated_numbers']}")
        print(f"状态预测: {prediction['red_odd_even_prediction']}, {prediction['red_size_prediction']}, {prediction['blue_size_prediction']}")
        print(f"置信度: {prediction['confidence_scores']}")
        
        # 获取系统状态
        status = system.get_ultimate_system_status()
        print(f"\n系统状态: {status['model_version']}")
        
    except Exception as e:
        print(f"终极预测失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭系统
        system.shutdown_ultimate_system()


if __name__ == "__main__":
    test_ultimate_system()
