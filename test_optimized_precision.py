#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化的precision_focused组合
基于分析结果手动优化组合
"""

from test_algorithm_combinations import AlgorithmCombinationTester

def main():
    """主函数"""
    print("🎯 优化precision_focused组合测试")
    print("=" * 60)
    
    # 初始化测试器
    tester = AlgorithmCombinationTester()
    
    # 加载数据
    if not tester.load_data():
        return
    
    # 原始precision_focused组合
    original_precision = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'energy_kill', 'abundant_kill'
    ]
    
    # 优化方案1：替换表现最差的abundant_kill和energy_kill
    optimized_v1 = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'last_period_reverse',  # 替换energy_kill (90.2%成功率)
        'symmetry_kill'         # 替换abundant_kill (90.0%成功率)
    ]
    
    # 优化方案2：替换为更多数学算法
    optimized_v2 = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'odd_even',            # 替换energy_kill (90.9%成功率)
        'modular_kill'         # 替换abundant_kill (90.0%成功率)
    ]
    
    # 优化方案3：混合最佳算法
    optimized_v3 = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'prev2_period_half',   # 替换energy_kill (89.1%成功率)
        'prime_kill'           # 替换abundant_kill (89.5%成功率)
    ]
    
    # 优化方案4：保守优化（只替换最差的abundant_kill）
    optimized_v4 = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'energy_kill',         # 保留energy_kill
        'interval_kill'        # 替换abundant_kill (90.5%成功率)
    ]
    
    # 优化方案5：全面升级
    optimized_v5 = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'last_period_reverse',
        'symmetry_kill',       # 替换span
        'odd_even'             # 替换abundant_kill
    ]
    
    # 测试所有组合
    combinations = {
        'original_precision': original_precision,
        'optimized_v1_mixed': optimized_v1,
        'optimized_v2_math': optimized_v2,
        'optimized_v3_balanced': optimized_v3,
        'optimized_v4_conservative': optimized_v4,
        'optimized_v5_upgraded': optimized_v5
    }
    
    results = {}
    
    print(f"\n🔍 测试各种优化方案 (最近50期)")
    print("=" * 80)
    
    for combo_name, algorithms in combinations.items():
        print(f"\n正在测试: {combo_name}")
        print(f"算法列表: {', '.join(algorithms)}")
        result = tester.test_combination(algorithms, test_periods=50)
        results[combo_name] = result
    
    # 打印结果对比
    print(f"\n📊 优化方案对比结果")
    print("=" * 80)
    
    # 按成功率排序
    sorted_results = sorted(results.items(), key=lambda x: (x[1]['success_rate'], x[1]['kill_success_rate']), reverse=True)
    
    print("🏆 优化方案排行:")
    for i, (combo_name, result) in enumerate(sorted_results, 1):
        status = "🎯" if result['success_rate'] >= 1.0 else "⚠️" if result['success_rate'] >= 0.9 else "❌"
        improvement = ""
        if combo_name != 'original_precision':
            original_result = results['original_precision']
            period_diff = result['success_rate'] - original_result['success_rate']
            kill_diff = result['kill_success_rate'] - original_result['kill_success_rate']
            improvement = f" (期成功率{period_diff:+.1%}, 杀号成功率{kill_diff:+.1%})"
        
        print(f"  {i}. {combo_name:25} 期成功率:{result['success_rate']:6.1%} "
              f"杀号成功率:{result['kill_success_rate']:6.1%} "
              f"平均杀号:{result['total_kills']/result['total_periods']:.1f}{improvement} {status}")
    
    # 详细分析最佳方案
    best_combo_name, best_result = sorted_results[0]
    if best_combo_name != 'original_precision':
        print(f"\n🎯 最佳优化方案详细分析: {best_combo_name}")
        print("=" * 60)
        
        best_algorithms = combinations[best_combo_name]
        original_algorithms = combinations['original_precision']
        
        print(f"原始算法: {', '.join(original_algorithms)}")
        print(f"优化算法: {', '.join(best_algorithms)}")
        
        # 找出替换的算法
        replacements = []
        for i, (orig, opt) in enumerate(zip(original_algorithms, best_algorithms)):
            if orig != opt:
                replacements.append(f"{orig} → {opt}")
        
        if replacements:
            print(f"替换详情: {', '.join(replacements)}")
        
        original_result = results['original_precision']
        print(f"\n改进效果:")
        print(f"  期成功率: {original_result['success_rate']:.1%} → {best_result['success_rate']:.1%} "
              f"({best_result['success_rate'] - original_result['success_rate']:+.1%})")
        print(f"  杀号成功率: {original_result['kill_success_rate']:.1%} → {best_result['kill_success_rate']:.1%} "
              f"({best_result['kill_success_rate'] - original_result['kill_success_rate']:+.1%})")
        print(f"  平均杀号数: {original_result['total_kills']/original_result['total_periods']:.1f} → "
              f"{best_result['total_kills']/best_result['total_periods']:.1f}")
        
        # 显示最近几期详情
        print(f"\n最近5期详情:")
        for detail in best_result['details'][:5]:
            kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
            actual_str = ','.join(map(str, detail['actual_red']))
            status = "✅" if detail['period_success'] else "❌"
            print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                  f"成功{detail['successful_kills']}/{detail['total_kills']} {status}")
    else:
        print(f"\n📝 分析结果: 原始precision_focused组合已经是最优方案")
    
    print(f"\n🎉 precision_focused组合优化测试完成！")

if __name__ == "__main__":
    main()
