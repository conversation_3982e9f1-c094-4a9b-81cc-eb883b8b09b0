#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试蓝球范围修正效果
验证蓝球算法是否正确使用1-12范围
"""

from src.systems.main import LotteryPredictor
import pandas as pd

def test_blue_ball_range_fix():
    """测试蓝球范围修正效果"""
    print("🔵 测试蓝球范围修正效果 (1-12)")
    print("=" * 60)
    
    # 初始化预测系统
    predictor = LotteryPredictor()
    
    # 测试前3期的预测
    for i in range(3):
        print(f"\n📅 测试第 {i+1} 期预测:")
        
        try:
            # 进行预测
            prediction = predictor.predict_next_period(i)
            
            # 显示蓝球杀号结果
            kill_numbers = prediction['kill_numbers']
            blue_kills = kill_numbers['blue_universal']
            
            print(f"  期号: {prediction['period']}")
            print(f"  蓝球杀号: {blue_kills} (共{len(blue_kills)}个)")
            
            # 验证蓝球杀号范围
            print(f"  🔍 蓝球杀号范围验证:")
            
            if blue_kills:
                min_blue = min(blue_kills)
                max_blue = max(blue_kills)
                print(f"    最小号码: {min_blue}")
                print(f"    最大号码: {max_blue}")
                
                # 检查是否在正确范围内
                valid_range = all(1 <= num <= 12 for num in blue_kills)
                if valid_range:
                    print(f"    ✅ 范围正确: 所有杀号都在1-12范围内")
                else:
                    invalid_nums = [num for num in blue_kills if not (1 <= num <= 12)]
                    print(f"    ❌ 范围错误: {invalid_nums} 超出1-12范围")
                
                # 分析蓝球杀号特征
                odd_count = sum(1 for num in blue_kills if num % 2 == 1)
                even_count = len(blue_kills) - odd_count
                small_count = sum(1 for num in blue_kills if num <= 6)
                large_count = len(blue_kills) - small_count
                
                print(f"    奇偶分布: 奇数{odd_count}个, 偶数{even_count}个")
                print(f"    大小分布: 小数(1-6){small_count}个, 大数(7-12){large_count}个")
            else:
                print(f"    ⚠️  没有蓝球杀号")
            
        except Exception as e:
            print(f"  ❌ 预测失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 蓝球范围测试完成！")

def test_blue_ball_algorithm_directly():
    """直接测试蓝球算法"""
    print("\n🧪 直接测试蓝球算法")
    print("=" * 60)
    
    try:
        # 加载数据
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}: {len(data)} 期")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 获取最近几期蓝球数据
        recent_blue_periods = []
        for i in range(6):
            if i < len(data):
                from test_kill_algorithm import parse_numbers
                _, blue_balls = parse_numbers(data.iloc[i])
                recent_blue_periods.append(blue_balls)
        
        print(f"\n📊 最近蓝球数据:")
        for i, blues in enumerate(recent_blue_periods):
            print(f"  第{i+1}期: {blues}")
        
        # 测试蓝球算法
        from bayesian_markov_killer import BayesianMarkovKiller
        killer = BayesianMarkovKiller(data)
        
        blue_kills = killer.calculate_blue_kills(recent_blue_periods, target_count=5)
        
        print(f"\n🎯 蓝球杀号结果:")
        print(f"  杀号列表: {blue_kills}")
        print(f"  杀号数量: {len(blue_kills)}个")
        
        if blue_kills:
            # 验证范围
            valid_range = all(1 <= num <= 12 for num in blue_kills)
            print(f"  范围验证: {'✅ 正确 (1-12)' if valid_range else '❌ 错误'}")
            
            # 分析特征
            min_num = min(blue_kills)
            max_num = max(blue_kills)
            print(f"  号码范围: {min_num}-{max_num}")
            
            odd_count = sum(1 for num in blue_kills if num % 2 == 1)
            small_count = sum(1 for num in blue_kills if num <= 6)
            
            print(f"  奇偶分布: 奇数{odd_count}个, 偶数{len(blue_kills)-odd_count}个")
            print(f"  大小分布: 小数{small_count}个, 大数{len(blue_kills)-small_count}个")
        
        print(f"\n✅ 直接测试完成")
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()

def validate_blue_ball_data():
    """验证蓝球数据的实际范围"""
    print("\n📊 验证蓝球数据的实际范围")
    print("=" * 60)
    
    try:
        # 加载数据
        data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
        data = None
        
        for file_name in data_files:
            try:
                data = pd.read_csv(file_name)
                print(f"✅ 成功加载数据文件 {file_name}")
                break
            except FileNotFoundError:
                continue
        
        if data is None:
            print("❌ 未找到数据文件")
            return
        
        # 分析蓝球数据范围
        all_blue_balls = []
        for i, row in data.head(50).iterrows():
            from test_kill_algorithm import parse_numbers
            _, blue_balls = parse_numbers(row)
            
            if isinstance(blue_balls, list):
                all_blue_balls.extend(blue_balls)
            else:
                all_blue_balls.append(blue_balls)
        
        if all_blue_balls:
            min_blue = min(all_blue_balls)
            max_blue = max(all_blue_balls)
            unique_blues = sorted(set(all_blue_balls))
            
            print(f"📈 蓝球数据分析 (最近50期):")
            print(f"  最小蓝球: {min_blue}")
            print(f"  最大蓝球: {max_blue}")
            print(f"  出现过的蓝球: {unique_blues}")
            print(f"  蓝球种类数: {len(unique_blues)}个")
            
            # 验证是否符合1-12范围
            if min_blue >= 1 and max_blue <= 12:
                print(f"  ✅ 确认: 蓝球范围确实是1-12")
            elif min_blue >= 1 and max_blue <= 16:
                print(f"  ⚠️  发现: 蓝球范围可能是1-16")
            else:
                print(f"  ❓ 未知: 蓝球范围需要进一步确认")
            
            # 统计频率
            from collections import Counter
            blue_counter = Counter(all_blue_balls)
            print(f"\n📊 蓝球出现频率 (前10):")
            for num, count in blue_counter.most_common(10):
                print(f"    {num:2d}号: {count:2d}次")
        
        print(f"\n✅ 数据验证完成")
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    validate_blue_ball_data()
    test_blue_ball_algorithm_directly()
    test_blue_ball_range_fix()
