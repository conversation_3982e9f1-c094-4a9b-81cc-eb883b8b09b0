#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
蓝球分析系统 - 基于贝叶斯和马尔科夫链
分析蓝球杀号数量与成功率的关系
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from itertools import combinations
from collections import defaultdict, Counter
import math

class BlueBallBayesianAlgorithm:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.prior_probs = {}
        self.conditional_probs = {}
        self._calculate_probabilities()
    
    def _calculate_probabilities(self):
        """计算蓝球的先验概率和条件概率"""
        print("🔍 计算蓝球贝叶斯概率...")
        
        # 收集蓝球数据
        all_blue_balls = []
        blue_patterns = []
        
        for i, row in self.data.head(200).iterrows():
            from test_kill_algorithm import parse_numbers
            red_balls, blue_balls = parse_numbers(row)
            
            # 处理蓝球数据（可能是列表）
            if isinstance(blue_balls, list):
                current_blues = blue_balls
            else:
                current_blues = [blue_balls]
            
            all_blue_balls.extend(current_blues)
            
            # 记录模式：前一期的蓝球 -> 当期蓝球
            if i < len(self.data) - 1:
                prev_row = self.data.iloc[i + 1]
                _, prev_blues = parse_numbers(prev_row)
                if isinstance(prev_blues, list):
                    prev_blues_tuple = tuple(sorted(prev_blues))
                else:
                    prev_blues_tuple = (prev_blues,)
                
                blue_patterns.append((prev_blues_tuple, tuple(sorted(current_blues))))
        
        # 计算先验概率（蓝球范围是1-12）
        total_count = len(all_blue_balls)
        for num in range(1, 13):  # 蓝球范围1-12
            self.prior_probs[num] = all_blue_balls.count(num) / total_count if total_count > 0 else 1/12
        
        # 计算条件概率
        pattern_counts = defaultdict(lambda: defaultdict(int))
        pattern_totals = defaultdict(int)
        
        for prev_blues, curr_blues in blue_patterns:
            pattern_totals[prev_blues] += 1
            for blue in curr_blues:
                pattern_counts[prev_blues][blue] += 1
        
        # 应用拉普拉斯平滑
        for prev_blues in pattern_counts:
            total = pattern_totals[prev_blues]
            for num in range(1, 13):
                count = pattern_counts[prev_blues][num]
                self.conditional_probs[(prev_blues, num)] = (count + 1) / (total + 12)
    
    def predict_kill_blue_balls(self, period_data: Dict, target_count: int = 3) -> List[int]:
        """预测蓝球杀号"""
        # 获取前期蓝球
        from test_kill_algorithm import parse_numbers
        _, prev_blues = parse_numbers(period_data['last'])
        
        if isinstance(prev_blues, list):
            prev_blues_tuple = tuple(sorted(prev_blues))
        else:
            prev_blues_tuple = (prev_blues,)
        
        # 计算每个蓝球的后验概率
        posterior_probs = {}
        
        for num in range(1, 13):
            prior = self.prior_probs.get(num, 1/12)
            conditional = self.conditional_probs.get((prev_blues_tuple, num), 1/12)
            posterior_probs[num] = conditional * prior
        
        # 选择概率最低的号码作为杀号
        sorted_nums = sorted(posterior_probs.items(), key=lambda x: x[1])
        kill_numbers = [num for num, prob in sorted_nums[:target_count]]
        
        return kill_numbers

class BlueBallMarkovChainAlgorithm:
    def __init__(self, data: pd.DataFrame, order: int = 1):
        self.data = data
        self.order = order
        self.transition_matrix = {}
        self._build_transition_matrix()
    
    def _build_transition_matrix(self):
        """构建蓝球状态转移矩阵"""
        print(f"🔍 构建蓝球{self.order}阶马尔科夫链...")
        
        # 收集蓝球状态序列
        states = []
        for i, row in self.data.head(300).iterrows():
            from test_kill_algorithm import parse_numbers
            _, blue_balls = parse_numbers(row)
            
            # 提取蓝球特征
            state = self._extract_blue_state_features(blue_balls)
            states.append(state)
        
        # 构建转移计数
        transition_counts = defaultdict(lambda: defaultdict(int))
        state_counts = defaultdict(int)
        
        for i in range(len(states) - self.order):
            if self.order == 1:
                current_state = states[i]
            else:
                current_state = tuple(states[i:i+self.order])
            
            next_state = states[i + self.order]
            
            transition_counts[current_state][next_state] += 1
            state_counts[current_state] += 1
        
        # 计算转移概率
        for current_state in transition_counts:
            total = state_counts[current_state]
            for next_state in transition_counts[current_state]:
                count = transition_counts[current_state][next_state]
                self.transition_matrix[(current_state, next_state)] = count / total
    
    def _extract_blue_state_features(self, blue_balls) -> Tuple:
        """提取蓝球状态特征"""
        if isinstance(blue_balls, list):
            blues = blue_balls
        else:
            blues = [blue_balls]
        
        # 特征：奇偶性、大小、数值范围
        odd_count = sum(1 for num in blues if num % 2 == 1)
        large_count = sum(1 for num in blues if num > 6)  # 蓝球1-12，6为中位数
        avg_value = sum(blues) / len(blues) if blues else 0

        odd_ratio = "high" if odd_count >= len(blues) / 2 else "low"
        large_ratio = "high" if large_count >= len(blues) / 2 else "low"
        value_range = "high" if avg_value > 8 else "medium" if avg_value > 4 else "low"  # 调整为蓝球范围
        
        return (odd_ratio, large_ratio, value_range)
    
    def predict_kill_blue_balls(self, period_data: Dict, target_count: int = 3) -> List[int]:
        """使用马尔科夫链预测蓝球杀号"""
        from test_kill_algorithm import parse_numbers
        
        if self.order == 1:
            _, prev_blues = parse_numbers(period_data['last'])
            current_state = self._extract_blue_state_features(prev_blues)
        else:
            _, prev_blues = parse_numbers(period_data['last'])
            _, prev2_blues = parse_numbers(period_data['prev2'])
            state1 = self._extract_blue_state_features(prev2_blues)
            state2 = self._extract_blue_state_features(prev_blues)
            current_state = (state1, state2)
        
        # 预测下一个状态
        next_state_probs = defaultdict(float)
        for (state, next_state), prob in self.transition_matrix.items():
            if state == current_state:
                next_state_probs[next_state] += prob
        
        if not next_state_probs:
            return list(range(1, target_count + 1))
        
        # 选择概率最高的下一状态
        most_likely_state = max(next_state_probs.items(), key=lambda x: x[1])[0]
        
        # 根据预测状态选择杀号
        kill_numbers = self._select_blue_kills_by_state(most_likely_state, target_count)
        
        return kill_numbers
    
    def _select_blue_kills_by_state(self, predicted_state: Tuple, target_count: int) -> List[int]:
        """根据预测状态选择蓝球杀号"""
        odd_ratio, large_ratio, value_range = predicted_state
        
        kill_numbers = []
        
        # 根据预测的奇偶分布选择杀号
        if odd_ratio == "high":
            kill_numbers.extend([2, 4, 6])  # 杀偶数
        else:
            kill_numbers.extend([1, 3, 5])  # 杀奇数
        
        # 根据预测的大小分布选择杀号
        if large_ratio == "high":
            kill_numbers.extend([1, 2])     # 杀小数
        else:
            kill_numbers.extend([11, 12])   # 杀大数

        # 去重并限制数量
        kill_numbers = list(set(kill_numbers))[:target_count]

        # 如果不够，补充一些号码
        while len(kill_numbers) < target_count:
            for num in range(7, 11):  # 中间号码
                if num not in kill_numbers:
                    kill_numbers.append(num)
                    break
        
        return kill_numbers[:target_count]

class BlueBallEnsembleSystem:
    def __init__(self, data: pd.DataFrame):
        self.data = data
        self.bayesian_algo = BlueBallBayesianAlgorithm(data)
        self.markov1_algo = BlueBallMarkovChainAlgorithm(data, order=1)
        self.markov2_algo = BlueBallMarkovChainAlgorithm(data, order=2)
        self.weights = {'bayesian': 0.4, 'markov1': 0.3, 'markov2': 0.3}
    
    def predict_ensemble_blue_kills(self, period_data: Dict, target_count: int = 3) -> List[int]:
        """集成预测蓝球杀号"""
        # 获取各模型的预测
        bayesian_kills = self.bayesian_algo.predict_kill_blue_balls(period_data, target_count)
        markov1_kills = self.markov1_algo.predict_kill_blue_balls(period_data, target_count)
        markov2_kills = self.markov2_algo.predict_kill_blue_balls(period_data, target_count)
        
        # 投票机制
        vote_counts = defaultdict(float)
        
        for num in bayesian_kills:
            vote_counts[num] += self.weights['bayesian']
        
        for num in markov1_kills:
            vote_counts[num] += self.weights['markov1']
        
        for num in markov2_kills:
            vote_counts[num] += self.weights['markov2']
        
        # 选择得票最高的号码
        sorted_votes = sorted(vote_counts.items(), key=lambda x: x[1], reverse=True)
        ensemble_kills = [num for num, votes in sorted_votes[:target_count]]
        
        # 如果不够，从各模型中补充
        if len(ensemble_kills) < target_count:
            all_kills = set(bayesian_kills + markov1_kills + markov2_kills)
            for num in all_kills:
                if num not in ensemble_kills:
                    ensemble_kills.append(num)
                    if len(ensemble_kills) >= target_count:
                        break
        
        return ensemble_kills[:target_count]

class BlueBallAnalysisSystem:
    def __init__(self):
        self.data = None
        self.ensemble_system = None
        self.test_periods = 30
        self.kill_count_range = range(1, 11)  # 测试1-10个蓝球杀号
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            data_files = ['双色球历史数据.csv', 'dlt_data.csv', 'data.csv']
            
            for file_name in data_files:
                try:
                    self.data = pd.read_csv(file_name)
                    print(f"✅ 成功加载数据文件 {file_name}: {len(self.data)} 期")
                    return True
                except FileNotFoundError:
                    continue
            
            print(f"❌ 未找到数据文件: {data_files}")
            return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def initialize_system(self):
        """初始化蓝球分析系统"""
        print("🔧 初始化蓝球分析系统...")
        self.ensemble_system = BlueBallEnsembleSystem(self.data)
        print("✅ 蓝球系统初始化完成")

    def test_blue_ball_kill_counts(self) -> Dict:
        """测试不同蓝球杀号数量的成功率"""
        print(f"\n🔍 测试不同蓝球杀号数量的成功率变化...")
        print("=" * 80)

        results = {}

        for kill_count in self.kill_count_range:
            print(f"\n测试蓝球杀号数量: {kill_count}个")

            stats = self._test_specific_blue_kill_count(kill_count)
            results[kill_count] = stats

            print(f"  全中率: {stats['perfect_rate']:.1%} ({stats['perfect_periods']}/{stats['total_periods']})")
            print(f"  杀号成功率: {stats['kill_success_rate']:.1%} ({stats['successful_kills']}/{stats['total_kills']})")
            print(f"  平均杀号数: {stats['avg_kills']:.1f}")

        return results

    def _test_specific_blue_kill_count(self, target_kill_count: int) -> Dict:
        """测试特定蓝球杀号数量的表现"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0,
            'avg_kills': 0.0,
            'period_details': []
        }

        for i in range(self.test_periods):
            if i + 5 >= len(self.data):
                break

            # 获取历史数据
            current_period = self.data.iloc[i]
            period_data = {
                'current': current_period,
                'last': self.data.iloc[i + 1],
                'prev2': self.data.iloc[i + 2],
                'prev3': self.data.iloc[i + 3],
                'prev4': self.data.iloc[i + 4],
                'prev5': self.data.iloc[i + 5]
            }

            # 解析蓝球号码
            from test_kill_algorithm import parse_numbers
            _, current_blues = parse_numbers(current_period)
            _, period1_blues = parse_numbers(period_data['last'])
            _, period2_blues = parse_numbers(period_data['prev2'])

            # 处理蓝球数据格式
            if isinstance(current_blues, list):
                current_blue_set = set(current_blues)
            else:
                current_blue_set = {current_blues}

            if isinstance(period1_blues, list):
                prev_blue_set = set(period1_blues)
            else:
                prev_blue_set = {period1_blues}

            if isinstance(period2_blues, list):
                prev2_blue_set = set(period2_blues)
            else:
                prev2_blue_set = {period2_blues}

            # 使用集成系统预测蓝球杀号
            try:
                predicted_kills = self.ensemble_system.predict_ensemble_blue_kills(period_data, target_count=target_kill_count)

                # 过滤掉前两期出现的蓝球
                valid_kills = [k for k in predicted_kills if k not in (prev_blue_set | prev2_blue_set) and 1 <= k <= 16]

                if valid_kills:
                    stats['total_periods'] += 1
                    stats['total_kills'] += len(valid_kills)

                    # 检查杀号成功情况
                    successful_kills = sum(1 for k in valid_kills if k not in current_blue_set)
                    stats['successful_kills'] += successful_kills

                    # 检查是否全中
                    is_perfect = successful_kills == len(valid_kills)
                    if is_perfect:
                        stats['perfect_periods'] += 1

                    # 记录详情
                    stats['period_details'].append({
                        'period': current_period['期号'],
                        'kills': valid_kills,
                        'successful': successful_kills,
                        'total': len(valid_kills),
                        'perfect': is_perfect,
                        'actual_blues': list(current_blue_set)
                    })

            except Exception as e:
                continue

        # 计算统计数据
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
            stats['avg_kills'] = stats['total_kills'] / stats['total_periods']

        if stats['total_kills'] > 0:
            stats['kill_success_rate'] = stats['successful_kills'] / stats['total_kills']

        return stats

    def analyze_blue_ball_relationship(self, results: Dict):
        """分析蓝球成功率与杀号数量的关系"""
        print(f"\n📊 蓝球杀号数量与成功率关系分析")
        print("=" * 80)

        # 提取数据
        kill_counts = []
        perfect_rates = []
        kill_success_rates = []
        avg_kills = []

        for kill_count, stats in results.items():
            kill_counts.append(kill_count)
            perfect_rates.append(stats['perfect_rate'])
            kill_success_rates.append(stats['kill_success_rate'])
            avg_kills.append(stats['avg_kills'])

        # 打印详细表格
        print("蓝球杀号目标 | 实际杀号 | 全中率  | 杀号成功率 | 全中期数")
        print("-" * 65)

        for i, kill_count in enumerate(kill_counts):
            stats = results[kill_count]
            print(f"     {kill_count:2d}个      |   {avg_kills[i]:4.1f}个  | {perfect_rates[i]:6.1%} |   {kill_success_rates[i]:6.1%}   | {stats['perfect_periods']:2d}/{stats['total_periods']:2d}")

        # 分析趋势
        print(f"\n📈 蓝球趋势分析:")

        # 找出全中率的变化点
        print(f"🎯 蓝球全中率变化:")
        for i in range(1, len(perfect_rates)):
            change = perfect_rates[i] - perfect_rates[i-1]
            if abs(change) > 0.05:  # 变化超过5%
                direction = "上升" if change > 0 else "下降"
                print(f"  {kill_counts[i-1]}→{kill_counts[i]}个杀号: {direction}{abs(change):.1%}")

        # 找出最佳平衡点
        best_balance_idx = self._find_blue_best_balance_point(kill_counts, perfect_rates, avg_kills)
        if best_balance_idx is not None:
            best_count = kill_counts[best_balance_idx]
            best_rate = perfect_rates[best_balance_idx]
            best_avg = avg_kills[best_balance_idx]
            print(f"\n🏆 蓝球最佳平衡点: {best_count}个杀号目标")
            print(f"   全中率: {best_rate:.1%}")
            print(f"   实际平均杀号: {best_avg:.1f}个")

        # 蓝球特殊性分析
        self._analyze_blue_ball_specifics(kill_counts, perfect_rates, kill_success_rates)

        return {
            'kill_counts': kill_counts,
            'perfect_rates': perfect_rates,
            'kill_success_rates': kill_success_rates,
            'avg_kills': avg_kills
        }

    def _find_blue_best_balance_point(self, kill_counts: List[int], perfect_rates: List[float], avg_kills: List[float]) -> int:
        """找出蓝球最佳平衡点"""
        best_score = 0
        best_idx = None

        for i, (count, rate, avg) in enumerate(zip(kill_counts, perfect_rates, avg_kills)):
            # 蓝球评分：全中率权重70%，杀号效率权重30%
            efficiency = min(avg / 5, 1.0)  # 假设5个蓝球杀号为合理上限
            score = rate * 0.7 + efficiency * 0.3

            if score > best_score:
                best_score = score
                best_idx = i

        return best_idx

    def _analyze_blue_ball_specifics(self, kill_counts: List[int], perfect_rates: List[float], kill_success_rates: List[float]):
        """分析蓝球的特殊性"""
        print(f"\n🔬 蓝球特殊性分析:")

        # 蓝球范围分析
        print(f"📉 蓝球杀号特点:")
        print(f"  蓝球总数: 16个 (vs 红球35个)")
        print(f"  开奖数量: 通常1-2个 (vs 红球6-7个)")
        print(f"  杀号难度: 相对较高 (选择空间小)")

        # 计算理论成功率
        if kill_success_rates:
            avg_single_rate = sum(kill_success_rates) / len(kill_success_rates)
            print(f"\n💡 蓝球理论分析:")
            print(f"  估算单个蓝球杀号成功率: {avg_single_rate:.1%}")

            print(f"  理论vs实际对比:")
            for i, count in enumerate(kill_counts[:6]):  # 只显示前6个
                theoretical = avg_single_rate ** count
                actual = perfect_rates[i]
                diff = actual - theoretical
                print(f"    {count}个杀号: 理论{theoretical:.1%} vs 实际{actual:.1%} (差异{diff:+.1%})")

    def optimize_blue_weights(self):
        """优化蓝球集成模型的权重"""
        print(f"\n🔧 优化蓝球模型权重...")

        best_weights = None
        best_score = 0

        # 尝试不同的权重组合
        weight_combinations = [
            {'bayesian': 0.5, 'markov1': 0.3, 'markov2': 0.2},
            {'bayesian': 0.4, 'markov1': 0.4, 'markov2': 0.2},
            {'bayesian': 0.3, 'markov1': 0.4, 'markov2': 0.3},
            {'bayesian': 0.6, 'markov1': 0.2, 'markov2': 0.2},
            {'bayesian': 0.2, 'markov1': 0.5, 'markov2': 0.3},
        ]

        for weights in weight_combinations:
            self.ensemble_system.weights = weights
            # 测试3个蓝球杀号的表现
            stats = self._test_specific_blue_kill_count(3)

            # 综合评分：全中率权重70%，杀号成功率权重30%
            score = stats['perfect_rate'] * 0.7 + stats['kill_success_rate'] * 0.3

            print(f"  权重 {weights} -> 评分: {score:.3f} (全中率: {stats['perfect_rate']:.1%})")

            if score > best_score:
                best_score = score
                best_weights = weights

        if best_weights:
            self.ensemble_system.weights = best_weights
            print(f"✅ 蓝球最佳权重: {best_weights}")

        return best_weights

    def print_blue_final_results(self, analysis_data: Dict):
        """打印蓝球最终结果"""
        print(f"\n🏆 蓝球分析最终结果")
        print("=" * 60)

        kill_counts = analysis_data['kill_counts']
        perfect_rates = analysis_data['perfect_rates']
        kill_success_rates = analysis_data['kill_success_rates']
        avg_kills = analysis_data['avg_kills']

        # 找出最佳表现
        best_idx = perfect_rates.index(max(perfect_rates))
        best_kill_count = kill_counts[best_idx]
        best_rate = perfect_rates[best_idx]
        best_avg = avg_kills[best_idx]

        print(f"🎯 蓝球最佳表现:")
        print(f"  最佳杀号数量: {best_kill_count}个")
        print(f"  最高全中率: {best_rate:.1%}")
        print(f"  实际平均杀号: {best_avg:.1f}个")

        # 推荐策略
        print(f"\n💡 蓝球杀号策略推荐:")

        # 保守策略
        conservative_counts = [c for c, r in zip(kill_counts, perfect_rates) if r >= 0.9]
        if conservative_counts:
            print(f"  保守策略: {min(conservative_counts)}个杀号 (全中率≥90%)")

        # 平衡策略
        balanced_counts = [c for c, r in zip(kill_counts, perfect_rates) if r >= 0.8]
        if balanced_counts:
            print(f"  平衡策略: {max(balanced_counts)}个杀号 (全中率≥80%)")

        # 激进策略
        if perfect_rates:
            max_rate_idx = perfect_rates.index(max(perfect_rates))
            print(f"  激进策略: {kill_counts[max_rate_idx]}个杀号 (最高全中率{perfect_rates[max_rate_idx]:.1%})")

def main():
    """主函数"""
    print("🎯 蓝球分析系统 - 贝叶斯 + 马尔科夫链")
    print("分析蓝球杀号数量与成功率的关系")
    print("=" * 60)
    
    system = BlueBallAnalysisSystem()
    
    if not system.load_data():
        return
    
    system.initialize_system()

    # 测试不同蓝球杀号数量
    results = system.test_blue_ball_kill_counts()

    # 分析关系
    analysis_data = system.analyze_blue_ball_relationship(results)

    # 优化权重
    print(f"\n🔧 优化蓝球模型权重...")
    best_weights = system.optimize_blue_weights()

    # 使用最佳权重重新测试
    if best_weights:
        print(f"\n🎯 使用最佳权重重新测试...")
        final_results = system.test_blue_ball_kill_counts()
        final_analysis = system.analyze_blue_ball_relationship(final_results)

        # 打印最终结果
        system.print_blue_final_results(final_analysis)

    print(f"\n🎉 蓝球分析系统测试完成！")

if __name__ == "__main__":
    main()
