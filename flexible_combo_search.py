#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
灵活的组合搜索系统
寻找平均杀号5-7个、全中率>88%的实用组合
"""

import pandas as pd
import numpy as np
from typing import List, Dict, <PERSON><PERSON>
from itertools import combinations
import random

# 导入现有的算法系统
from single_output_algorithm_system import SingleOutputAlgorithmSystem

class FlexibleComboSearcher:
    def __init__(self):
        self.system = SingleOutputAlgorithmSystem()
        
    def load_data(self) -> bool:
        """加载数据"""
        return self.system.load_data()

    def search_practical_combinations(self, test_periods: int = 50) -> Dict:
        """搜索实用的组合"""
        print(f"\n🎯 搜索实用组合 (平均杀号5-7个, 全中率>88%)")
        print("=" * 80)
        
        # 测试所有算法
        all_results = self.system.test_all_algorithms(test_periods)
        
        # 筛选高质量算法（成功率>85%）
        quality_algorithms = []
        for algo_name, result in all_results.items():
            if result['success_rate'] >= 0.85 and result['total_kills'] >= test_periods * 0.5:
                quality_algorithms.append(algo_name)
        
        print(f"✅ 筛选出{len(quality_algorithms)}个高质量算法")
        
        # 搜索不同条件的组合
        search_results = {}
        
        # 条件1: 平均杀号5-7个，全中率>88%
        search_results['practical_88'] = self._search_combinations(
            quality_algorithms, test_periods, 
            min_rate=0.88, min_kills=5.0, max_kills=7.0, max_samples=100
        )
        
        # 条件2: 平均杀号4-6个，全中率>90%
        search_results['efficient_90'] = self._search_combinations(
            quality_algorithms, test_periods,
            min_rate=0.90, min_kills=4.0, max_kills=6.0, max_samples=100
        )
        
        # 条件3: 平均杀号6-8个，全中率>85%
        search_results['coverage_85'] = self._search_combinations(
            quality_algorithms, test_periods,
            min_rate=0.85, min_kills=6.0, max_kills=8.0, max_samples=100
        )
        
        return search_results

    def _search_combinations(self, algorithms: List[str], test_periods: int,
                           min_rate: float, min_kills: float, max_kills: float,
                           max_samples: int = 100) -> List[Tuple]:
        """搜索符合条件的组合"""
        found_combinations = []
        
        # 测试不同规模的组合
        for combo_size in range(4, 9):  # 4-8个算法的组合
            combo_count = 0
            
            for combo in combinations(algorithms, combo_size):
                if combo_count >= max_samples:
                    break
                    
                combo_stats = self._test_combination_detailed(combo, test_periods)
                
                if combo_stats['total_periods'] == 0:
                    continue
                    
                avg_kills = combo_stats['total_kills'] / combo_stats['total_periods']
                
                if (combo_stats['perfect_rate'] >= min_rate and 
                    min_kills <= avg_kills <= max_kills):
                    found_combinations.append((combo, combo_stats))
                
                combo_count += 1
        
        # 按全中率排序
        found_combinations.sort(key=lambda x: x[1]['perfect_rate'], reverse=True)
        return found_combinations[:10]  # 返回前10个

    def _test_combination_detailed(self, algorithms: Tuple[str], test_periods: int = 50) -> Dict:
        """详细测试组合"""
        stats = {
            'total_periods': 0,
            'perfect_periods': 0,
            'perfect_rate': 0.0,
            'total_kills': 0,
            'successful_kills': 0,
            'kill_success_rate': 0.0,
            'period_details': []
        }
        
        for i in range(test_periods):
            if i + 5 >= len(self.system.data):
                break
                
            # 获取历史数据
            current_period = self.system.data.iloc[i]
            period_data = {
                'current': current_period,
                'last': self.system.data.iloc[i + 1],
                'prev2': self.system.data.iloc[i + 2],
                'prev3': self.system.data.iloc[i + 3],
                'prev4': self.system.data.iloc[i + 4],
                'prev5': self.system.data.iloc[i + 5]
            }
            
            # 解析号码
            from test_kill_algorithm import parse_numbers
            current_red, _ = parse_numbers(current_period)
            period1_red, _ = parse_numbers(period_data['last'])
            period2_red, _ = parse_numbers(period_data['prev2'])
            
            # 收集本期所有杀号
            period_kills = set()  # 使用set去重
            
            for algo_name in algorithms:
                try:
                    kill_number = self.system.derived_algorithms[algo_name](period_data)
                    if 1 <= kill_number <= 35 and kill_number not in (period1_red + period2_red):
                        period_kills.add(kill_number)
                except Exception:
                    continue
            
            period_kills = list(period_kills)  # 转回list
            
            if period_kills:
                stats['total_periods'] += 1
                stats['total_kills'] += len(period_kills)
                
                # 检查杀号成功情况
                successful_kills = sum(1 for kill in period_kills if kill not in current_red)
                stats['successful_kills'] += successful_kills
                
                # 检查是否全中
                is_perfect = successful_kills == len(period_kills)
                if is_perfect:
                    stats['perfect_periods'] += 1
                
                # 记录期详情
                stats['period_details'].append({
                    'period': current_period['期号'],
                    'kills': period_kills,
                    'successful_kills': successful_kills,
                    'total_kills': len(period_kills),
                    'perfect': is_perfect,
                    'actual_red': current_red
                })
        
        # 计算最终统计
        if stats['total_periods'] > 0:
            stats['perfect_rate'] = stats['perfect_periods'] / stats['total_periods']
        
        if stats['total_kills'] > 0:
            stats['kill_success_rate'] = stats['successful_kills'] / stats['total_kills']
        
        return stats

    def print_search_results(self, results: Dict):
        """打印搜索结果"""
        print(f"\n📊 灵活组合搜索结果")
        print("=" * 80)
        
        categories = {
            'practical_88': '实用组合 (平均杀号5-7个, 全中率>88%)',
            'efficient_90': '高效组合 (平均杀号4-6个, 全中率>90%)',
            'coverage_85': '覆盖组合 (平均杀号6-8个, 全中率>85%)'
        }
        
        for category, combinations in results.items():
            print(f"\n🎯 {categories[category]}")
            print("-" * 60)
            
            if not combinations:
                print("❌ 未找到符合条件的组合")
                continue
            
            print(f"✅ 找到{len(combinations)}个符合条件的组合:")
            
            for i, (combo, stats) in enumerate(combinations[:5], 1):  # 显示前5个
                avg_kills = stats['total_kills'] / stats['total_periods']
                status = "🎯" if stats['perfect_rate'] >= 0.92 else "✅" if stats['perfect_rate'] >= 0.88 else "⚠️"
                
                print(f"\n{i}. 全中率:{stats['perfect_rate']:6.1%} "
                      f"杀号成功率:{stats['kill_success_rate']:6.1%} "
                      f"平均杀号:{avg_kills:.1f} "
                      f"({stats['perfect_periods']}/{stats['total_periods']}) {status}")
                
                print(f"   算法组合({len(combo)}个): {', '.join(combo[:3])}{'...' if len(combo) > 3 else ''}")
                
                # 显示失败期数
                failed_periods = [p for p in stats['period_details'] if not p['perfect']]
                if failed_periods:
                    print(f"   失败期数: {len(failed_periods)}期")
                    for detail in failed_periods[:2]:  # 只显示前2期失败
                        kills_str = ','.join(map(str, detail['kills']))
                        actual_str = ','.join(map(str, detail['actual_red']))
                        print(f"     {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                              f"成功{detail['successful_kills']}/{detail['total_kills']}")

    def find_best_overall_combination(self, results: Dict) -> Tuple:
        """找出总体最佳组合"""
        all_combinations = []
        
        for category, combinations in results.items():
            all_combinations.extend(combinations)
        
        if not all_combinations:
            return None, None
        
        # 按综合评分排序（全中率 * 0.7 + 杀号成功率 * 0.3）
        def score_combination(combo_data):
            combo, stats = combo_data
            avg_kills = stats['total_kills'] / stats['total_periods'] if stats['total_periods'] > 0 else 0
            # 综合评分：全中率权重70%，杀号成功率权重30%，杀号数量适中加分
            score = stats['perfect_rate'] * 0.7 + stats['kill_success_rate'] * 0.3
            # 杀号数量在5-7之间的组合额外加分
            if 5.0 <= avg_kills <= 7.0:
                score += 0.05
            return score
        
        all_combinations.sort(key=score_combination, reverse=True)
        
        return all_combinations[0]

def main():
    """主函数"""
    print("🎯 灵活组合搜索系统")
    print("=" * 60)
    
    # 初始化搜索器
    searcher = FlexibleComboSearcher()
    
    # 加载数据
    if not searcher.load_data():
        return
    
    print(f"✅ 成功加载数据: {len(searcher.system.data)} 期")
    
    # 设置随机种子
    random.seed(42)
    
    # 搜索实用组合
    results = searcher.search_practical_combinations(test_periods=50)
    
    # 打印结果
    searcher.print_search_results(results)
    
    # 找出最佳组合
    best_combo, best_stats = searcher.find_best_overall_combination(results)
    
    if best_combo:
        avg_kills = best_stats['total_kills'] / best_stats['total_periods']
        print(f"\n🏆 总体最佳组合推荐:")
        print("=" * 60)
        print(f"算法组合: {', '.join(best_combo)}")
        print(f"全中率: {best_stats['perfect_rate']:.1%}")
        print(f"杀号成功率: {best_stats['kill_success_rate']:.1%}")
        print(f"平均杀号数: {avg_kills:.1f}")
        print(f"测试期数: {best_stats['total_periods']}")
        
        # 显示最近几期表现
        print(f"\n最近5期表现:")
        for detail in best_stats['period_details'][:5]:
            kills_str = ','.join(map(str, detail['kills']))
            actual_str = ','.join(map(str, detail['actual_red']))
            status = "✅" if detail['perfect'] else "❌"
            print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                  f"成功{detail['successful_kills']}/{detail['total_kills']} {status}")
    
    print(f"\n🎉 灵活搜索完成！")

if __name__ == "__main__":
    main()
