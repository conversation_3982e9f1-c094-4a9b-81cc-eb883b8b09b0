"""
外部特征提取器
提取时间、季节、节假日等外部特征
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from datetime import datetime, timedelta
import calendar


class ExternalFeatureExtractor:
    """外部特征提取器"""
    
    def __init__(self):
        """初始化特征提取器"""
        # 中国法定节假日（简化版本）
        self.holidays = {
            '01-01': '元旦',
            '02-14': '情人节',
            '03-08': '妇女节',
            '04-05': '清明节',
            '05-01': '劳动节',
            '06-01': '儿童节',
            '08-15': '中秋节',  # 农历，这里用公历近似
            '10-01': '国庆节',
            '12-25': '圣诞节'
        }
        
        # 二十四节气（简化版本，使用公历近似日期）
        self.solar_terms = {
            '02-04': '立春', '02-19': '雨水', '03-06': '惊蛰', '03-21': '春分',
            '04-05': '清明', '04-20': '谷雨', '05-06': '立夏', '05-21': '小满',
            '06-06': '芒种', '06-21': '夏至', '07-07': '小暑', '07-23': '大暑',
            '08-08': '立秋', '08-23': '处暑', '09-08': '白露', '09-23': '秋分',
            '10-08': '寒露', '10-23': '霜降', '11-07': '立冬', '11-22': '小雪',
            '12-07': '大雪', '12-22': '冬至', '01-06': '小寒', '01-20': '大寒'
        }
        
        # 星座日期范围
        self.zodiac_signs = [
            ('03-21', '04-19', '白羊座'), ('04-20', '05-20', '金牛座'),
            ('05-21', '06-21', '双子座'), ('06-22', '07-22', '巨蟹座'),
            ('07-23', '08-22', '狮子座'), ('08-23', '09-22', '处女座'),
            ('09-23', '10-23', '天秤座'), ('10-24', '11-22', '天蝎座'),
            ('11-23', '12-21', '射手座'), ('12-22', '01-19', '摩羯座'),
            ('01-20', '02-18', '水瓶座'), ('02-19', '03-20', '双鱼座')
        ]
    
    def extract_time_features(self, period_num: int) -> Dict[str, float]:
        """
        提取时间特征
        
        Args:
            period_num: 期号
            
        Returns:
            Dict[str, float]: 时间特征
        """
        # 根据期号推算开奖日期（假设每周三、六开奖）
        # 这里使用简化的计算方法
        base_date = datetime(2024, 1, 1)  # 假设的基准日期
        
        # 假设每期间隔3.5天（每周两期）
        days_offset = (period_num - 24001) * 3.5  # 假设24001期是基准期
        estimated_date = base_date + timedelta(days=days_offset)
        
        features = {}
        
        # 基础时间特征
        features['year'] = estimated_date.year / 2030.0  # 归一化
        features['month'] = estimated_date.month / 12.0
        features['day'] = estimated_date.day / 31.0
        features['weekday'] = estimated_date.weekday() / 6.0  # 0-6
        features['week_of_year'] = estimated_date.isocalendar()[1] / 53.0
        features['day_of_year'] = estimated_date.timetuple().tm_yday / 366.0
        
        # 季节特征
        month = estimated_date.month
        if month in [3, 4, 5]:
            season = 0  # 春
        elif month in [6, 7, 8]:
            season = 1  # 夏
        elif month in [9, 10, 11]:
            season = 2  # 秋
        else:
            season = 3  # 冬
        
        features['season'] = season / 3.0
        
        # 季节内的进度
        season_months = [3, 6, 9, 12]
        season_start = season_months[season]
        if season == 3:  # 冬季跨年
            if month >= 12:
                season_progress = (month - 12) / 3.0
            else:
                season_progress = (month + 1) / 3.0
        else:
            season_progress = (month - season_start) / 3.0
        
        features['season_progress'] = season_progress
        
        # 月相特征（简化计算）
        # 使用天数计算月相周期
        days_since_epoch = (estimated_date - datetime(2000, 1, 1)).days
        lunar_cycle = (days_since_epoch % 29.53) / 29.53  # 月相周期约29.53天
        features['lunar_phase'] = lunar_cycle
        
        # 是否为月初、月中、月末
        if estimated_date.day <= 10:
            month_period = 0  # 月初
        elif estimated_date.day <= 20:
            month_period = 1  # 月中
        else:
            month_period = 2  # 月末
        
        features['month_period'] = month_period / 2.0
        
        # 是否为周末
        features['is_weekend'] = 1.0 if estimated_date.weekday() >= 5 else 0.0
        
        # 是否为月末
        next_month = estimated_date.replace(day=28) + timedelta(days=4)
        last_day = (next_month - timedelta(days=next_month.day)).day
        features['is_month_end'] = 1.0 if estimated_date.day >= last_day - 2 else 0.0
        
        return features
    
    def extract_holiday_features(self, period_num: int) -> Dict[str, float]:
        """
        提取节假日特征
        
        Args:
            period_num: 期号
            
        Returns:
            Dict[str, float]: 节假日特征
        """
        # 推算日期
        base_date = datetime(2024, 1, 1)
        days_offset = (period_num - 24001) * 3.5
        estimated_date = base_date + timedelta(days=days_offset)
        
        features = {}
        
        # 检查是否为节假日
        date_str = estimated_date.strftime('%m-%d')
        features['is_holiday'] = 1.0 if date_str in self.holidays else 0.0
        
        # 检查是否接近节假日（前后3天）
        is_near_holiday = False
        for i in range(-3, 4):
            check_date = estimated_date + timedelta(days=i)
            check_str = check_date.strftime('%m-%d')
            if check_str in self.holidays:
                is_near_holiday = True
                break
        
        features['near_holiday'] = 1.0 if is_near_holiday else 0.0
        
        # 检查是否为二十四节气
        features['is_solar_term'] = 1.0 if date_str in self.solar_terms else 0.0
        
        # 检查是否接近节气
        is_near_solar_term = False
        for i in range(-2, 3):
            check_date = estimated_date + timedelta(days=i)
            check_str = check_date.strftime('%m-%d')
            if check_str in self.solar_terms:
                is_near_solar_term = True
                break
        
        features['near_solar_term'] = 1.0 if is_near_solar_term else 0.0
        
        # 星座特征
        zodiac_index = self._get_zodiac_sign(estimated_date)
        features['zodiac_sign'] = zodiac_index / 11.0  # 12个星座，归一化到0-1
        
        # 特殊日期特征
        features['is_first_day_of_month'] = 1.0 if estimated_date.day == 1 else 0.0
        features['is_15th'] = 1.0 if estimated_date.day == 15 else 0.0
        features['is_last_day_of_month'] = 1.0 if self._is_last_day_of_month(estimated_date) else 0.0
        
        return features
    
    def extract_period_features(self, period_num: int) -> Dict[str, float]:
        """
        提取期号相关特征
        
        Args:
            period_num: 期号
            
        Returns:
            Dict[str, float]: 期号特征
        """
        features = {}
        
        # 期号的数字特征
        features['period_last_digit'] = (period_num % 10) / 9.0  # 期号尾数
        features['period_last_two_digits'] = (period_num % 100) / 99.0  # 期号后两位
        
        # 期号的数字根（数字根是将一个数的各位数字相加，直到得到一位数）
        digit_root = self._calculate_digit_root(period_num)
        features['period_digit_root'] = digit_root / 9.0
        
        # 期号的奇偶性
        features['period_is_odd'] = 1.0 if period_num % 2 == 1 else 0.0
        
        # 期号是否为特殊数字（如回文数、对称数等）
        period_str = str(period_num)
        features['is_palindrome'] = 1.0 if period_str == period_str[::-1] else 0.0
        
        # 期号各位数字之和
        digit_sum = sum(int(d) for d in period_str)
        features['period_digit_sum'] = digit_sum / 50.0  # 假设最大和为50
        
        # 期号是否包含重复数字
        features['has_repeated_digits'] = 1.0 if len(set(period_str)) < len(period_str) else 0.0
        
        # 年内期号（假设每年约104期）
        year_period = period_num % 104
        features['year_period'] = year_period / 103.0
        
        return features
    
    def extract_cyclical_features(self, period_num: int) -> Dict[str, float]:
        """
        提取周期性特征
        
        Args:
            period_num: 期号
            
        Returns:
            Dict[str, float]: 周期性特征
        """
        features = {}
        
        # 7期周期（一周两期的周期性）
        cycle_7 = period_num % 7
        features['cycle_7_sin'] = np.sin(2 * np.pi * cycle_7 / 7)
        features['cycle_7_cos'] = np.cos(2 * np.pi * cycle_7 / 7)
        
        # 30期周期（月度周期）
        cycle_30 = period_num % 30
        features['cycle_30_sin'] = np.sin(2 * np.pi * cycle_30 / 30)
        features['cycle_30_cos'] = np.cos(2 * np.pi * cycle_30 / 30)
        
        # 104期周期（年度周期）
        cycle_104 = period_num % 104
        features['cycle_104_sin'] = np.sin(2 * np.pi * cycle_104 / 104)
        features['cycle_104_cos'] = np.cos(2 * np.pi * cycle_104 / 104)
        
        # 自定义周期（基于观察到的模式）
        # 13期周期
        cycle_13 = period_num % 13
        features['cycle_13_sin'] = np.sin(2 * np.pi * cycle_13 / 13)
        features['cycle_13_cos'] = np.cos(2 * np.pi * cycle_13 / 13)
        
        # 21期周期
        cycle_21 = period_num % 21
        features['cycle_21_sin'] = np.sin(2 * np.pi * cycle_21 / 21)
        features['cycle_21_cos'] = np.cos(2 * np.pi * cycle_21 / 21)
        
        return features
    
    def extract_all_external_features(self, period_num: int) -> Dict[str, float]:
        """
        提取所有外部特征
        
        Args:
            period_num: 期号
            
        Returns:
            Dict[str, float]: 所有外部特征
        """
        all_features = {}
        
        # 合并所有特征
        all_features.update(self.extract_time_features(period_num))
        all_features.update(self.extract_holiday_features(period_num))
        all_features.update(self.extract_period_features(period_num))
        all_features.update(self.extract_cyclical_features(period_num))
        
        return all_features
    
    def _get_zodiac_sign(self, date: datetime) -> int:
        """获取星座索引"""
        month_day = date.strftime('%m-%d')
        
        for i, (start, end, name) in enumerate(self.zodiac_signs):
            if start <= end:  # 正常情况
                if start <= month_day <= end:
                    return i
            else:  # 跨年情况（如摩羯座）
                if month_day >= start or month_day <= end:
                    return i
        
        return 0  # 默认返回第一个星座
    
    def _is_last_day_of_month(self, date: datetime) -> bool:
        """检查是否为月末最后一天"""
        next_day = date + timedelta(days=1)
        return next_day.month != date.month
    
    def _calculate_digit_root(self, number: int) -> int:
        """计算数字根"""
        while number >= 10:
            number = sum(int(digit) for digit in str(number))
        return number
    
    def get_feature_names(self) -> List[str]:
        """获取所有特征名称"""
        # 返回一个示例期号的所有特征名称
        sample_features = self.extract_all_external_features(25001)
        return list(sample_features.keys())
    
    def extract_features_for_periods(self, period_nums: List[int]) -> np.ndarray:
        """
        为多个期号提取特征
        
        Args:
            period_nums: 期号列表
            
        Returns:
            np.ndarray: 特征矩阵
        """
        features_list = []
        
        for period_num in period_nums:
            features = self.extract_all_external_features(period_num)
            features_list.append(list(features.values()))
        
        return np.array(features_list)


def test_external_features():
    """测试外部特征提取器"""
    extractor = ExternalFeatureExtractor()
    
    print("测试外部特征提取器...")
    
    # 测试单个期号
    test_period = 25068
    features = extractor.extract_all_external_features(test_period)
    
    print(f"\n期号 {test_period} 的外部特征:")
    for name, value in features.items():
        print(f"  {name}: {value:.4f}")
    
    print(f"\n总特征数: {len(features)}")
    
    # 测试多个期号
    test_periods = [25065, 25066, 25067, 25068]
    features_matrix = extractor.extract_features_for_periods(test_periods)
    
    print(f"\n多期号特征矩阵形状: {features_matrix.shape}")
    
    # 显示特征名称
    feature_names = extractor.get_feature_names()
    print(f"\n特征名称 ({len(feature_names)} 个):")
    for i, name in enumerate(feature_names):
        print(f"  {i+1:2d}. {name}")


if __name__ == "__main__":
    test_external_features()
