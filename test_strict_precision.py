#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用严格标准测试precision_focused组合
要求：所有杀号都成功才算期成功
"""

from test_algorithm_combinations import AlgorithmCombinationTester

def main():
    """主函数"""
    print("🎯 严格标准precision_focused组合测试")
    print("要求：所有杀号都成功才算期成功")
    print("=" * 60)
    
    # 初始化测试器
    tester = AlgorithmCombinationTester()
    
    # 加载数据
    if not tester.load_data():
        return
    
    # 原始precision_focused组合
    original_precision = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'energy_kill', 'abundant_kill'
    ]
    
    # 最佳优化方案
    optimized_v5_upgraded = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'last_period_reverse',
        'symmetry_kill', 'odd_even'
    ]
    
    # 其他优化方案
    optimized_v1_mixed = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'last_period_reverse', 'symmetry_kill'
    ]
    
    optimized_v2_math = [
        'factorial_kill', 'pentagonal_kill', 'last_period_plus', 'catalan_kill',
        'prev2_period_pattern_kill', 'direction_kill', 'psychology_kill', 'span',
        'odd_even', 'modular_kill'
    ]
    
    # 测试所有组合
    combinations = {
        'original_precision': original_precision,
        'optimized_v5_upgraded': optimized_v5_upgraded,
        'optimized_v1_mixed': optimized_v1_mixed,
        'optimized_v2_math': optimized_v2_math
    }
    
    results = {}
    
    print(f"\n🔍 使用严格标准测试各组合 (最近50期)")
    print("=" * 80)
    
    for combo_name, algorithms in combinations.items():
        print(f"\n正在测试: {combo_name}")
        print(f"算法列表: {', '.join(algorithms)}")
        result = tester.test_combination(algorithms, test_periods=50)
        results[combo_name] = result
        
        # 立即显示结果
        print(f"结果: 期成功率 {result['success_rate']:.1%} ({result['successful_periods']}/{result['total_periods']}), "
              f"杀号成功率 {result['kill_success_rate']:.1%} ({result['successful_kills']}/{result['total_kills']})")
    
    # 打印详细结果对比
    print(f"\n📊 严格标准下的结果对比")
    print("=" * 80)
    
    # 按期成功率排序
    sorted_results = sorted(results.items(), key=lambda x: (x[1]['success_rate'], x[1]['kill_success_rate']), reverse=True)
    
    print("🏆 严格标准排行 (所有杀号都成功才算期成功):")
    for i, (combo_name, result) in enumerate(sorted_results, 1):
        status = "🎯" if result['success_rate'] >= 0.8 else "⚠️" if result['success_rate'] >= 0.6 else "❌"
        print(f"  {i}. {combo_name:25} 期成功率:{result['success_rate']:6.1%} "
              f"杀号成功率:{result['kill_success_rate']:6.1%} "
              f"平均杀号:{result['total_kills']/result['total_periods']:.1f} "
              f"({result['successful_periods']}/{result['total_periods']}) {status}")
    
    # 详细分析最佳方案
    best_combo_name, best_result = sorted_results[0]
    print(f"\n🎯 严格标准下最佳方案详细分析: {best_combo_name}")
    print("=" * 60)
    
    print(f"期成功率: {best_result['success_rate']:.1%} ({best_result['successful_periods']}/{best_result['total_periods']})")
    print(f"杀号成功率: {best_result['kill_success_rate']:.1%} ({best_result['successful_kills']}/{best_result['total_kills']})")
    print(f"平均每期杀号数: {best_result['total_kills']/best_result['total_periods']:.1f}")
    
    # 分析失败的期数
    failed_periods = [detail for detail in best_result['details'] if not detail['period_success']]
    if failed_periods:
        print(f"\n❌ 失败期数分析 (共{len(failed_periods)}期):")
        for detail in failed_periods[:10]:  # 只显示前10期失败
            kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
            actual_str = ','.join(map(str, detail['actual_red']))
            failed_kills = [k for k in detail['kills'] if k in detail['actual_red']]
            failed_str = ','.join(map(str, failed_kills)) if failed_kills else '无'
            print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                  f"失败杀号[{failed_str}] 成功{detail['successful_kills']}/{detail['total_kills']}")
    
    # 分析成功的期数
    successful_periods = [detail for detail in best_result['details'] if detail['period_success']]
    if successful_periods:
        print(f"\n✅ 成功期数示例 (共{len(successful_periods)}期，显示前5期):")
        for detail in successful_periods[:5]:
            kills_str = ','.join(map(str, detail['kills'])) if detail['kills'] else '无'
            actual_str = ','.join(map(str, detail['actual_red']))
            print(f"  {detail['period']}: 杀号[{kills_str}] 实际[{actual_str}] "
                  f"全中 {detail['successful_kills']}/{detail['total_kills']}")
    
    # 对比原始和最佳优化方案
    if best_combo_name != 'original_precision':
        original_result = results['original_precision']
        print(f"\n📈 与原始方案对比:")
        print(f"原始方案 (original_precision):")
        print(f"  期成功率: {original_result['success_rate']:.1%} ({original_result['successful_periods']}/{original_result['total_periods']})")
        print(f"  杀号成功率: {original_result['kill_success_rate']:.1%}")
        print(f"  平均杀号数: {original_result['total_kills']/original_result['total_periods']:.1f}")
        
        print(f"\n最佳方案 ({best_combo_name}):")
        print(f"  期成功率: {best_result['success_rate']:.1%} ({best_result['successful_periods']}/{best_result['total_periods']})")
        print(f"  杀号成功率: {best_result['kill_success_rate']:.1%}")
        print(f"  平均杀号数: {best_result['total_kills']/best_result['total_periods']:.1f}")
        
        period_improvement = best_result['success_rate'] - original_result['success_rate']
        kill_improvement = best_result['kill_success_rate'] - original_result['kill_success_rate']
        print(f"\n改进效果:")
        print(f"  期成功率改进: {period_improvement:+.1%}")
        print(f"  杀号成功率改进: {kill_improvement:+.1%}")
    
    # 总结建议
    print(f"\n💡 严格标准下的建议:")
    if best_result['success_rate'] >= 0.8:
        print("✅ 该组合在严格标准下表现优秀，建议采用")
    elif best_result['success_rate'] >= 0.6:
        print("⚠️ 该组合在严格标准下表现一般，可考虑进一步优化")
    else:
        print("❌ 该组合在严格标准下表现不佳，需要重新设计")
    
    print(f"📝 注意：严格标准要求所有杀号都成功，比宽松标准更加严格")
    print(f"🎯 实际应用中可根据风险偏好选择合适的成功标准")
    
    print(f"\n🎉 严格标准precision_focused组合测试完成！")

if __name__ == "__main__":
    main()
